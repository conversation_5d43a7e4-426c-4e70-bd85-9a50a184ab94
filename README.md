# 🚀 DevBottle.com

**DevBottle** is a growing suite of tiny, high-utility tools built for developers, designers, and content creators. Each tool is crafted to solve a very specific problem — fast, clean, and without distractions. No bloat, no ads, just helpful utilities you can rely on.

## 🧰 Available Tools

### 🧼 MarkupCleaner – HTML Sanitizer

**MarkupCleaner** strips messy HTML down to its cleanest form.

-   Removes inline attributes (`style`, `class`, `id`, `data-*`)
-   Decodes HTML entities
-   Removes unnecessary wrappers and empty elements
-   Preserves semantic structure
-   Beautifies code with clean indentation

### 🔗 SlugTool – URL Slug Generator

**SlugTool** creates perfect URL slugs from any text input.

-   Converts text to URL-friendly format
-   Customizable options (lowercase, stop words, separators)
-   Automatic number removal
-   Real-time length counter
-   Click-to-copy functionality

### 🎨 CSS Shadow Generator

**CSS Shadow Generator** helps you create and visualize CSS box shadows.

-   Visual shadow editor with real-time preview
-   Multiple shadow layers support
-   Customizable color, blur, spread, and position
-   Inset shadow option
-   One-click code copying

### � Font Scale Calculator

**Font Scale Calculator** generates harmonious type scales for your designs.

-   Multiple ratio presets (Golden Ratio, Perfect Fourth, etc.)
-   Customizable base size
-   Visual preview of all scale steps
-   Generates CSS variables
-   WCAG contrast checking

### 🎭 Color Contrast Checker

**Color Contrast Checker** ensures your color combinations meet accessibility standards.

-   WCAG 2.1 compliance checking (AA and AAA levels)
-   Real-time contrast ratio calculation
-   Visual pass/fail indicators
-   Accessible color suggestions
-   Support for text and UI components

### ⚡ Critical CSS Extractor

**Critical CSS Extractor** helps you identify and extract the CSS needed for above-the-fold content.

-   Analyzes HTML and CSS
-   Identifies critical styles
-   Generates optimized CSS output
-   Improves page load performance
-   Side-by-side diff viewer

### � SVG Optimizer

**SVG Optimizer** reduces the file size of your SVG files while preserving appearance.

-   Removes unnecessary metadata
-   Cleans up viewBox attributes
-   Optimizes path data
-   Removes comments and empty groups
-   Before/after file size comparison

## 🌐 Website Features

-   **Dark/Light Mode**: Automatic theme detection with manual toggle
-   **Responsive Design**: Works on all devices from mobile to desktop
-   **Blog**: Articles about web development and design
-   **Local Image Optimization**: Automatically optimized images for better performance
-   **Accessibility**: WCAG compliant design and functionality

## 🔧 Tech Stack

-   [Astro](https://astro.build/) - Fast, modern static site generator
-   [React](https://reactjs.org/) - UI component library
-   [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS framework
-   [Lucide Icons](https://lucide.dev/) - Beautiful open-source icons

## 📦 Additional Libraries

-   [js-beautify](https://www.npmjs.com/package/js-beautify) - Code formatting
-   [mathjs](https://mathjs.org/) - Mathematical calculations
-   [react-color](https://casesandberg.github.io/react-color/) - Color picker components
-   [sharp](https://sharp.pixelplumbing.com/) - Image processing

## 🚀 Getting Started

```bash
# Clone the repository
git clone https://github.com/nishville/DevBottle.git

# Navigate to the project directory
cd DevBottle

# Install dependencies
npm install

# Start the development server
npm run dev
```

## 📝 Contact

Have suggestions or feedback? [Contact us](https://devbottle.com/contact) with your ideas.
