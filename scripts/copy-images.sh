#!/bin/bash

# This script copies images from src/assets/images to public/images
# to ensure they're available for both Astro and React components
#
# NOTE: This script is being maintained for backward compatibility during the transition
# to Astro's native Image component. Once the transition is complete, this script
# will no longer be necessary as Astro will handle image optimization automatically.

# Create directories if they don't exist
mkdir -p public/images/blog
mkdir -p public/images/profiles

# Copy blog images
echo "Copying blog images..."
cp src/assets/images/blog/*.jpg public/images/blog/ 2>/dev/null || :
cp src/assets/images/blog/*.webp public/images/blog/ 2>/dev/null || :

# Copy profile images
echo "Copying profile images..."
cp src/assets/images/profiles/profile.jpg public/
cp src/assets/images/profiles/profile.jpg public/images/profiles/

# Copy dashboard image
echo "Copying dashboard image..."
cp src/assets/images/dashboard.webp public/ 2>/dev/null || :

echo "Images copied successfully!"
