import fs from 'fs';
import path from 'path';
import sharp from 'sharp';
import { fileURLToPath } from 'url';
import { tools } from '../src/data/tools.js';

// Get the directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const targetDir = path.join(__dirname, '../public/images');
const logoPath = path.join(__dirname, '../src/assets/images/logo-og.svg');

// Ensure the target directory exists
if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, { recursive: true });
}

// Check if the logo file exists
if (!fs.existsSync(logoPath)) {
  console.error(`Logo file not found at ${logoPath}`);
  process.exit(1);
}

// Read the logo SVG file
const logoSvg = fs.readFileSync(logoPath, 'utf8');
console.log(`✓ Using logo from ${logoPath}`);

// We'll use the full SVG as base64 in the image tag

// Colors
const primaryColor = '#3366cc'; // Blue color from the theme
const darkTextColor = '#1f2937'; // Dark text color
const lightBgColor = '#ffffff'; // White background color
const outlineColor = '#3366cc'; // Blue outline color

// Function to wrap text to multiple lines
function wrapText(text, maxCharsPerLine, maxLines) {
  if (!text) return [''];

  const words = text.split(' ');
  const lines = [];
  let currentLine = '';

  for (const word of words) {
    const testLine = currentLine ? `${currentLine} ${word}` : word;

    if (testLine.length <= maxCharsPerLine) {
      currentLine = testLine;
    } else {
      lines.push(currentLine);
      currentLine = word;

      // If we've reached the maximum number of lines, stop processing
      if (lines.length >= maxLines - 1) {
        break;
      }
    }
  }

  // Add the last line
  if (currentLine) {
    lines.push(currentLine);
  }

  // If we have more lines than maxLines, truncate and add ellipsis
  if (lines.length > maxLines) {
    lines[maxLines - 1] = lines[maxLines - 1].substring(0, maxCharsPerLine - 3) + '...';
    return lines.slice(0, maxLines);
  }

  return lines;
}

// Function to generate an OG image for a tool or page
async function generateOgImage(item) {
  try {
    console.log(`Generating OG image for ${item.name}...`);

    // OG image dimensions
    const width = 1200;
    const height = 630;

    // Create a blank canvas with the right dimensions
    const image = sharp({
      create: {
        width,
        height,
        channels: 4,
        background: lightBgColor
      }
    });

    // Special case for homepage - only show centered logo
    let svgContent;
    if (item.id === 'home') {
      svgContent = `
        <svg width="${width}" height="${height}">
          <rect width="${width}" height="${height}" fill="${lightBgColor}" />

          <!-- Blue outline -->
          <rect x="10" y="10" width="${width - 20}" height="${height - 20}" fill="none" stroke="${outlineColor}" stroke-width="6" />

          <!-- Centered DevBottle Logo (properly centered) -->
          <image x="${(width - 864) / 2}" y="${(height - 216) / 2}" width="864" height="216" href="data:image/svg+xml;base64,${Buffer.from(logoSvg).toString('base64')}" />

        </svg>
      `;
    } else {
      // Standard OG image for all other pages and tools
      // Wrap the description text to multiple lines (max 3)
      const descriptionLines = wrapText(item.shortDescription, 60, 3);

      svgContent = `
        <svg width="${width}" height="${height}">
          <rect width="${width}" height="${height}" fill="${lightBgColor}" />

          <!-- Blue outline -->
          <rect x="10" y="10" width="${width - 20}" height="${height - 20}" fill="none" stroke="${outlineColor}" stroke-width="6" />

          <!-- DevBottle Logo - Left aligned with content below (20% smaller) -->
          <image x="60" y="60" width="576" height="144" href="data:image/svg+xml;base64,${Buffer.from(logoSvg).toString('base64')}" />

          <!-- Item Name -->
          <text x="60" y="300" font-family="Arial" font-weight="bold" font-size="72" fill="${darkTextColor}">${item.name}</text>

          <!-- Item Description (multi-line) -->
          ${descriptionLines.map((line, index) =>
            `<text x="60" y="${380 + index * 50}" font-family="Arial" font-size="36" fill="${darkTextColor}">${line}</text>`
          ).join('\n')}

          <!-- Bottom URL -->
          <text x="60" y="560" font-family="Arial" font-size="28" fill="${primaryColor}">devbottle.com${item.id === 'home' ? '' : item.id === 'about' ? '/about' : item.id === 'contact' ? '/contact' : item.id === 'tools' ? '/tools' : `/tools/${item.id}`}</text>
        </svg>
      `;
    }

    // Composite the SVG onto the image
    const outputPath = path.join(targetDir, `og-${item.id}.jpg`);
    await image
      .composite([
        {
          input: Buffer.from(svgContent),
          top: 0,
          left: 0
        }
      ])
      .jpeg({ quality: 90 })
      .toFile(outputPath);

    console.log(`✓ Generated OG image for ${item.name} at ${outputPath}`);
  } catch (error) {
    console.error(`✗ Error generating OG image for ${item.name}:`, error);
  }
}

// Generate OG images for pages and tools
async function generateAllOgImages() {
  // Define the main pages
  const pages = [
    {
      id: 'home',
      name: 'DevBottle',
      shortDescription: 'Simple, useful tools for developers, designers, and content creators'
    },
    {
      id: 'about',
      name: 'About DevBottle',
      shortDescription: 'Learn about our mission to create simple, focused tools for developers'
    },
    {
      id: 'tools',
      name: 'Developer Tools',
      shortDescription: 'A collection of simple, useful tools for developers'
    },
    {
      id: 'contact',
      name: 'Contact Us',
      shortDescription: 'Get in touch with the DevBottle team'
    }
  ];

  console.log(`Generating OG images for ${pages.length} pages and ${tools.length} tools`);

  // Generate OG images for main pages
  for (const page of pages) {
    await generateOgImage(page);
  }

  // Generate OG images for each tool
  for (const tool of tools) {
    await generateOgImage(tool);
  }

  // Clean up any blog-related OG images
  const blogOgPath = path.join(targetDir, 'og-blog.jpg');
  if (fs.existsSync(blogOgPath)) {
    try {
      fs.unlinkSync(blogOgPath);
      console.log('Removed blog index OG image: og-blog.jpg');
    } catch (error) {
      console.error('Error removing blog index OG image:', error);
    }
  }

  console.log('OG image generation complete!');
}

// Run the script
generateAllOgImages().catch(err => {
  console.error('Error generating OG images:', err);
  process.exit(1);
});
