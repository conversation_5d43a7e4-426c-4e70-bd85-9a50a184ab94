{"name": "devbottle", "type": "module", "version": "0.0.1", "engines": {"node": ">=18.17.1"}, "overrides": {"yocto-spinner": {"engines": {"node": ">=18.17.1"}}, "typescript": "^5.0.0"}, "scripts": {"dev": "./scripts/copy-images.sh && astro dev", "build": "chmod +x scripts/copy-images.sh && ./scripts/copy-images.sh && node scripts/generate-og-images-consolidated.js && astro build", "preview": "astro preview", "astro": "astro", "copy-images": "./scripts/copy-images.sh", "generate-og": "node scripts/generate-og-images-consolidated.js"}, "dependencies": {"@astrojs/cloudflare": "^12.5.0", "@astrojs/node": "^9.2.1", "@astrojs/react": "^4.2.3", "@astrojs/sitemap": "^3.3.0", "@lucide/astro": "^0.501.0", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "astro": "^5.6.1", "astro-og-canvas": "^0.7.0", "astro-seo": "^0.8.4", "js-beautify": "^1.15.4", "lucide": "^0.501.0", "lucide-react": "^0.501.0", "mathjs": "^14.4.0", "open-graph-scraper": "^6.10.0", "postcss": "^8.5.3", "postcss-critical-css": "^3.0.7", "react": "^18.2.0", "react-color": "^2.19.3", "react-colorful": "^5.6.1", "react-diff-viewer-continued": "^3.4.0", "react-dom": "^18.2.0", "sharp": "^0.34.1"}, "devDependencies": {"@astrojs/tailwind": "^6.0.2", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "tailwindcss": "^3.4.17"}}