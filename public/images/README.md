# DevBottle OG Images

This directory contains Open Graph (OG) images for DevBottle pages and blog posts.

## Main Pages

- `og-home.jpg` - devbottle
- `og-about.jpg` - About Us
- `og-tools.jpg` - Developer Tools
- `og-blog.jpg` - Our Blog
- `og-contact.jpg` - Get in Touch

## Tools

- `og-slugtool.jpg` - Slug Tool
- `og-markupcleaner.jpg` - Markup Cleaner
- `og-shadowgenerator.jpg` - CSS Shadow Generator
- `og-fontscale.jpg` - Font Scale Calculator
- `og-contrastchecker.jpg` - Color Contrast Checker
- `og-criticalcss.jpg` - Critical CSS Extractor
- `og-svgoptimizer.jpg` - SVG Optimizer

## Blog Posts

Blog post OG images are stored in the `blog/` subdirectory:

- `blog/expanding-our-toolset.jpg` - Expanding Our Toolset: New Developer Tools at DevBottle
- `blog/introducing-devbottle.jpg` - Introducing DevBottle: Tools for Developers and Designers
- `blog/svg-optimizer-for-web-performance.jpg` - Optimize Your SVGs for Web Performance with Our New Tool

## Usage

These images are automatically used as Open Graph images for social media sharing.
They are referenced in the `<head>` section of each page using the `og:image` meta tag.

## Regeneration

These images are automatically generated during the build process. To manually regenerate them, run:

```
npm run generate-og
```

New tools and blog posts will automatically get OG images when they are added to the codebase.
