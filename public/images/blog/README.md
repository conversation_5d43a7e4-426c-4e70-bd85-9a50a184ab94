# Blog Images

This directory contains images for blog posts. In a real implementation, you would place actual image files (jpg, png, webp, etc.) here.

## Image Naming Convention

Images should be named according to the blog post they belong to:

- `introducing-devbottle.jpg` - For the "Introducing DevBottle" post
- `expanding-our-toolset.jpg` - For the "Expanding Our Toolset" post
- `svg-optimizer-for-web-performance.jpg` - For the "SVG Optimizer for Web Performance" post
- `using-local-images.jpg` - For the "Using Local Images" post

## Image Specifications

- **Format**: JPG, PNG, or WebP
- **Dimensions**: 1200-1600px wide
- **Aspect Ratio**: 16:9 or 3:2 recommended
- **File Size**: Under 500KB recommended

## How to Add Images

1. Place your image in this directory
2. Reference it in your blog post frontmatter:
   ```yaml
   image: "blog/your-image-name.jpg"
   ```
