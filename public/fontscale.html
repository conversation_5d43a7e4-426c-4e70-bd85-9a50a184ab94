<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Font Scale Calculator | DevBottle</title>
    <link rel="stylesheet" href="/styles/global.css">
    <script src="https://unpkg.com/react@17/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@17/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://unpkg.com/mathjs@14.4.0/lib/browser/math.js"></script>
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-100">
    <div class="container mx-auto px-4 py-8">
        <header class="mb-8">
            <h1 class="text-3xl font-bold">Font Scale Calculator | DevBottle</h1>
        </header>
        
        <main>
            <div id="app" class="max-w-4xl mx-auto px-6 py-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg">
                <div class="flex items-center mb-6">
                    <span class="text-4xl mr-4">📏</span>
                    <h1 class="text-3xl md:text-4xl font-bold">Font Scale Calculator</h1>
                </div>
                <p class="mb-8 text-gray-600 dark:text-gray-400 leading-relaxed text-lg">
                    Create harmonious typography with our font scale calculator. Choose from popular ratio presets like the golden ratio, perfect fourth, or create your own custom scale.
                </p>
                
                <div id="loading">Loading Font Scale Calculator...</div>
            </div>
        </main>
    </div>

    <script type="text/babel" data-type="module">
        // Predefined ratio presets
        const RATIO_PRESETS = {
            "Minor Second": 1.067,
            "Major Second": 1.125,
            "Minor Third": 1.2,
            "Major Third": 1.25,
            "Perfect Fourth": 1.333,
            "Augmented Fourth": 1.414,
            "Perfect Fifth": 1.5,
            "Golden Ratio": 1.618
        };

        // Font size names
        const SIZE_NAMES = [
            "text-xs", "text-sm", "text-base", "text-lg", "text-xl",
            "text-2xl", "text-3xl", "text-4xl", "text-5xl", "text-6xl",
            "text-7xl", "text-8xl", "text-9xl"
        ];

        // WCAG contrast ratios
        const WCAG_LEVELS = {
            "AA Large": 3.0,
            "AA Normal": 4.5,
            "AAA Large": 4.5,
            "AAA Normal": 7.0
        };

        function FontScaleCalculator() {
            const [baseSize, setBaseSize] = React.useState(16);
            const [unit, setUnit] = React.useState("px");
            const [ratio, setRatio] = React.useState(1.25);
            const [customRatio, setCustomRatio] = React.useState(1.25);
            const [selectedPreset, setSelectedPreset] = React.useState("Major Third");
            const [fontScale, setFontScale] = React.useState([]);
            const [bgColor, setBgColor] = React.useState("#FFFFFF");
            const [textColor, setTextColor] = React.useState("#333333");
            const [contrastRatio, setContrastRatio] = React.useState(0);
            const [outputFormat, setOutputFormat] = React.useState("css");
            const [copied, setCopied] = React.useState(false);

            // Calculate the font scale when base size or ratio changes
            React.useEffect(() => {
                try {
                    calculateFontScale();
                    calculateContrastRatio();
                } catch (error) {
                    console.error('Error calculating font scale:', error);
                }
            }, [baseSize, ratio, unit, bgColor, textColor]);

            // Calculate the font scale
            const calculateFontScale = () => {
                const scale = [];
                
                // Calculate 5 steps down and 7 steps up from the base size
                for (let i = -5; i <= 7; i++) {
                    const size = math.round(baseSize * Math.pow(ratio, i), 2);
                    const remSize = math.round(size / 16, 3); // Convert to rem
                    
                    scale.push({
                        step: i,
                        name: i === 0 ? "Base" : i < 0 ? `Small ${Math.abs(i)}` : `Heading ${i}`,
                        sizePx: size,
                        sizeRem: remSize,
                        tailwindClass: i + 5 < SIZE_NAMES.length ? SIZE_NAMES[i + 5] : "custom",
                        cssVar: `--text-${i === 0 ? 'base' : i < 0 ? `sm-${Math.abs(i)}` : `h${i}`}`
                    });
                }
                
                setFontScale(scale);
            };

            // Calculate contrast ratio between background and text colors
            const calculateContrastRatio = () => {
                // Convert hex to RGB
                const hexToRgb = (hex) => {
                    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
                    return result ? {
                        r: parseInt(result[1], 16),
                        g: parseInt(result[2], 16),
                        b: parseInt(result[3], 16)
                    } : null;
                };
                
                // Calculate relative luminance
                const luminance = (r, g, b) => {
                    const a = [r, g, b].map(v => {
                        v /= 255;
                        return v <= 0.03928 ? v / 12.92 : Math.pow((v + 0.055) / 1.055, 2.4);
                    });
                    return a[0] * 0.2126 + a[1] * 0.7152 + a[2] * 0.0722;
                };
                
                const rgb1 = hexToRgb(bgColor);
                const rgb2 = hexToRgb(textColor);
                
                if (rgb1 && rgb2) {
                    const l1 = luminance(rgb1.r, rgb1.g, rgb1.b);
                    const l2 = luminance(rgb2.r, rgb2.g, rgb2.b);
                    
                    const ratio = l1 > l2 
                        ? ((l1 + 0.05) / (l2 + 0.05))
                        : ((l2 + 0.05) / (l1 + 0.05));
                    
                    setContrastRatio(math.round(ratio, 2));
                }
            };

            // Handle preset selection
            const handlePresetChange = (e) => {
                const presetName = e.target.value;
                setSelectedPreset(presetName);
                
                if (presetName === "Custom") {
                    setRatio(customRatio);
                } else {
                    setRatio(RATIO_PRESETS[presetName]);
                    setCustomRatio(RATIO_PRESETS[presetName]);
                }
            };

            // Handle custom ratio change
            const handleCustomRatioChange = (e) => {
                const value = parseFloat(e.target.value);
                if (!isNaN(value) && value > 0) {
                    setCustomRatio(value);
                    if (selectedPreset === "Custom") {
                        setRatio(value);
                    }
                }
            };

            // Generate CSS output
            const generateCSSOutput = () => {
                let output = ":root {\n";
                
                // Add base font size
                output += `  --font-base-size: ${baseSize}${unit};\n`;
                
                // Add ratio
                output += `  --font-scale-ratio: ${ratio};\n\n`;
                
                // Add all font sizes
                fontScale.forEach(size => {
                    output += `  ${size.cssVar}: ${unit === "px" ? size.sizePx + "px" : size.sizeRem + "rem"};\n`;
                });
                
                output += "}";
                return output;
            };

            // Generate Tailwind config output
            const generateTailwindOutput = () => {
                let output = "// tailwind.config.js\nmodule.exports = {\n  theme: {\n    extend: {\n      fontSize: {";
                
                // Add all font sizes
                fontScale.forEach(size => {
                    if (size.step >= -2) { // Only include base and larger sizes
                        const sizeName = size.step === 0 ? "base" : size.step < 0 ? `sm${Math.abs(size.step)}` : `h${size.step}`;
                        output += `\n        '${sizeName}': '${unit === "px" ? size.sizePx + "px" : size.sizeRem + "rem"}',`;
                    }
                });
                
                // Remove trailing comma and close the object
                output = output.slice(0, -1);
                output += "\n      }\n    }\n  }\n}";
                
                return output;
            };

            // Generate SCSS variables output
            const generateSCSSOutput = () => {
                let output = "// Typography Scale Variables\n";
                
                // Add base font size
                output += `$font-base-size: ${baseSize}${unit};\n`;
                
                // Add ratio
                output += `$font-scale-ratio: ${ratio};\n\n`;
                
                // Add all font sizes
                fontScale.forEach(size => {
                    const varName = size.step === 0 ? "base" : size.step < 0 ? `sm-${Math.abs(size.step)}` : `h${size.step}`;
                    output += `$font-size-${varName}: ${unit === "px" ? size.sizePx + "px" : size.sizeRem + "rem"};\n`;
                });
                
                return output;
            };

            // Get the output based on selected format
            const getOutput = () => {
                switch (outputFormat) {
                    case "css":
                        return generateCSSOutput();
                    case "tailwind":
                        return generateTailwindOutput();
                    case "scss":
                        return generateSCSSOutput();
                    default:
                        return generateCSSOutput();
                }
            };

            // Copy to clipboard
            const copyToClipboard = () => {
                navigator.clipboard.writeText(getOutput()).then(() => {
                    setCopied(true);
                    setTimeout(() => setCopied(false), 2000);
                });
            };

            // Get contrast level based on WCAG guidelines
            const getContrastLevel = () => {
                if (contrastRatio >= WCAG_LEVELS["AAA Normal"]) {
                    return { level: "AAA", text: "All Text", className: "text-green-600 dark:text-green-400" };
                } else if (contrastRatio >= WCAG_LEVELS["AAA Large"]) {
                    return { level: "AAA", text: "Large Text", className: "text-green-600 dark:text-green-400" };
                } else if (contrastRatio >= WCAG_LEVELS["AA Normal"]) {
                    return { level: "AA", text: "All Text", className: "text-yellow-600 dark:text-yellow-400" };
                } else if (contrastRatio >= WCAG_LEVELS["AA Large"]) {
                    return { level: "AA", text: "Large Text", className: "text-yellow-600 dark:text-yellow-400" };
                } else {
                    return { level: "Fail", text: "All Text", className: "text-red-600 dark:text-red-400" };
                }
            };

            return (
                <div>
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                        {/* Controls Section */}
                        <div className="space-y-6">
                            <div className="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg h-full">
                                <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">Scale Settings</h2>
                                
                                {/* Base Size Control */}
                                <div className="mb-4">
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Base Size: {baseSize}{unit}
                                    </label>
                                    <div className="flex items-center">
                                        <input
                                            type="range"
                                            min="12"
                                            max="24"
                                            step="1"
                                            value={baseSize}
                                            onChange={(e) => setBaseSize(parseInt(e.target.value))}
                                            className="w-full mr-2"
                                        />
                                        <input
                                            type="number"
                                            min="8"
                                            max="36"
                                            value={baseSize}
                                            onChange={(e) => setBaseSize(parseInt(e.target.value) || 16)}
                                            className="w-16 p-1 text-center border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800"
                                        />
                                    </div>
                                </div>
                                
                                {/* Unit Selection */}
                                <div className="mb-4">
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Unit
                                    </label>
                                    <div className="flex space-x-4">
                                        <label className="inline-flex items-center">
                                            <input
                                                type="radio"
                                                value="px"
                                                checked={unit === "px"}
                                                onChange={() => setUnit("px")}
                                                className="form-radio text-primary dark:text-primary-dark"
                                            />
                                            <span className="ml-2 text-gray-700 dark:text-gray-300">px</span>
                                        </label>
                                        <label className="inline-flex items-center">
                                            <input
                                                type="radio"
                                                value="rem"
                                                checked={unit === "rem"}
                                                onChange={() => setUnit("rem")}
                                                className="form-radio text-primary dark:text-primary-dark"
                                            />
                                            <span className="ml-2 text-gray-700 dark:text-gray-300">rem</span>
                                        </label>
                                    </div>
                                </div>
                                
                                {/* Ratio Presets */}
                                <div className="mb-4">
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Scale Ratio
                                    </label>
                                    <select
                                        value={selectedPreset}
                                        onChange={handlePresetChange}
                                        className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                                    >
                                        {Object.keys(RATIO_PRESETS).map(preset => (
                                            <option key={preset} value={preset}>
                                                {preset} ({RATIO_PRESETS[preset]})
                                            </option>
                                        ))}
                                        <option value="Custom">Custom</option>
                                    </select>
                                </div>
                                
                                {/* Custom Ratio Input */}
                                {selectedPreset === "Custom" && (
                                    <div className="mb-4">
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            Custom Ratio: {customRatio}
                                        </label>
                                        <div className="flex items-center">
                                            <input
                                                type="range"
                                                min="1.05"
                                                max="2"
                                                step="0.001"
                                                value={customRatio}
                                                onChange={(e) => handleCustomRatioChange(e)}
                                                className="w-full mr-2"
                                            />
                                            <input
                                                type="number"
                                                min="1"
                                                max="3"
                                                step="0.001"
                                                value={customRatio}
                                                onChange={(e) => handleCustomRatioChange(e)}
                                                className="w-20 p-1 text-center border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800"
                                            />
                                        </div>
                                    </div>
                                )}
                                
                                {/* Color Controls */}
                                <div className="mb-4">
                                    <h3 className="text-md font-medium text-gray-800 dark:text-white mb-2">Colors & Contrast</h3>
                                    
                                    <div className="grid grid-cols-2 gap-4 mb-2">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                Background
                                            </label>
                                            <div className="flex items-center">
                                                <div
                                                    className="w-8 h-8 rounded mr-2 border border-gray-300 dark:border-gray-600"
                                                    style={{ backgroundColor: bgColor }}
                                                ></div>
                                                <input
                                                    type="text"
                                                    value={bgColor}
                                                    onChange={(e) => setBgColor(e.target.value)}
                                                    className="w-full p-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800"
                                                />
                                            </div>
                                        </div>
                                        
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                Text
                                            </label>
                                            <div className="flex items-center">
                                                <div
                                                    className="w-8 h-8 rounded mr-2 border border-gray-300 dark:border-gray-600"
                                                    style={{ backgroundColor: textColor }}
                                                ></div>
                                                <input
                                                    type="text"
                                                    value={textColor}
                                                    onChange={(e) => setTextColor(e.target.value)}
                                                    className="w-full p-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800"
                                                />
                                            </div>
                                        </div>
                                    </div>
                                    
                                    {/* Contrast Ratio Display */}
                                    <div className="mt-4 p-3 bg-gray-100 dark:bg-gray-600 rounded">
                                        <div className="flex justify-between items-center">
                                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Contrast Ratio:</span>
                                            <span className="text-sm font-bold text-gray-800 dark:text-white">{contrastRatio}:1</span>
                                        </div>
                                        
                                        <div className="flex justify-between items-center mt-1">
                                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">WCAG Level:</span>
                                            <span className={`text-sm font-bold ${getContrastLevel().className}`}>
                                                {getContrastLevel().level} ({getContrastLevel().text})
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        {/* Output Format */}
                        <div className="space-y-6">
                            <div className="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg h-full">
                                <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">Output</h2>
                                
                                <div className="mb-4">
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Format
                                    </label>
                                    <div className="flex space-x-4">
                                        <label className="inline-flex items-center">
                                            <input
                                                type="radio"
                                                value="css"
                                                checked={outputFormat === "css"}
                                                onChange={() => setOutputFormat("css")}
                                                className="form-radio text-primary dark:text-primary-dark"
                                            />
                                            <span className="ml-2 text-gray-700 dark:text-gray-300">CSS Variables</span>
                                        </label>
                                        <label className="inline-flex items-center">
                                            <input
                                                type="radio"
                                                value="tailwind"
                                                checked={outputFormat === "tailwind"}
                                                onChange={() => setOutputFormat("tailwind")}
                                                className="form-radio text-primary dark:text-primary-dark"
                                            />
                                            <span className="ml-2 text-gray-700 dark:text-gray-300">Tailwind</span>
                                        </label>
                                        <label className="inline-flex items-center">
                                            <input
                                                type="radio"
                                                value="scss"
                                                checked={outputFormat === "scss"}
                                                onChange={() => setOutputFormat("scss")}
                                                className="form-radio text-primary dark:text-primary-dark"
                                            />
                                            <span className="ml-2 text-gray-700 dark:text-gray-300">SCSS</span>
                                        </label>
                                    </div>
                                </div>
                                
                                <div className="relative">
                                    <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded text-sm font-mono text-gray-800 dark:text-gray-200 overflow-x-auto max-h-60">
                                        {getOutput()}
                                    </pre>
                                    <button
                                        className="absolute top-2 right-2 px-2 py-1 bg-primary dark:bg-primary-dark text-white text-xs rounded hover:bg-primary-dark dark:hover:bg-primary transition-colors"
                                        onClick={copyToClipboard}
                                    >
                                        {copied ? "Copied!" : "Copy"}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {/* Preview Section */}
                    <div className="w-full">
                        <div className="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg">
                            <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">Type Scale Preview</h2>
                            
                            {/* Visual Scale Chart */}
                            <div className="mb-8 overflow-hidden">
                                <div className="relative h-60">
                                    <svg className="w-full h-full" viewBox="0 0 800 240">
                                        {fontScale.map((size, index) => {
                                            if (size.step >= -2 && size.step <= 5) { // Only show a subset for the chart
                                                const x = 20;
                                                const y = 220 - (size.step + 2) * 30;
                                                const width = Math.min(700, size.sizePx * 6);
                                                
                                                return (
                                                    <g key={index}>
                                                        <rect
                                                            x={x}
                                                            y={y}
                                                            width={width}
                                                            height={24}
                                                            fill={size.step === 0 ? "#4F46E5" : "#9CA3AF"}
                                                            opacity={size.step === 0 ? 1 : 0.7}
                                                            rx={3}
                                                        />
                                                        <text
                                                            x={width + 25}
                                                            y={y + 16}
                                                            fontSize="14"
                                                            fill="currentColor"
                                                            className="text-gray-600 dark:text-gray-300"
                                                        >
                                                            {size.name}: {size.sizePx}px / {size.sizeRem}rem
                                                        </text>
                                                    </g>
                                                );
                                            }
                                            return null;
                                        })}
                                    </svg>
                                </div>
                            </div>
                            
                            {/* Text Samples */}
                            <div
                                className="p-8 rounded-lg overflow-y-auto max-h-[400px] border border-gray-200 dark:border-gray-600"
                                style={{ backgroundColor: bgColor, color: textColor }}
                            >
                                <div className="space-y-6">
                                    {fontScale.map((size, index) => {
                                        if (size.step >= -2) { // Only show base and larger sizes
                                            return (
                                                <div key={index} className="border-b border-gray-200 dark:border-gray-700 pb-5 last:border-0">
                                                    <div className="flex justify-between items-center mb-2 text-xs opacity-70">
                                                        <span className="font-semibold">{size.name}</span>
                                                        <span className="bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded text-xs">{size.sizePx}px / {size.sizeRem}rem</span>
                                                    </div>
                                                    <div style={{ fontSize: `${size.sizePx}px`, lineHeight: 1.2 }}>
                                                        {size.step === 0 && "This is the base text size for body copy."}
                                                        {size.step === 1 && "Large text for emphasis and important content."}
                                                        {size.step === 2 && "Subheading level for content sections."}
                                                        {size.step === 3 && "Section heading for major content divisions."}
                                                        {size.step === 4 && "Article title or important headings."}
                                                        {size.step === 5 && "Page heading for main content areas."}
                                                        {size.step === 6 && "Hero title for featured content."}
                                                        {size.step === 7 && "Display text for maximum impact."}
                                                        {size.step === -1 && "Slightly smaller text for secondary content."}
                                                        {size.step === -2 && "Small text for captions, footnotes, and legal text."}
                                                    </div>
                                                </div>
                                            );
                                        }
                                        return null;
                                    })}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        // Render the component
        ReactDOM.render(
            <FontScaleCalculator />,
            document.getElementById('app')
        );

        // Hide loading message
        document.getElementById('loading').style.display = 'none';
    </script>
</body>
</html>
