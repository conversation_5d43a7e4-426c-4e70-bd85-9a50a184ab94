#!/bin/bash

# This script updates the SEO content containers in all tool pages to use a consistent class

# Find all tool pages (excluding index.astro)
TOOL_PAGES=$(find src/pages/tools -name "*.astro" -not -name "index.astro")

# Loop through each tool page
for page in $TOOL_PAGES; do
  echo "Updating $page..."
  
  # Replace the container div with one that uses a consistent class
  sed -i '' 's/<div class="container mx-auto px-4 py-12 w-full lg:w-8\/12 lg:mx-auto" style="width: var(--tool-container-width, auto);">/<div class="container mx-auto px-4 py-12 seo-content">/g' "$page"
done

echo "All tool pages updated successfully!"
