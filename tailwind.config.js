/** @type {import('tailwindcss').Config} */
export default {
  content: ['./src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}'],
  darkMode: 'class', // Enable class-based dark mode
  theme: {
    extend: {
      typography: (theme) => ({
        DEFAULT: {
          css: {
            color: theme('colors.gray.700'),
            maxWidth: '65ch',
            h2: {
              fontWeight: '700',
              borderBottomWidth: '1px',
              borderColor: theme('colors.gray.200'),
              paddingBottom: theme('spacing.2'),
              marginTop: theme('spacing.16'),
              marginBottom: theme('spacing.6'),
            },
            h3: {
              fontWeight: '700',
              marginTop: theme('spacing.12'),
              marginBottom: theme('spacing.4'),
            },
            h4: {
              fontWeight: '600',
              marginTop: theme('spacing.10'),
              marginBottom: theme('spacing.4'),
            },
            a: {
              color: theme('colors.primary'),
              textDecoration: 'none',
              borderBottomWidth: '2px',
              borderColor: 'rgba(79, 70, 229, 0.3)',
              '&:hover': {
                borderColor: theme('colors.primary'),
              },
            },
            blockquote: {
              fontStyle: 'italic',
              borderLeftColor: theme('colors.primary'),
              borderLeftWidth: '4px',
              marginTop: theme('spacing.10'),
              marginBottom: theme('spacing.10'),
              paddingLeft: theme('spacing.6'),
              paddingTop: theme('spacing.6'),
              paddingBottom: theme('spacing.6'),
              backgroundColor: theme('colors.gray.50'),
              borderTopRightRadius: theme('borderRadius.lg'),
              borderBottomRightRadius: theme('borderRadius.lg'),
              boxShadow: theme('boxShadow.md'),
            },
            'blockquote p:first-of-type::before': {
              content: 'none',
            },
            'blockquote p:last-of-type::after': {
              content: 'none',
            },
            code: {
              fontWeight: '400',
              backgroundColor: theme('colors.gray.100'),
              padding: theme('spacing.1'),
              borderRadius: theme('borderRadius.DEFAULT'),
              fontSize: '0.875em',
            },
            'code::before': {
              content: 'none',
            },
            'code::after': {
              content: 'none',
            },
            pre: {
              backgroundColor: theme('colors.gray.900'),
              color: theme('colors.gray.200'),
              borderRadius: theme('borderRadius.lg'),
              marginTop: theme('spacing.6'),
              marginBottom: theme('spacing.6'),
              padding: theme('spacing.4'),
              boxShadow: theme('boxShadow.md'),
            },
            'pre code': {
              backgroundColor: 'transparent',
              padding: '0',
              fontSize: '0.875em',
            },
            table: {
              fontSize: theme('fontSize.base')[0],
              lineHeight: theme('lineHeight.normal'),
              marginTop: theme('spacing.10'),
              marginBottom: theme('spacing.10'),
              borderRadius: theme('borderRadius.lg'),
              overflow: 'hidden',
              boxShadow: theme('boxShadow.md'),
            },
            thead: {
              backgroundColor: theme('colors.gray.100'),
              borderBottomWidth: '2px',
              borderBottomColor: theme('colors.gray.200'),
            },
            'thead th': {
              fontWeight: '600',
              paddingTop: theme('spacing.4'),
              paddingBottom: theme('spacing.4'),
              paddingLeft: theme('spacing.6'),
              paddingRight: theme('spacing.6'),
            },
            'tbody tr': {
              borderBottomWidth: '1px',
              borderBottomColor: theme('colors.gray.200'),
              '&:hover': {
                backgroundColor: theme('colors.gray.50'),
              },
            },
            'tbody td': {
              paddingTop: theme('spacing.4'),
              paddingBottom: theme('spacing.4'),
              paddingLeft: theme('spacing.6'),
              paddingRight: theme('spacing.6'),
            },
            'ul > li::before': {
              backgroundColor: theme('colors.primary'),
              width: '0.375em',
              height: '0.375em',
              top: 'calc(0.875em - 0.1875em)',
              borderRadius: '100%',
            },
            'ol > li::before': {
              fontWeight: '600',
              color: theme('colors.primary'),
            },
            hr: {
              marginTop: theme('spacing.12'),
              marginBottom: theme('spacing.12'),
              borderColor: theme('colors.gray.200'),
            },
            img: {
              marginTop: theme('spacing.8'),
              marginBottom: theme('spacing.8'),
              borderRadius: theme('borderRadius.lg'),
              boxShadow: theme('boxShadow.md'),
            },
          },
        },
        dark: {
          css: {
            color: theme('colors.gray.300'),
            h2: {
              color: theme('colors.white'),
              borderColor: theme('colors.gray.700'),
            },
            h3: {
              color: theme('colors.white'),
            },
            h4: {
              color: theme('colors.white'),
            },
            a: {
              color: theme('colors.primary-dark'),
              borderColor: 'rgba(99, 102, 241, 0.3)',
              '&:hover': {
                borderColor: theme('colors.primary-dark'),
              },
            },
            blockquote: {
              borderLeftColor: theme('colors.primary-dark'),
              backgroundColor: theme('colors.gray.800'),
            },
            code: {
              backgroundColor: theme('colors.gray.800'),
            },
            pre: {
              backgroundColor: 'rgb(0 0 0)',
              color: theme('colors.gray.300'),
            },
            thead: {
              backgroundColor: theme('colors.gray.800'),
              borderBottomColor: theme('colors.gray.700'),
            },
            'tbody tr': {
              borderBottomColor: theme('colors.gray.700'),
              '&:hover': {
                backgroundColor: 'rgba(55, 65, 81, 0.5)',
              },
            },
            'ul > li::before': {
              backgroundColor: theme('colors.primary-dark'),
            },
            'ol > li::before': {
              color: theme('colors.primary-dark'),
            },
            hr: {
              borderColor: theme('colors.gray.700'),
            },
          },
        },
      }),
      colors: {
        body: '#ffffff',
        'body-dark': '#1f2937',
        primary: '#3366cc', // blue (from the new logo)
        'primary-dark': '#4477dd', // lighter blue for dark mode
        secondary: '#0055aa', // darker blue
        'secondary-dark': '#5588ee', // lighter blue for dark mode
        'text-primary': '#111827', // gray-900
        'text-primary-dark': '#f9fafb', // gray-50
        'text-secondary': '#4b5563', // gray-600
        'text-secondary-dark': '#d1d5db', // gray-300
      },
      fontFamily: {
        body: ['Inter', 'sans-serif'],
        heading: ['General Sans', 'sans-serif'],
      },
      fontSize: {
        '10xl': ['10rem', { lineHeight: '1' }],
      },
      boxShadow: {
        '6xl': '0 0 50px 0 rgba(0, 0, 0, 0.1)',
        '9xl': '0 0 50px 0 rgba(0, 0, 0, 0.1)',
      },
      borderRadius: {
        'xl': '0.75rem',
        '2xl': '1rem',
      },
      spacing: {
        '18': '4.5rem',
        '22': '5.5rem',
        '30': '7.5rem',
        '34': '8.5rem',
        '38': '9.5rem',
        '42': '10.5rem',
        '50': '12.5rem',
      },
      letterSpacing: {
        'px': '1px',
        'px-n': '-1px',
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
}
