# Deployment Guide

This document explains how to deploy the DevBottle project to Cloudflare Pages.

## Prerequisites

-   A Cloudflare account
-   Access to the DevBottle GitHub repository

## Deployment Steps

1. Log in to your Cloudflare account
2. Go to Pages > Create a project
3. Connect your GitHub account and select the DevBottle repository
4. Configure the build settings:
    - Build command: `npm run build` (default for Astro projects)
    - Build output directory: `dist`
    - Node.js version: 18.17.1 (the version used by Cloudflare Pages)
5. Click "Save and Deploy"

## Environment Variables

No environment variables are required for the basic deployment.

## Troubleshooting

### Dependency Conflicts

If you encounter dependency conflicts during the build process, the `cloudflare-build` script in `package.json` uses the `--legacy-peer-deps` flag to resolve these conflicts.

### Node.js Version Issues

Some dependencies may require specific Node.js versions. The project is configured to work with Node.js 18.17.1 (the version used by Cloudflare Pages) by:

1. Using the `--ignore-engines` flag during installation
2. Setting `ignore-engines=true` in the `.npmrc` file
3. Adding Node.js version overrides in `package.json`

### Image Issues

If images are not displaying correctly:

1. Make sure the images are not ignored by Git (check `.gitignore`)
2. Ensure the `copy-images.sh` script is executable (`chmod +x scripts/copy-images.sh`)
3. Verify that the images are being copied to the correct directories

## Local Testing

To test the Cloudflare build process locally:

```bash
npm run build
```

This will run the same build process that Cloudflare Pages uses.

## Deployment Configuration

The deployment configuration is stored in the following files:

-   `cloudflare-pages.json`: Cloudflare Pages configuration
-   `package.json`: Build scripts and dependencies
-   `.npmrc`: NPM configuration
-   `.node-version` and `.nvmrc`: Node.js version specification

## Notes

-   The project uses React 18.x to ensure compatibility with all dependencies
-   The `.npmrc` file includes settings to handle dependency conflicts and Node.js version requirements
-   Images are copied from `src/assets/images/` to `public/images/` during the build process
-   The build script automatically makes the copy-images.sh script executable
