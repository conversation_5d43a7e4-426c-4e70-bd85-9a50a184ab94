# Optimized Images in DevBottle

This document explains the optimized image system in DevBottle using Astro's native Image component.

## Overview

DevBottle now uses Astro's built-in image optimization for all images across the site. This provides:

1. **Automatic Optimization**: Images are automatically optimized for better performance
2. **Format Conversion**: Images are converted to modern formats like WebP
3. **Responsive Sizing**: Multiple sizes are generated for different devices
4. **Lazy Loading**: Images are loaded only when they're about to enter the viewport
5. **Proper Dimensions**: Width and height attributes prevent layout shifts

## Components

### OptimizedImage.astro

The `OptimizedImage.astro` component is the core of our image optimization system. It:

- Handles both local and remote images
- Optimizes local images using Astro's Image component
- Falls back to standard img tags for remote images
- Supports all image formats (WebP, AVIF, PNG, JPG)
- Provides consistent styling and behavior

```astro
<OptimizedImage
  src="blog/my-image.webp"
  alt="Description"
  width={800}
  height={600}
  class="my-custom-class"
  format="webp"
  quality={80}
/>
```

### BlogImage.astro

The `BlogImage.astro` component is a wrapper around `OptimizedImage.astro` that provides:

- Default styling for blog images
- Consistent behavior across blog posts
- Simplified API for common use cases

```astro
<BlogImage
  src="blog/my-image.webp"
  alt="Description"
/>
```

### OptimizedImageReact.jsx

For React components, we provide `OptimizedImageReact.jsx` which:

- Mimics the behavior of the Astro components
- Provides error handling and fallbacks
- Maintains consistent styling and behavior

```jsx
<OptimizedImageReact
  src="blog/my-image.webp"
  alt="Description"
  className="my-custom-class"
  width={800}
  height={600}
/>
```

## Image Paths

### Local Images

Local images should be stored in the `src/assets/images/` directory:

- Blog images: `src/assets/images/blog/`
- Profile images: `src/assets/images/profiles/`
- UI images: `src/assets/images/ui/`

When referencing local images, use relative paths from the `src/assets/images/` directory:

```astro
<OptimizedImage src="blog/my-image.webp" alt="Description" />
```

### Remote Images

Remote images can be used directly with their full URL:

```astro
<OptimizedImage src="https://example.com/image.jpg" alt="Description" />
```

## Best Practices

1. **Use WebP format** when possible for better compression
2. **Provide width and height** to prevent layout shifts
3. **Use descriptive alt text** for accessibility
4. **Optimize image dimensions** before adding them to the project
5. **Use appropriate image sizes** for different contexts:
   - Blog featured images: 1200-1600px wide
   - Thumbnails: 300-400px wide
   - Profile images: 200-300px wide

## Transition Notes

We are transitioning from a dual storage approach (copying images to public/) to using Astro's native image optimization. During this transition:

1. The `copy-images.sh` script is still being used for backward compatibility
2. Both approaches will work side by side
3. New components should use the optimized image components

Once the transition is complete, we will:

1. Remove the `copy-images.sh` script
2. Remove duplicate images from the public directory
3. Use only Astro's native image optimization

## Technical Details

The image optimization is configured in `astro.config.mjs`:

```javascript
image: {
  // Enable image optimization
  service: { entrypoint: 'astro/assets/services/sharp' },
  // Set reasonable defaults for image optimization
  remotePatterns: [{ protocol: 'https' }],
},
```

This configuration uses the Sharp image processing library for optimization and allows remote images from HTTPS sources.
