# SEO and Performance Optimization Guide for DevBottle

This guide outlines best practices for SEO and performance optimization for the DevBottle website.

## SEO Implementation

DevBottle now has a comprehensive SEO implementation including:

1. **Meta Tags**: Title, description, and Open Graph tags using the `astro-seo` package
2. **Structured Data**: JSON-LD structured data for rich results in search engines
3. **Canonical URLs**: Proper canonical URL tags to prevent duplicate content issues
4. **XML Sitemap**: Automatically generated sitemap.xml for better indexing
5. **Robots.txt**: Basic robots.txt file to guide search engine crawlers

### Using the MetaTags Component

For consistency across pages, use the `MetaTags` component:

```astro
---
import MetaTags from "../components/MetaTags.astro";
---

<AppLayout>
  <MetaTags
    slot="head"
    title="Page Title - DevBottle"
    description="Page description goes here."
    ogImage="/images/og-custom.jpg"
    canonicalUrl="https://devbottle.com/page-path"
    structuredDataType="website"
    structuredData={{
      // Additional structured data properties
    }}
  />

  <!-- Page content -->
</AppLayout>
```

### Structured Data

The `StructuredData` component supports multiple schema types:

-   `website`: For general pages
-   `article`: For blog posts
-   `tool`: For tool pages
-   `organization`: For about/company pages

### XML Sitemap

The sitemap is automatically generated using the official `@astrojs/sitemap` integration. It includes:

-   All main pages (home, tools, blog, about)
-   All tool pages
-   All blog posts

The sitemap is regenerated every time you build the site, so any new content will be automatically included. The integration is configured in `astro.config.mjs` with the following settings:

-   Default priority: 0.7
-   Default change frequency: weekly
-   Last modification date: current date at build time
-   Filtering: excludes the `/thanks/` page
-   Site URL: https://devbottle.com (from the `site` property in the config)

The sitemap is available at both `/sitemap-index.xml` (the default Astro sitemap URL) and `/sitemap.xml` (for backward compatibility). A redirect is in place to ensure that search engines can find the sitemap at either URL.

To customize the sitemap further, you can modify the sitemap configuration in `astro.config.mjs`.

### Robots.txt

The site includes a `robots.txt` file that provides instructions to search engine crawlers. It includes:

-   Permission for all crawlers to access the site
-   Links to both sitemap URLs
-   Disallowed paths (like `/thanks/` and `/admin/`)

The robots.txt file is implemented in two ways:

1. A static file in the `public` directory
2. A dynamic file generated by an Astro API route that ensures the sitemap URLs are always correct

This dual approach ensures that the robots.txt file is always available and up-to-date.

## Performance Optimization

### Image Optimization

1. **Use Astro's Image Component**: For automatic optimization

    ```astro
    import { Image } from "astro:assets";
    import myImage from "../assets/images/my-image.jpg";

    <Image src={myImage} alt="Description" />
    ```

2. **OG Images**: Use the OG image generator to create optimized images
    ```bash
    npm run generate-og
    ```

### CSS Optimization

1. **Critical CSS**: Consider using the Critical CSS Extractor tool for your own site
2. **Minimize CSS**: The build process already minimizes CSS

### JavaScript Optimization

1. **Client Directives**: Use appropriate client directives

    - `client:load`: For components that need to be interactive immediately
    - `client:visible`: For components that only need to be interactive when visible
    - `client:idle`: For lower-priority interactive components

2. **Code Splitting**: Astro automatically handles code splitting

### Lighthouse Metrics

Regularly test the site using Lighthouse to ensure good performance:

1. **Performance**: Aim for 90+ score
2. **Accessibility**: Aim for 90+ score
3. **Best Practices**: Aim for 95+ score
4. **SEO**: Aim for 100 score

## Monitoring and Maintenance

### Regular Checks

1. **Sitemap Updates**: The sitemap is automatically regenerated during each build using the Astro sitemap integration
2. **Broken Links**: Regularly check for broken links
3. **Schema Validation**: Validate structured data using Google's Rich Results Test

### Analytics Integration

Consider adding analytics to monitor traffic and user behavior:

```astro
<!-- Google Analytics Example -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-XXXXXXXXXX"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'G-XXXXXXXXXX');
</script>
```

## Future Improvements

1. **Internationalization**: Add hreflang tags for multiple languages
2. **Breadcrumbs**: Implement breadcrumb navigation with structured data
3. **FAQ Schema**: Add FAQ schema for appropriate content
4. **Web Vitals Monitoring**: Implement monitoring for Core Web Vitals
