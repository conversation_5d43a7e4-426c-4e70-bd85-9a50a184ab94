# Open Graph (OG) Image Generator

This document explains how to use the OG image generator for DevBottle.

## What are OG Images?

Open Graph images are the images that appear when you share a link on social media platforms like Twitter, Facebook, LinkedIn, etc. They help make your shared links more visually appealing and provide context about the content.

![Example OG Image](../public/images/og-fontscale.jpg)

The OG images for DevBottle include:

-   The DevBottle logo
-   The tool name in large text
-   A description of the tool (up to 3 lines)
-   The URL of the tool

## How to Generate OG Images

DevBottle includes a script to automatically generate OG images for all tools and main pages.

### Prerequisites

The script uses the Sharp image processing library, which is already a dependency of the project.

### Running the Generator

To generate OG images for all tools and pages:

```bash
npm run generate-og
```

This will:

1. Create a template if it doesn't exist
2. Generate OG images for all tools defined in `src/data/tools.js`
3. Generate OG images for main pages (home, tools, blog, about)
4. Save all images to the `public/images/` directory with the naming pattern `og-{id}.jpg`

### Customizing the Template

The script uses a simple template with a gradient background and text overlay. If you want to customize the template:

1. Create your own template image at `src/assets/images/templates/og-template.png`
2. The template should be 1200x630 pixels (standard OG image size)
3. Run the generator script again

### Customizing the Logo

The script creates a DevBottle logo SVG at `src/assets/images/logo-og.svg`. If you want to customize the logo:

1. Edit or replace this SVG file with your own logo
2. Make sure the dimensions and viewBox are appropriate (default is 200x50)
3. Run the generator script again

### Advanced Customization

For more advanced customization, you can modify the `scripts/generate-og-images-simple.js` file:

-   Change colors, fonts, and text positioning
-   Modify the logo or other visual elements
-   Adjust the text wrapping parameters (currently set to 60 characters per line, max 3 lines)
-   Change the URL format or other text content

## Using the Generated Images

The OG images are automatically referenced in the page metadata. For example:

```astro
<SEO
    slot="head"
    title="Tool Name - DevBottle"
    description="Tool description"
    openGraph={{
        basic: {
            title: "Tool Name - DevBottle",
            type: "website",
            image: "https://devbottle.com/images/og-toolid.jpg",
            url: "https://devbottle.com/tools/toolid",
        },
        // ...
    }}
/>
```

## Troubleshooting

If you encounter issues with the OG image generator:

1. Make sure the `public/images/` directory exists and is writable
2. Check that Sharp is properly installed (`npm install sharp`)
3. Look for error messages in the console output

For more complex issues, you can try the advanced generator script at `scripts/generate-og-images-advanced.js`, which requires additional dependencies like Satori for more sophisticated text rendering.
