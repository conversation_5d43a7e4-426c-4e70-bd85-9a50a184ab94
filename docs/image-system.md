# DevBottle Image System

This document explains how to use the image system in the DevBottle project.

## Directory Structure

Images are stored in the following locations:

-   **Local optimized images**: `src/assets/images/`

    -   Blog images: `src/assets/images/blog/`
    -   Tool images: `src/assets/images/tools/`
    -   UI images: `src/assets/images/ui/`
    -   Profile images: `src/assets/images/profiles/`

-   **Public static images**: `public/images/`
    -   Blog images: `public/images/blog/` (duplicated for React components)
    -   Blog thumbnails: `public/images/blog/thumbnails/` (200x200px WebP optimized thumbnails)
    -   Profile images: `public/profile.jpg` (for backward compatibility)
    -   Profile thumbnails: `public/images/profiles/thumbnails/` (80x80px WebP optimized thumbnails)
    -   Logos: `public/images/logos/`
    -   Favicons: `public/images/favicons/`

### Dual Storage Approach

We use a dual storage approach for blog images:

1. **Astro Components**: Use images from `src/assets/images/` for optimization
2. **React Components**: Use images from `public/images/` for direct access

This approach ensures compatibility with both Astro's build-time optimization and React's client-side rendering.

### Image Handling Approach

We use a straightforward approach to handle images across the site:

#### Full-Size Images

Full-size images are stored in two locations:

1. **Source Images**: `src/assets/images/` - Used by Astro components with optimization
2. **Public Images**: `public/images/` - Used by React components for direct access

#### Blog Images

Blog images are handled as follows:

1. **Source**: Original images are stored in `src/assets/images/blog/`
2. **Public**: Copies are stored in `public/images/blog/` for React components
3. **Usage**: Referenced in blog posts as `image: "blog/my-image.jpg"`

#### Profile Images

Profile images are handled as follows:

1. **Source**: Original images are stored in `src/assets/images/profiles/`
2. **Public**: The main profile image is also stored at `public/profile.jpg`
3. **Usage**: Referenced in blog posts as `authorImage: "profiles/profile.jpg"`

#### Benefits of This Approach

1. **Simplicity**: Direct file references without complex processing
2. **Reliability**: Images are always available at predictable paths
3. **Compatibility**: Works with both Astro and React components
4. **Performance**: Astro components still benefit from built-in optimization

## Image Components

### BlogImage Component

The `BlogImage` component (`src/components/BlogImage.astro`) is used to display images in blog posts. It automatically:

-   Detects if an image is local or remote
-   Optimizes local images using Astro's image processing
-   Converts images to WebP format
-   Applies responsive sizing
-   Adds proper styling

#### Usage in Blog Posts

In your blog post frontmatter:

```yaml
---
layout: "../../layouts/Blog.astro"
title: "My Blog Post"
image: "blog/my-image.jpg"  # Local image in src/assets/images/blog/
# or
image: "https://example.com/image.jpg"  # Remote image
---
```

The `BlogImage` component will automatically handle both local and remote images.

#### Implementation Details

The BlogImage component uses Astro's glob import to dynamically import images:

```astro
<Image
  src={import.meta.glob('../assets/images/**/*')[`../assets/images/${src}`]()}
  alt={alt}
  width={width || defaultWidth}
  height={height || defaultHeight}
  class={`w-full h-auto rounded-xl object-cover ${className || ""}`}
  format="webp"
  quality={80}
/>
```

This approach allows for:

-   Dynamic imports based on the path provided
-   Glob pattern matching to find the correct image
-   Proper optimization through Astro's Image component

### BlogPostImage Component (React)

The `BlogPostImage` component (`src/components/BlogPostImage.jsx`) is used to display images in React components, such as blog post cards on the homepage and blog listing page. It:

-   Handles both local and remote images
-   Provides loading states with placeholders
-   Includes error handling
-   Maintains consistent styling

#### Implementation Details

The BlogPostImage component uses a direct path-based approach:

```jsx
// For local images, use a direct approach
if (src.startsWith("profiles/")) {
    // For profile images
    setImageSrc(`/profile.jpg`);
} else if (src.startsWith("blog/")) {
    // For blog images
    const imageName = src.split("/").pop();
    setImageSrc(`/images/blog/${imageName}`);
} else {
    // For any other images
    setImageSrc(`/${src}`);
}
```

This approach:

-   Uses the same image paths as the Astro components
-   Maps these paths to the public directory for direct access
-   Uses the same images across the site for consistency
-   Provides a reliable experience across different environments
-   Simplifies the image handling process

## Image Optimization

Astro's built-in image optimization is enabled with the following settings:

-   **Format**: WebP (modern, efficient format)
-   **Quality**: 80% (good balance between quality and file size)
-   **Lazy Loading**: Enabled by default
-   **Responsive**: Images are responsive by default

## Best Practices

1. **Use local images when possible**: Local images can be optimized by Astro
2. **Use descriptive filenames**: e.g., `react-component-diagram.jpg` instead of `image1.jpg`
3. **Include alt text**: Always provide descriptive alt text for accessibility
4. **Appropriate dimensions**:

    - Blog featured images: 1200-1600px wide
    - Tool screenshots: 800-1200px wide
    - UI elements: As small as possible while maintaining quality

5. **File formats**:
    - Photos: JPG
    - Graphics with transparency: PNG
    - Icons and simple graphics: SVG

## Adding New Images

1. Place your image in the appropriate directory:

    - Blog post images: `src/assets/images/blog/`
    - Tool screenshots: `src/assets/images/tools/`
    - UI elements: `src/assets/images/ui/`

2. Reference the image in your content:
    - In blog frontmatter: `image: "blog/my-image.jpg"`
    - In markdown content: `![Alt text](../../assets/images/blog/my-image.jpg)`
    - In components: `import myImage from '../assets/images/ui/my-image.jpg'`

## Technical Details

The image optimization is configured in `astro.config.mjs`:

```javascript
image: {
  service: { entrypoint: 'astro/assets/services/sharp' },
  remotePatterns: [{ protocol: 'https' }],
},
```

This uses the Sharp image processing library for optimization and allows remote images from HTTPS sources.

## Troubleshooting

### Image Not Displaying

If an image is not displaying:

1. **Check the file path**: Ensure the image is in the correct location

    - Local images should be in `src/assets/images/blog/`
    - The path in frontmatter should be relative to the images directory (e.g., `blog/my-image.jpg`)

2. **Check file format**: Ensure the image is in a supported format (JPG, PNG, WebP, AVIF)

3. **Use remote images as fallback**: If you're having issues with local images, you can temporarily use a remote image URL

4. **Check for build errors**: Look for any build errors in the console

### Blank Page

If you're seeing a blank page when using a local image:

1. **Check the console for errors**: There might be an error in the image import
2. **Try a different image**: The image file might be corrupted
3. **Use a remote image temporarily**: This can help isolate if the issue is with the specific image or the image system

### Image Quality Issues

If the image quality is not as expected:

1. **Check the quality setting**: The default quality is 80%, which is a good balance between quality and file size
2. **Use a higher quality source image**: Start with a high-quality image
3. **Adjust the quality parameter**: You can adjust the quality parameter in the BlogImage component
