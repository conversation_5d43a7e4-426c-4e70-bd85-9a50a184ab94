---
// MetaTags.astro
// A reusable component for consistent meta tags across the site

import { SEO } from "astro-seo";
import StructuredData from "./StructuredData.astro";

interface Props {
  title: string;
  description: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogType?: "website" | "article" | "product";
  canonicalUrl?: string;
  structuredDataType?: "website" | "article" | "product" | "tool" | "organization";
  structuredData?: any;
  noindex?: boolean;
}

const {
  title,
  description,
  ogTitle = title,
  ogDescription = description,
  ogImage,
  ogType = "website",
  canonicalUrl = Astro.url.href,
  structuredDataType = "website",
  structuredData = {},
  noindex = false
} = Astro.props;

// Base URL for the site
const baseUrl = "https://devbottle.com";

// Construct the full canonical URL
const fullCanonicalUrl = canonicalUrl.startsWith("http") 
  ? canonicalUrl 
  : `${baseUrl}${canonicalUrl.startsWith("/") ? "" : "/"}${canonicalUrl}`;

// Construct the full OG image URL
const fullOgImage = ogImage?.startsWith("http") 
  ? ogImage 
  : `${baseUrl}${ogImage?.startsWith("/") ? "" : "/"}${ogImage || "images/og-home.jpg"}`;

// Default structured data based on the page type
const defaultStructuredData = {
  url: fullCanonicalUrl,
  title: title,
  description: description,
  ...structuredData
};
---

<SEO
  slot="head"
  title={title}
  description={description}
  canonical={fullCanonicalUrl}
  noindex={noindex}
  openGraph={{
    basic: {
      title: ogTitle,
      type: ogType,
      image: fullOgImage,
      url: fullCanonicalUrl,
    },
    optional: {
      locale: "en-US",
      siteName: "DevBottle",
      description: ogDescription,
    },
  }}
  twitter={{
    card: "summary_large_image",
    site: "@devbottle",
    title: ogTitle,
    description: ogDescription,
    image: fullOgImage,
  }}
  extend={{
    meta: [
      { name: "viewport", content: "width=device-width, initial-scale=1" },
      { name: "theme-color", content: "#3b82f6" },
      { name: "apple-mobile-web-app-capable", content: "yes" },
      { name: "apple-mobile-web-app-status-bar-style", content: "black-translucent" },
    ],
    link: [
      { rel: "icon", href: "/favicon.svg", type: "image/svg+xml" },
      { rel: "apple-touch-icon", href: "/apple-touch-icon.png" },
    ],
  }}
/>

<StructuredData
  slot="head"
  type={structuredDataType}
  data={defaultStructuredData}
/>
