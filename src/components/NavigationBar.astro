---
import <PERSON><PERSON><PERSON><PERSON> from "./ThemeToggler.jsx";
---

<nav class="flex items-start py-8 px-4">
    <ul class="flex flex-wrap p-0 m-0 mr-auto">
        <li class="opacity-80 list-none text-sm">
            <a
                href="/"
                class="px-4 py-2 rounded-lg text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white no-underline transition-colors"
                >Home</a
            >
        </li>

        <li class="opacity-80 list-none text-sm">
            <a
                href="/tools"
                class="px-4 py-2 rounded-lg text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white no-underline transition-colors"
                >Tools</a
            >
        </li>

        <li class="opacity-80 list-none text-sm">
            <a
                href="/blog"
                class="px-4 py-2 rounded-lg text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white no-underline transition-colors"
                >Blog</a
            >
        </li>
    </ul>

    <ThemeToggler client:load />
</nav>

<script>
    const { pathname } = window.location;
    const activeNavigationElement = document.querySelector(
        `nav a[href="${pathname}"]`
    );

    if (activeNavigationElement) {
        activeNavigationElement.classList.add(
            "bg-gray-900",
            "text-white",
            "dark:bg-white",
            "dark:text-gray-900"
        );
    }
</script>
