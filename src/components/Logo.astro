---
// Logo.astro
interface Props {
    className?: string;
}

const { className = "" } = Astro.props;
---

<div class={`flex items-center ${className}`}>
    <!-- New SVG Logo -->
    <svg
        id="Layer_2"
        data-name="Layer 2"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 478.69 115.32"
        width="180"
        height="45"
        aria-label="DevBottle Logo"
    >
        <g id="Layer_1-2" data-name="Layer 1">
            <g>
                <g>
                    <path
                        class="fill-gray-800 dark:fill-white"
                        d="M82.99,23.94v-14.25h7.5c.55,0,1-.45,1-1V1c0-.55-.45-1-1-1h-44.99c-.55,0-1,.45-1,1v7.69c0,.55.45,1,1,1h7.5v14.25c-16.84,6.14-28.89,22.31-28.89,41.24,0,24.2,19.69,43.88,43.88,43.88s43.88-19.69,43.88-43.88c0-18.93-12.06-35.11-28.89-41.24ZM62.7,9.69h10.6v11.94c-1.74-.21-3.51-.33-5.3-.33s-3.56.12-5.3.33v-11.94ZM68,100.07c-19.24,0-34.88-15.65-34.88-34.88,0-13.87,8.14-25.88,19.89-31.49,3.02-1.44,6.27-2.47,9.69-2.99,1.73-.26,3.5-.4,5.3-.4s3.57.14,5.3.4c3.42.52,6.67,1.55,9.69,2.99,11.75,5.62,19.89,17.62,19.89,31.49,0,19.24-15.65,34.88-34.88,34.88Z"
                    ></path>
                    <g>
                        <path
                            class="fill-gray-800 dark:fill-white"
                            d="M31.83,16.34h-7.9c-8.45,0-17.19,6.91-17.19,18.49v18.83c0,3.66-1.5,7.94-5.74,7.94-.55,0-1,.45-1,1v6.48c0,.55.45,1,1,1,4.23,0,5.74,4.28,5.74,7.94v18.96c0,11.49,8.74,18.36,17.19,18.36h7.9c.55,0,1-.45,1-1v-7.12c0-.55-.45-1-1-1h-7.9c-4.23,0-7.55-4.11-7.55-9.36v-19.6c0-5.43-1.63-9.26-4.85-11.4,3.22-2.14,4.85-5.97,4.85-11.4v-19.6c0-5.34,3.25-9.36,7.55-9.36h7.9c.55,0,1-.45,1-1v-7.12c0-.55-.45-1-1-1Z"
                        ></path>
                        <path
                            class="fill-gray-800 dark:fill-white"
                            d="M135.78,61.59c-4.23,0-5.74-4.28-5.74-7.94v-18.83c0-9.09-6.43-18.49-17.19-18.49h-7.9c-.55,0-1,.45-1,1v7.12c0,.55.45,1,1,1h7.9c4.3,0,7.55,4.02,7.55,9.36v19.6c0,5.43,1.63,9.26,4.85,11.4-3.22,2.14-4.85,5.97-4.85,11.4v19.6c0,5.25-3.32,9.36-7.55,9.36h-7.9c-.55,0-1,.45-1,1v7.12c0,.55.45,1,1,1h7.9c8.45,0,17.19-6.87,17.19-18.36v-18.96c0-3.66,1.5-7.94,5.74-7.94.55,0,1-.45,1-1v-6.48c0-.55-.45-1-1-1Z"
                        ></path>
                    </g>
                    <path
                        class="fill-primary dark:fill-primary-dark"
                        d="M100.94,64.23c0,18.19-14.75,32.94-32.94,32.94s-32.94-14.75-32.94-32.94c30.25-13.67,35.62,29.04,65.88,0Z"
                    ></path>
                </g>
                <g>
                    <g>
                        <path
                            class="fill-gray-800 dark:fill-white"
                            d="M183.93,84.47v-4.65c-2.89,3.58-6.93,5.56-11.35,5.56-9.29,0-16.3-7.01-16.3-19.27s6.93-19.35,16.3-19.35c4.34,0,8.46,1.9,11.35,5.64v-18.74h9.75v50.81h-9.75ZM183.93,59.56c-1.68-2.44-5.1-4.19-8.38-4.19-5.48,0-9.29,4.34-9.29,10.74s3.81,10.66,9.29,10.66c3.28,0,6.7-1.75,8.38-4.19v-13.03Z"
                        ></path>
                        <path
                            class="fill-gray-800 dark:fill-white"
                            d="M219.96,46.76c10.97,0,18.43,8.23,18.43,20.26v2.13h-27.35c.61,4.65,4.34,8.53,10.59,8.53,3.43,0,7.46-1.37,9.83-3.66l4.27,6.25c-3.73,3.43-9.45,5.1-15.16,5.1-11.2,0-19.65-7.54-19.65-19.35,0-10.66,7.85-19.27,19.04-19.27ZM210.89,62.61h18.13c-.23-3.5-2.67-8.15-9.06-8.15-6.02,0-8.61,4.49-9.06,8.15Z"
                        ></path>
                        <path
                            class="fill-gray-800 dark:fill-white"
                            d="M254.92,84.47l-14.78-36.79h10.36l9.6,25.59,9.6-25.59h10.44l-14.78,36.79h-10.44Z"
                        ></path>
                        <path
                            class="fill-gray-800 dark:fill-white"
                            d="M284.47,84.47v-50.81h9.67v18.74c2.89-3.73,7.01-5.64,11.35-5.64,9.37,0,16.3,7.31,16.3,19.35s-7.01,19.27-16.3,19.27c-4.42,0-8.46-1.98-11.35-5.56v4.65h-9.67ZM294.14,72.66c1.6,2.36,5.18,4.11,8.3,4.11,5.64,0,9.37-4.27,9.37-10.66s-3.73-10.74-9.37-10.74c-3.12,0-6.7,1.83-8.3,4.27v13.03Z"
                        ></path>
                        <path
                            class="fill-gray-800 dark:fill-white"
                            d="M326.74,66.03c0-10.44,7.31-19.27,19.42-19.27s19.5,8.84,19.5,19.27-7.31,19.35-19.5,19.35-19.42-8.84-19.42-19.35ZM355.61,66.03c0-5.71-3.43-10.66-9.45-10.66s-9.37,4.95-9.37,10.66,3.35,10.74,9.37,10.74,9.45-4.95,9.45-10.74Z"
                        ></path>
                        <path
                            class="fill-gray-800 dark:fill-white"
                            d="M374.72,75.33v-19.2h-6.09v-8.46h6.09v-10.05h9.75v10.05h7.46v8.46h-7.46v16.61c0,2.29,1.22,4.04,3.35,4.04,1.45,0,2.82-.53,3.35-1.07l2.06,7.31c-1.45,1.29-4.04,2.36-8.07,2.36-6.78,0-10.44-3.5-10.44-10.05Z"
                        ></path>
                        <path
                            class="fill-gray-800 dark:fill-white"
                            d="M400.47,75.33v-19.2h-6.09v-8.46h6.09v-10.05h9.75v10.05h7.46v8.46h-7.46v16.61c0,2.29,1.22,4.04,3.35,4.04,1.45,0,2.82-.53,3.35-1.07l2.06,7.31c-1.45,1.29-4.04,2.36-8.07,2.36-6.78,0-10.44-3.5-10.44-10.05Z"
                        ></path>
                        <path
                            class="fill-gray-800 dark:fill-white"
                            d="M424.3,84.47v-50.81h9.67v50.81h-9.67Z"></path>
                        <path
                            class="fill-gray-800 dark:fill-white"
                            d="M460.26,46.76c10.97,0,18.43,8.23,18.43,20.26v2.13h-27.35c.61,4.65,4.34,8.53,10.59,8.53,3.43,0,7.46-1.37,9.83-3.66l4.27,6.25c-3.73,3.43-9.45,5.1-15.16,5.1-11.2,0-19.65-7.54-19.65-19.35,0-10.66,7.85-19.27,19.04-19.27ZM451.19,62.61h18.13c-.23-3.5-2.67-8.15-9.06-8.15-6.02,0-8.61,4.49-9.06,8.15Z"
                        ></path>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</div>
