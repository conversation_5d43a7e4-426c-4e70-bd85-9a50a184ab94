---
// This component will only run on the client
---

<script is:inline>
    // Theme initialization script - runs immediately to prevent flash of wrong theme
    (function () {
        const STORAGE_KEY = "theme";
        const DARK_THEME_CLASS = "dark";
        const LIGHT_THEME = "light";
        const DARK_THEME = "dark";

        // Initialize theme based on saved preference or system preference
        function initializeTheme() {
            const rootEl = document.documentElement;
            const savedTheme = localStorage.getItem(STORAGE_KEY);

            // If we have a saved preference, use it
            if (savedTheme) {
                if (savedTheme === DARK_THEME) {
                    rootEl.classList.add(DARK_THEME_CLASS);
                } else {
                    rootEl.classList.remove(DARK_THEME_CLASS);
                }
            }
            // Otherwise, use system preference
            else if (
                window.matchMedia &&
                window.matchMedia("(prefers-color-scheme: dark)").matches
            ) {
                rootEl.classList.add(DARK_THEME_CLASS);
            }
        }

        // Run initialization immediately
        initializeTheme();
    })();
</script>
