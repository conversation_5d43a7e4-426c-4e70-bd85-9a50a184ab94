import React from "react";
import OptimizedImageReact from "./OptimizedImageReact";

export default function BlogPostImage({
    src,
    alt,
    className = "",
    width,
    height,
    href,
}) {
    const image = (
        <OptimizedImageReact
            src={src}
            alt={alt}
            className={className}
            width={width}
            height={height}
        />
    );

    // If href is provided, wrap the image in a link
    if (href) {
        return (
            <a href={href} aria-label={`Read article: ${alt}`}>
                {image}
            </a>
        );
    }

    // Otherwise, just return the image
    return image;
}
