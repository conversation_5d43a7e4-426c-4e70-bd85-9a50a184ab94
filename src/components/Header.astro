---
import <PERSON><PERSON><PERSON><PERSON> from "./ThemeToggler.jsx";
import <PERSON><PERSON> from "./ui/Button.jsx";
import Logo from "./Logo.astro";

// This will be handled by client-side JavaScript
const mobileNavOpen = false;
---

<header class="relative z-50 bg-white dark:bg-gray-800">
    <div class="container mx-auto px-4 md:px-8">
        <div class="flex items-center justify-between py-5">
            <div class="w-auto">
                <div class="flex flex-wrap items-center">
                    <div class="w-auto mr-14">
                        <a href="/" class="flex items-center">
                            <Logo />
                        </a>
                    </div>
                </div>
            </div>
            <div class="w-auto">
                <div class="flex flex-wrap items-center">
                    <div class="w-auto hidden lg:block">
                        <ul class="flex items-center mr-8">
                            <li
                                class="mr-9 font-medium hover:text-primary dark:hover:text-primary-dark transition-colors"
                            >
                                <a href="/tools">Tools</a>
                            </li>
                            <li
                                class="mr-9 font-medium hover:text-primary dark:hover:text-primary-dark transition-colors"
                            >
                                <a href="/blog">Blog</a>
                            </li>
                            <li
                                class="mr-9 font-medium hover:text-primary dark:hover:text-primary-dark transition-colors"
                            >
                                <a href="/about">About</a>
                            </li>
                            <li
                                class="mr-9 font-medium hover:text-primary dark:hover:text-primary-dark transition-colors"
                            >
                                <a href="/contact">Contact</a>
                            </li>
                            <li class="font-medium">
                                <ThemeToggler client:load />
                            </li>
                        </ul>
                    </div>
                    <div class="w-auto hidden lg:block">
                        <Button href="/tools" variant="primary" client:load>
                            Explore Tools
                        </Button>
                    </div>
                    <div class="flex items-center space-x-4 lg:hidden">
                        <div>
                            <ThemeToggler client:load />
                        </div>
                        <div>
                            <button
                                id="mobile-menu-button"
                                class="p-2 text-gray-600 dark:text-gray-300"
                            >
                                <svg
                                    class="w-6 h-6"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M4 6h16M4 12h16M4 18h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Navigation -->
    <div id="mobile-nav" class="fixed inset-0 z-50 lg:hidden hidden">
        <div
            id="mobile-nav-backdrop"
            class="fixed inset-0 bg-gray-800 bg-opacity-80"
        >
        </div>
        <nav
            class="relative z-10 px-8 py-6 w-4/6 max-w-sm h-full overflow-y-auto bg-white dark:bg-gray-800 transform transition-all duration-300"
        >
            <div class="flex flex-col h-full">
                <div class="flex items-center justify-between mb-6">
                    <a href="/" class="flex items-center">
                        <Logo />
                    </a>
                    <button
                        id="mobile-menu-close"
                        class="p-2 text-gray-600 dark:text-gray-300"
                    >
                        <svg
                            class="w-6 h-6"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="flex flex-col w-full mt-6">
                    <ul class="space-y-5">
                        <li>
                            <a
                                href="/tools"
                                class="text-lg font-medium hover:text-primary dark:hover:text-primary-dark transition-colors mobile-nav-link"
                            >
                                Tools
                            </a>
                        </li>
                        <li>
                            <a
                                href="/blog"
                                class="text-lg font-medium hover:text-primary dark:hover:text-primary-dark transition-colors mobile-nav-link"
                            >
                                Blog
                            </a>
                        </li>
                        <li>
                            <a
                                href="/about"
                                class="text-lg font-medium hover:text-primary dark:hover:text-primary-dark transition-colors mobile-nav-link"
                            >
                                About
                            </a>
                        </li>
                        <li>
                            <a
                                href="/contact"
                                class="text-lg font-medium hover:text-primary dark:hover:text-primary-dark transition-colors mobile-nav-link"
                            >
                                Contact
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="flex flex-col justify-end w-full mt-auto pb-8">
                    <Button
                        href="/tools"
                        variant="primary"
                        className="w-full mobile-nav-link"
                        client:load
                    >
                        Explore Tools
                    </Button>
                </div>
            </div>
        </nav>
    </div>
</header>

<script>
    // Client-side JavaScript for mobile menu functionality
    document.addEventListener("DOMContentLoaded", () => {
        const mobileMenuButton = document.getElementById("mobile-menu-button");
        const mobileMenuClose = document.getElementById("mobile-menu-close");
        const mobileNav = document.getElementById("mobile-nav");
        const mobileNavBackdrop = document.getElementById(
            "mobile-nav-backdrop"
        );
        const mobileNavLinks = document.querySelectorAll(".mobile-nav-link");

        const toggleMobileNav = () => {
            mobileNav.classList.toggle("hidden");
        };

        mobileMenuButton.addEventListener("click", toggleMobileNav);
        mobileMenuClose.addEventListener("click", toggleMobileNav);
        mobileNavBackdrop.addEventListener("click", toggleMobileNav);

        // Close mobile nav when a link is clicked
        mobileNavLinks.forEach((link) => {
            link.addEventListener("click", toggleMobileNav);
        });
    });
</script>
