import React, { useState } from 'react';

export default function OptimizedImageReact({
  src,
  alt,
  className = '',
  width,
  height
}) {
  const [error, setError] = useState(null);

  // Determine the image source path
  let imageSrc;
  let finalWidth = width;
  let finalHeight = height;

  // Check if the image is a remote URL
  if (src.startsWith('http')) {
    imageSrc = src;
  } else if (src === 'profile.jpg') {
    // For root profile image
    imageSrc = '/profile.jpg';
  } else if (src.startsWith('profiles/')) {
    // For profile images
    imageSrc = `/images/${src}`;

    // For small profile images, use appropriate dimensions
    if (className.includes('w-8') || className.includes('h-8') || className.includes('rounded-full')) {
      finalWidth = finalWidth || 80;
      finalHeight = finalHeight || 80;
    }
  } else if (src.startsWith('blog/')) {
    // For blog images
    imageSrc = `/images/${src}`;

    // For blog thumbnails, use appropriate dimensions
    if (className.includes('w-32') || className.includes('w-40')) {
      finalWidth = finalWidth || 320;
      finalHeight = finalHeight || 320;
    } else {
      finalWidth = finalWidth || 1200;
      finalHeight = finalHeight || 630;
    }
  } else {
    // For any other images
    imageSrc = src.startsWith('/') ? src : `/${src}`;
  }

  // If there's an error loading the image, show a placeholder
  if (error) {
    return (
      <div
        className={`bg-gray-200 dark:bg-gray-700 flex items-center justify-center ${className}`}
        style={{ width: finalWidth, height: finalHeight }}
      >
        <span className="text-gray-500 dark:text-gray-400 text-sm">Image not available</span>
      </div>
    );
  }

  // Otherwise, render the image
  return (
    <img
      className={className}
      src={imageSrc}
      alt={alt}
      width={finalWidth}
      height={finalHeight}
      loading="lazy"
      onError={() => {
        setError(new Error('Failed to load image'));
      }}
    />
  );
}
