import React from 'react';

export default function Button({ 
  children, 
  variant = 'primary', 
  size = 'md', 
  className = '', 
  href,
  ...props 
}) {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  const variantClasses = {
    primary: 'bg-primary hover:bg-primary/90 text-white focus:ring-primary/50 dark:bg-primary-dark dark:hover:bg-primary-dark/90',
    secondary: 'bg-white border border-gray-300 hover:bg-gray-50 text-text-primary focus:ring-gray-300 dark:bg-gray-800 dark:border-gray-700 dark:hover:bg-gray-700 dark:text-white',
    outline: 'border border-primary text-primary hover:bg-primary/5 focus:ring-primary/50 dark:border-primary-dark dark:text-primary-dark',
    text: 'text-primary hover:bg-primary/5 focus:ring-primary/50 dark:text-primary-dark',
  };
  
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-5 py-2.5 text-base',
    lg: 'px-6 py-3 text-lg',
  };
  
  const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;
  
  if (href) {
    return (
      <a href={href} className={classes} {...props}>
        {children}
      </a>
    );
  }
  
  return (
    <button className={classes} {...props}>
      {children}
    </button>
  );
}
