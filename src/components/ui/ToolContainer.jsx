import React from "react";

export default function ToolContainer({
    children,
    className = "",
    containerWidth = "default", // 'default', 'full', 'narrow', 'wide'
    as: Component = "div",
    ...props
}) {
    // Define container width classes based on the containerWidth prop
    let widthClasses;

    switch (containerWidth) {
        case "full":
            widthClasses = "container mx-auto px-4 md:px-8 w-full";
            break;
        case "narrow":
            widthClasses =
                "container mx-auto px-4 md:px-8 w-full lg:w-6/12 lg:mx-auto";
            break;
        case "wide":
            widthClasses =
                "container mx-auto px-4 md:px-8 w-full lg:w-10/12 lg:mx-auto";
            break;
        case "default":
        default:
            widthClasses =
                "container mx-auto px-4 md:px-8 w-full lg:w-8/12 lg:mx-auto";
            break;
    }

    const classes = `${widthClasses} ${className}`;

    return (
        <Component className={classes} {...props}>
            {children}
        </Component>
    );
}
