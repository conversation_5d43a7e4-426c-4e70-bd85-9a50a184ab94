import React from 'react';
import LucideIcon from './LucideIcon.jsx';

/**
 * A standardized header component for all tools
 * @param {Object} props - Component props
 * @param {Object} props.toolData - The tool data object from the tools.js file
 * @param {string} [props.defaultTitle=''] - Default title to show if toolData is not available
 * @param {string} [props.defaultDescription=''] - Default description to show if toolData is not available
 * @param {string} [props.defaultIcon=''] - Default icon name to use if toolData is not available
 * @returns {React.ReactElement} The rendered tool header
 */
export default function ToolHeader({
  toolData,
  defaultTitle = '',
  defaultDescription = '',
  defaultIcon = ''
}) {
  return (
    <>
      <div className="flex items-center mb-6">
        <div className="text-primary dark:text-primary-dark mr-4 w-9 h-9 flex items-center justify-center">
          {toolData ? (
            <LucideIcon name={toolData.icon} size={36} />
          ) : (
            defaultIcon ? <LucideIcon name={defaultIcon} size={36} /> : <div className="w-9 h-9"></div>
          )}
        </div>
        <h1 className="text-3xl md:text-4xl font-bold text-gray-800 dark:text-white">
          {toolData?.name || defaultTitle}
        </h1>
      </div>
      <p className="mb-8 text-gray-600 dark:text-gray-400 leading-relaxed text-lg">
        {toolData?.fullDescription || defaultDescription}
      </p>
    </>
  );
}
