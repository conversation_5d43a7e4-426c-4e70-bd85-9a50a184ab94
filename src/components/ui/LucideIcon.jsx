import React from 'react';
import * as LucideReact from 'lucide-react';

/**
 * A component that renders a Lucide icon by name
 * @param {Object} props - Component props
 * @param {string} props.name - The name of the Lucide icon to render
 * @param {number} [props.size=24] - The size of the icon
 * @param {string} [props.className=''] - Additional CSS classes
 * @param {string} [props.color='currentColor'] - The color of the icon
 * @returns {React.ReactElement} The rendered icon
 */
export default function LucideIcon({ name, size = 24, className = '', color = 'currentColor', ...props }) {
  const IconComponent = LucideReact[name];
  
  if (!IconComponent) {
    console.warn(`Icon "${name}" not found in Lucide icons`);
    return null;
  }
  
  return <IconComponent size={size} className={className} color={color} {...props} />;
}
