import React from 'react';

export default function Card({ 
  children, 
  className = '', 
  href,
  ...props 
}) {
  const baseClasses = 'bg-white dark:bg-gray-800 rounded-xl border border-gray-100 dark:border-gray-700 transition-all duration-200 hover:shadow-md p-6';
  
  const classes = `${baseClasses} ${className}`;
  
  if (href) {
    return (
      <a href={href} className={classes} {...props}>
        {children}
      </a>
    );
  }
  
  return (
    <div className={classes} {...props}>
      {children}
    </div>
  );
}
