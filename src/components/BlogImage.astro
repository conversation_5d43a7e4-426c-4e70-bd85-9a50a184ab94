---
import OptimizedImage from "./OptimizedImage.astro";

interface Props {
    src: string;
    alt: string;
    width?: number;
    height?: number;
    class?: string;
    format?: "webp" | "avif" | "png" | "jpg" | "jpeg";
    quality?: number;
}

const {
    src,
    alt,
    width,
    height,
    class: className = "w-full h-auto rounded-xl object-cover",
    format = "webp",
    quality = 80,
} = Astro.props;
---

<OptimizedImage
    src={src}
    alt={alt}
    width={width}
    height={height}
    class={className}
    format={format}
    quality={quality}
/>
