import { useState, useEffect } from "react";
import { getToolById } from "../../data/tools.js";
import ToolHeader from "../ui/ToolHeader.jsx";
import Tool<PERSON>ontainer from "../ui/ToolContainer.jsx";

// Developer-focused text generation data
const DEV_TEXT_DATA = {
    general: {
        title: "General Tech",
        words: [
            // Technical terms
            "algorithm",
            "API",
            "architecture",
            "async",
            "backend",
            "bandwidth",
            "binary",
            "bug",
            "cache",
            "cloud",
            "cluster",
            "code",
            "compiler",
            "component",
            "config",
            "database",
            "debug",
            "deploy",
            "dev",
            "distributed",
            "endpoint",
            "framework",
            "frontend",
            "function",
            "git",
            "HTTP",
            "instance",
            "integration",
            "interface",
            "JSON",
            "kernel",
            "latency",
            "library",
            "middleware",
            "module",
            "network",
            "node",
            "object",
            "package",
            "parameter",
            "parser",
            "platform",
            "plugin",
            "protocol",
            "query",
            "repository",
            "request",
            "response",
            "REST",
            "runtime",
            "schema",
            "server",
            "service",
            "socket",
            "source",
            "stack",
            "syntax",
            "system",
            "thread",
            "token",
            "UI",
            "update",
            "URL",
            "utility",
            "validation",
            "variable",
            "version",
            "virtual",
            "webhook",
            "XML",

            // Common tech phrases
            "technical debt",
            "unit test",
            "pull request",
            "code review",
            "continuous integration",
            "deployment pipeline",
            "edge case",
            "feature flag",
            "load balancing",
            "microservice",
            "minimum viable product",
            "pair programming",
            "refactoring",
            "regression test",
            "scalability",
            "sprint planning",
            "tech stack",
            "user story",
            "version control",
            "agile methodology",
            "containerization",
            "dependency injection",
            "infrastructure as code",
            "legacy system",
            "responsive design",
            "serverless architecture",
            "single sign-on",
            "test-driven development",
            "user experience",
        ],

        // Sentence starters for more natural-sounding text
        sentenceStarters: [
            "The system requires",
            "Developers should consider",
            "We need to implement",
            "The architecture supports",
            "Users expect",
            "The codebase includes",
            "Our team deployed",
            "The documentation explains",
            "The latest update introduces",
            "Engineers designed",
            "The platform enables",
            "Testing revealed",
            "The API provides",
            "Performance metrics indicate",
            "The project roadmap includes",
            "Stakeholders requested",
            "The current version contains",
            "The interface allows",
            "Security protocols require",
            "The database schema defines",
            "The network configuration supports",
            "The deployment process involves",
            "The algorithm optimizes",
            "The component handles",
            "The service manages",
            "The module exports",
            "The function returns",
            "The object contains",
            "The library implements",
            "The framework simplifies",
        ],

        // Sentence endings for more natural-sounding text
        sentenceEndings: [
            "in production environments",
            "during the development cycle",
            "for optimal performance",
            "with minimal latency",
            "across multiple platforms",
            "in the current sprint",
            "according to best practices",
            "to meet user requirements",
            "in the staging environment",
            "before the next release",
            "after code review",
            "with proper documentation",
            "using modern techniques",
            "through the API",
            "in the cloud infrastructure",
            "with automated testing",
            "for better user experience",
            "in legacy systems",
            "with backward compatibility",
            "through continuous integration",
            "in distributed systems",
            "with proper error handling",
            "for security compliance",
            "in the current architecture",
            "with minimal dependencies",
            "for scalability",
            "in containerized environments",
            "with proper logging",
            "for debugging purposes",
            "in the production pipeline",
        ],
    },

    javascript: {
        title: "JavaScript",
        words: [
            // JS-specific terms
            "async",
            "await",
            "callback",
            "closure",
            "const",
            "DOM",
            "ES6",
            "event",
            "fetch",
            "function",
            "hoisting",
            "iterator",
            "JSON",
            "let",
            "map",
            "module",
            "Node.js",
            "npm",
            "object",
            "promise",
            "prototype",
            "React",
            "reducer",
            "render",
            "scope",
            "setState",
            "spread",
            "this",
            "TypeScript",
            "undefined",
            "useEffect",
            "useState",
            "var",
            "Vue",
            "webpack",
            "yield",

            // JS variables and functions
            "addEventListener",
            "appendChild",
            "Array.from",
            "async function",
            "axios.get",
            "componentDidMount",
            "console.log",
            "createElement",
            "document.querySelector",
            "fetch()",
            "filter()",
            "forEach",
            "getElementById",
            "innerHTML",
            "JSON.parse",
            "localStorage",
            "map()",
            "Math.random",
            "new Promise",
            "Object.keys",
            "onClick",
            "preventDefault",
            "querySelector",
            "reduce()",
            "removeEventListener",
            "requestAnimationFrame",
            "setTimeout",
            "String.prototype",
            "useCallback",
            "window.onload",
        ],

        sentenceStarters: [
            "The function returns",
            "We need to refactor",
            "The component renders",
            "The event listener handles",
            "The promise resolves",
            "The module exports",
            "The hook manages",
            "The API call fetches",
            "The reducer updates",
            "The middleware processes",
            "The utility function converts",
            "The DOM element contains",
            "The event bubbles",
            "The callback executes",
            "The state includes",
            "The props contain",
            "The ref points to",
            "The context provides",
            "The effect runs",
            "The memo prevents",
            "The portal renders",
            "The fragment groups",
            "The error boundary catches",
            "The lazy component loads",
            "The suspense shows",
        ],

        sentenceEndings: [
            "when the component mounts",
            "after the state updates",
            "before the DOM renders",
            "during the event propagation",
            "when the promise resolves",
            "after the API response",
            "before the cleanup function",
            "during the render phase",
            "after the effect runs",
            "when the ref updates",
            "during the reconciliation",
            "after the component unmounts",
            "when the error occurs",
            "during the event capturing",
            "after the callback executes",
            "when the props change",
            "during the context update",
            "after the reducer dispatches",
            "when the memo rerenders",
            "during the lazy loading",
            "after the suspense resolves",
        ],
    },

    python: {
        title: "Python",
        words: [
            // Python-specific terms
            "args",
            "async",
            "class",
            "decorator",
            "def",
            "dict",
            "Django",
            "except",
            "Flask",
            "generator",
            "import",
            "iterator",
            "kwargs",
            "lambda",
            "list",
            "method",
            "NumPy",
            "pandas",
            "pip",
            "PyPI",
            "pytest",
            "self",
            "tuple",
            "type",
            "venv",
            "virtualenv",
            "yield",

            // Python functions and methods
            "__init__",
            "append()",
            "asyncio.run",
            "collections.defaultdict",
            "contextlib.contextmanager",
            "datetime.datetime",
            "enumerate()",
            "f-string",
            "filter()",
            "functools.wraps",
            "itertools.cycle",
            "json.dumps",
            "list comprehension",
            "map()",
            "numpy.array",
            "os.path.join",
            "pandas.DataFrame",
            "pathlib.Path",
            "range()",
            "re.match",
            "requests.get",
            "sorted()",
            "str.format",
            "super()",
            "with statement",
        ],

        sentenceStarters: [
            "The function defines",
            "We need to import",
            "The class inherits",
            "The method returns",
            "The decorator wraps",
            "The generator yields",
            "The context manager handles",
            "The list comprehension creates",
            "The dictionary contains",
            "The module provides",
            "The package includes",
            "The exception catches",
            "The async function awaits",
            "The type hint specifies",
            "The virtual environment isolates",
            "The test case verifies",
            "The iterator produces",
            "The lambda function transforms",
            "The tuple packs",
            "The set eliminates",
            "The bytes object represents",
            "The slice extracts",
        ],

        sentenceEndings: [
            "with proper type hints",
            "using list comprehension",
            "through the context manager",
            "with dictionary unpacking",
            "using generator expressions",
            "with proper exception handling",
            "through method chaining",
            "using async/await syntax",
            "with proper docstrings",
            "through dependency injection",
            "using metaclasses",
            "with proper logging",
            "through the decorator pattern",
            "using functional programming",
            "with proper testing",
            "through the iterator protocol",
            "using the descriptor protocol",
            "with proper documentation",
            "through the MRO",
            "using abstract base classes",
            "with proper error handling",
        ],
    },

    devops: {
        title: "DevOps & Infrastructure",
        words: [
            // DevOps terms
            "Ansible",
            "artifact",
            "AWS",
            "Azure",
            "backup",
            "CI/CD",
            "cluster",
            "configuration",
            "container",
            "deployment",
            "Docker",
            "environment",
            "GCP",
            "GitLab",
            "infrastructure",
            "Jenkins",
            "Kubernetes",
            "load balancer",
            "logging",
            "metrics",
            "microservice",
            "monitoring",
            "orchestration",
            "pipeline",
            "provisioning",
            "scaling",
            "serverless",
            "Terraform",
            "virtualization",
            "VPC",

            // DevOps concepts and tools
            "auto-scaling group",
            "blue-green deployment",
            "canary release",
            "configuration drift",
            "container registry",
            "disaster recovery",
            "ECS",
            "EKS",
            "Elastic Beanstalk",
            "environment variable",
            "Grafana",
            "Helm chart",
            "infrastructure as code",
            "ingress controller",
            "instance profile",
            "Istio",
            "Kibana",
            "Lambda function",
            "load testing",
            "Prometheus",
            "S3 bucket",
            "security group",
            "service mesh",
            "Splunk",
            "stateful set",
            "subnet",
            "systemd",
            "VPN",
            "webhook",
            "zero downtime",
        ],

        sentenceStarters: [
            "The pipeline deploys",
            "We need to configure",
            "The cluster scales",
            "The container orchestrates",
            "The infrastructure provisions",
            "The service mesh routes",
            "The monitoring system alerts",
            "The load balancer distributes",
            "The CI/CD process automates",
            "The Kubernetes cluster manages",
            "The Terraform module creates",
            "The Docker image contains",
            "The Ansible playbook configures",
            "The cloud provider offers",
            "The serverless function processes",
            "The security group allows",
            "The network policy restricts",
            "The secret manager stores",
            "The log aggregator collects",
            "The metrics dashboard displays",
            "The auto-scaling group adjusts",
            "The service discovery finds",
        ],

        sentenceEndings: [
            "in the production environment",
            "across multiple availability zones",
            "with zero downtime",
            "through the CI/CD pipeline",
            "using infrastructure as code",
            "with proper monitoring",
            "across the Kubernetes cluster",
            "in the cloud provider",
            "with proper authentication",
            "through the service mesh",
            "using container orchestration",
            "with proper logging",
            "across multiple regions",
            "using blue-green deployment",
            "with proper alerting",
            "through the load balancer",
            "using canary releases",
            "with proper backup strategies",
            "across the virtual network",
            "using configuration management",
            "with proper security controls",
        ],
    },

    loremipsum: {
        title: "Lorem Ipsum",
        words: [
            // Traditional Lorem Ipsum words
            "lorem",
            "ipsum",
            "dolor",
            "sit",
            "amet",
            "consectetur",
            "adipiscing",
            "elit",
            "sed",
            "do",
            "eiusmod",
            "tempor",
            "incididunt",
            "ut",
            "labore",
            "et",
            "dolore",
            "magna",
            "aliqua",
            "enim",
            "ad",
            "minim",
            "veniam",
            "quis",
            "nostrud",
            "exercitation",
            "ullamco",
            "laboris",
            "nisi",
            "aliquip",
            "ex",
            "ea",
            "commodo",
            "consequat",
            "duis",
            "aute",
            "irure",
            "in",
            "reprehenderit",
            "voluptate",
            "velit",
            "esse",
            "cillum",
            "eu",
            "fugiat",
            "nulla",
            "pariatur",
            "excepteur",
            "sint",
            "occaecat",
            "cupidatat",
            "non",
            "proident",
            "sunt",
            "culpa",
            "qui",
            "officia",
            "deserunt",
            "mollit",
            "anim",
            "id",
            "est",
            "laborum",
        ],

        // Latin-sounding sentence starters
        sentenceStarters: [
            "Lorem ipsum dolor sit amet",
            "Consectetur adipiscing elit",
            "Sed do eiusmod tempor",
            "Ut labore et dolore",
            "Magna aliqua enim ad",
            "Quis nostrud exercitation",
            "Ullamco laboris nisi",
            "Aliquip ex ea commodo",
            "Duis aute irure dolor",
            "In reprehenderit voluptate",
            "Velit esse cillum dolore",
            "Eu fugiat nulla pariatur",
            "Excepteur sint occaecat",
            "Cupidatat non proident",
            "Sunt in culpa qui",
            "Officia deserunt mollit",
            "Anim id est laborum",
            "Neque porro quisquam",
            "Qui dolorem ipsum quia",
            "Dolor sit amet consectetur",
        ],

        // Latin-sounding sentence endings
        sentenceEndings: [
            "consectetur adipiscing elit",
            "sed do eiusmod tempor",
            "incididunt ut labore",
            "et dolore magna aliqua",
            "enim ad minim veniam",
            "quis nostrud exercitation",
            "ullamco laboris nisi",
            "ut aliquip ex ea commodo",
            "consequat duis aute",
            "irure dolor in reprehenderit",
            "in voluptate velit esse",
            "cillum dolore eu fugiat",
            "nulla pariatur excepteur",
            "sint occaecat cupidatat",
            "non proident sunt",
            "in culpa qui officia",
            "deserunt mollit anim",
            "id est laborum et",
            "dolorum fuga et harum",
            "quidem rerum facilis",
        ],
    },

    klingon: {
        title: "Klingon (tlhIngan Hol)",
        words: [
            // Klingon vocabulary words
            "tlhIngan",
            "Hol",
            "Qapla'",
            "batlh",
            "jIH",
            "SoH",
            "ghaH",
            "chaH",
            "maH",
            "tlhIH",
            "Duj",
            "HIja'",
            "ghobe'",
            "nuqneH",
            "Qo'noS",
            "petaQ",
            "Qu'",
            "Quch",
            "yIn",
            "Hegh",
            "mara",
            "worf",
            "mogh",
            "kahless",
            "valkris",
            "gorkon",
            "gowron",
            "martok",
            "lursa",
            "b'etor",
            "toQ",
            "Soj",
            "HIq",
            "chab",
            "Ha'DIbaH",
            "targh",
            "cha",
            "wam",
            "loD",
            "be'",
            "puqloD",
            "puqbe'",
            "vav",
            "SoS",
            "qeylIS",
            "Dor",
            "Suvwl'",
            "may'",
            "veS",
            "yuQ",
            "Qav",
            "Qel",
            "Qong",
            "yIH",
            "ghop",
            "ghIch",
            "Qagh",
            "Qoj",
            "Qot",
            "Qoy",
            "Qub",
            "Qup",
            "Qeq",
            "Qev",
            "Qib",
            "Qom",
            "Qot",
            "Qoy",
            "Qub",
            "Qup",
        ],

        // Klingon sentence starters
        sentenceStarters: [
            "tlhIngan maH",
            "Qapla' noH",
            "batlh Duj",
            "jIH SoH",
            "ghaH chaH",
            "maH tlhIH",
            "Duj HIja'",
            "ghobe' nuqneH",
            "Qo'noS petaQ",
            "Qu' Quch",
            "yIn Hegh",
            "mara worf",
            "mogh kahless",
            "valkris gorkon",
            "gowron martok",
            "lursa b'etor",
            "toQ Soj",
            "HIq chab",
            "Ha'DIbaH targh",
            "cha wam",
        ],

        // Klingon sentence endings
        sentenceEndings: [
            "Qapla' batlh je",
            "Heghlu'meH QaQ jajvam",
            "bIlujDI' yIchegh",
            "Hab SoSlI' Quch",
            "tlhIngan maH taHjaj",
            "batlh bIHeghjaj",
            "Suvlu'taHvIS yapbe' HoS neH",
            "tIqwIj Sa'angnIS",
            "bIjatlh 'e' yImev",
            "yISaHQo' 'ej yIHoHQo'",
            "Hoch nuH qel",
            "HIja' Qapla'",
            "maj ram maSuvtaHvIS",
            "DaH Qapla'",
            "bortaS bIr jablu'DI'",
            "reH bang largh Qav'ap",
            "Duj ngaDmoH",
            "Qapla' noH neH",
            "batlh Dujvo'",
            "jIH SoHvo'",
        ],
    },
};

// Sample text for each category
const SAMPLE_TEXTS = {
    general:
        "The system requires proper authentication protocols for secure API access in production environments. Developers should consider implementing caching mechanisms to optimize query performance with minimal latency. We need to implement comprehensive logging for all microservices across multiple platforms. The architecture supports horizontal scaling to handle increased traffic during peak hours according to best practices.",

    javascript:
        "The function returns a Promise that resolves with user data when the component mounts. We need to refactor the event handling logic to prevent memory leaks after the component unmounts. The component renders a virtualized list for performance optimization during the render phase. The hook manages complex state transitions with proper cleanup functions when the props change.",

    python: "The function defines multiple decorators for authentication and caching with proper type hints. We need to import the necessary modules before initializing the application context using generator expressions. The class inherits from multiple base classes to implement the required interfaces through the MRO. The method returns a generator that yields paginated results with proper exception handling.",

    devops: "The pipeline deploys containerized applications to the Kubernetes cluster with zero downtime. We need to configure auto-scaling policies based on CPU and memory metrics across multiple availability zones. The cluster scales horizontally when traffic increases using infrastructure as code. The container orchestrates microservices with proper service discovery through the service mesh.",

    loremipsum:
        "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",

    klingon:
        "tlhIngan maH Qapla' batlh je. Heghlu'meH QaQ jajvam bIlujDI' yIchegh. Hab SoSlI' Quch tlhIngan maH taHjaj. batlh bIHeghjaj Suvlu'taHvIS yapbe' HoS neH. tIqwIj Sa'angnIS bIjatlh 'e' yImev. yISaHQo' 'ej yIHoHQo' Hoch nuH qel HIja' Qapla'.",
};

export default function DevTextGenerator() {
    const [toolData, setToolData] = useState(null);
    const [output, setOutput] = useState("");
    const [copied, setCopied] = useState(false);
    const [options, setOptions] = useState({
        category: "general",
        paragraphCount: 3,
        sentencesPerParagraph: 4,
        htmlFormatting: "none",
    });
    const [stats, setStats] = useState({
        characters: 0,
        words: 0,
        sentences: 0,
        paragraphs: 0,
    });

    useEffect(() => {
        // Get the tool data
        const data = getToolById("devtextgenerator");
        setToolData(data);
    }, []);

    // Function to load the sample text
    const loadSample = () => {
        const sampleText = SAMPLE_TEXTS[options.category];
        // Apply HTML formatting if selected
        const formattedText = applyHtmlFormatting(
            sampleText,
            options.htmlFormatting
        );
        setOutput(formattedText);
        updateStats(sampleText); // Use the unformatted text for stats
        setCopied(false);
    };

    // Update text statistics
    const updateStats = (text) => {
        const characters = text.length;
        const words = text
            .split(/\s+/)
            .filter((word) => word.length > 0).length;
        const sentences = text
            .split(/[.!?]+/)
            .filter((sentence) => sentence.trim().length > 0).length;
        const paragraphs = text
            .split(/\n\n+/)
            .filter((paragraph) => paragraph.trim().length > 0).length;

        setStats({
            characters,
            words,
            sentences,
            paragraphs,
        });
    };

    // Generate a random sentence
    const generateSentence = (category) => {
        const data = DEV_TEXT_DATA[category];
        const words = data.words;
        const starters = data.sentenceStarters;
        const endings = data.sentenceEndings;

        // Special handling for Lorem Ipsum and Klingon to make them more authentic
        if (category === "loremipsum" || category === "klingon") {
            // Start with a sentence starter
            let sentence =
                starters[Math.floor(Math.random() * starters.length)];

            // For these languages, we'll add more words to make longer, flowing sentences
            const middleWordCount = Math.floor(Math.random() * 10) + 5; // 5-15 words
            for (let i = 0; i < middleWordCount; i++) {
                const randomWord =
                    words[Math.floor(Math.random() * words.length)];
                sentence += " " + randomWord;
            }

            // Add a sentence ending
            sentence +=
                " " + endings[Math.floor(Math.random() * endings.length)] + ".";

            // Ensure proper spacing and capitalization
            return (
                sentence.charAt(0).toUpperCase() +
                sentence.slice(1).replace(/\s+/g, " ")
            );
        } else {
            // Standard handling for technical categories
            // Start with a sentence starter
            let sentence =
                starters[Math.floor(Math.random() * starters.length)];

            // Add some random words in the middle
            const middleWordCount = Math.floor(Math.random() * 6) + 3; // 3-8 words
            for (let i = 0; i < middleWordCount; i++) {
                const randomWord =
                    words[Math.floor(Math.random() * words.length)];
                sentence += " " + randomWord;
            }

            // Add a sentence ending
            sentence +=
                " " + endings[Math.floor(Math.random() * endings.length)] + ".";

            // Capitalize the first letter and ensure proper spacing
            return (
                sentence.charAt(0).toUpperCase() +
                sentence.slice(1).replace(/\s+/g, " ")
            );
        }
    };

    // Generate a code comment (for variety)
    const generateCodeComment = (category) => {
        const data = DEV_TEXT_DATA[category];
        const words = data.words;

        // Create a code comment
        let comment = "// ";
        if (category === "python") {
            comment = "# ";
        }

        // Add some technical words
        const commentLength = Math.floor(Math.random() * 5) + 3; // 3-7 words
        for (let i = 0; i < commentLength; i++) {
            const randomWord = words[Math.floor(Math.random() * words.length)];
            comment += (i === 0 ? "" : " ") + randomWord;
        }

        return comment;
    };

    // Generate a variable or function declaration (for variety)
    const generateVariable = (category) => {
        const data = DEV_TEXT_DATA[category];
        const words = data.words;

        // Create a variable or function
        let variable = "";

        if (category === "javascript") {
            const isConst = Math.random() > 0.5;
            const varName = words[
                Math.floor(Math.random() * words.length)
            ].replace(/[^\w]/g, "");
            variable = (isConst ? "const " : "let ") + varName + " = ";

            // Value could be a string, number, object, or function
            const valueType = Math.floor(Math.random() * 4);
            switch (valueType) {
                case 0: // String
                    variable +=
                        "'" +
                        words[Math.floor(Math.random() * words.length)] +
                        "';";
                    break;
                case 1: // Number
                    variable += Math.floor(Math.random() * 1000) + ";";
                    break;
                case 2: // Object
                    variable +=
                        "{ " +
                        words[Math.floor(Math.random() * words.length)] +
                        ": '" +
                        words[Math.floor(Math.random() * words.length)] +
                        "' };";
                    break;
                case 3: // Function
                    variable +=
                        "() => { return " +
                        words[Math.floor(Math.random() * words.length)] +
                        "; };";
                    break;
            }
        } else if (category === "python") {
            const varName = words[Math.floor(Math.random() * words.length)]
                .replace(/[^\w]/g, "")
                .toLowerCase();
            variable = varName + " = ";

            // Value could be a string, number, list, or dict
            const valueType = Math.floor(Math.random() * 4);
            switch (valueType) {
                case 0: // String
                    variable +=
                        "'" +
                        words[Math.floor(Math.random() * words.length)] +
                        "'";
                    break;
                case 1: // Number
                    variable += Math.floor(Math.random() * 1000).toString();
                    break;
                case 2: // List
                    variable +=
                        "['" +
                        words[Math.floor(Math.random() * words.length)] +
                        "', '" +
                        words[Math.floor(Math.random() * words.length)] +
                        "']";
                    break;
                case 3: // Dict
                    variable +=
                        "{ '" +
                        words[Math.floor(Math.random() * words.length)] +
                        "': '" +
                        words[Math.floor(Math.random() * words.length)] +
                        "' }";
                    break;
            }
        } else {
            // Generic variable for other categories
            const varName = words[
                Math.floor(Math.random() * words.length)
            ].replace(/[^\w]/g, "");
            variable =
                "var " +
                varName +
                " = " +
                words[Math.floor(Math.random() * words.length)] +
                ";";
        }

        return variable;
    };

    // Generate a paragraph
    const generateParagraph = (category, sentenceCount) => {
        let paragraph = "";

        // Add sentences
        for (let i = 0; i < sentenceCount; i++) {
            paragraph += generateSentence(category) + " ";
        }

        return paragraph.trim();
    };

    // Apply HTML formatting to the text
    const applyHtmlFormatting = (text, formattingType) => {
        if (formattingType === "none") {
            return text;
        }

        // Split text into paragraphs
        const paragraphs = text
            .split("\n\n")
            .filter((p) => p.trim().length > 0);

        if (formattingType === "paragraphs") {
            // Wrap each paragraph in <p> tags
            return paragraphs.map((p) => `<p>${p}</p>`).join("\n\n");
        }

        if (formattingType === "basic") {
            // Apply more complex HTML formatting
            let result = "";

            // Make first paragraph an intro with some bold text
            if (paragraphs.length > 0) {
                const firstPara = paragraphs[0];
                const sentences = firstPara
                    .split(".")
                    .filter((s) => s.trim().length > 0);

                if (sentences.length > 0) {
                    // Add a heading based on the first sentence
                    const heading = sentences[0].trim() + ".";
                    result += `<h2>${heading}</h2>\n\n`;

                    // Rest of the first paragraph
                    if (sentences.length > 1) {
                        const restOfPara = sentences.slice(1).join(".") + ".";
                        // Bold some random words
                        const words = restOfPara.split(" ");
                        const boldedWords = words.map((word) => {
                            // Bold about 1 in 10 words that are longer than 4 characters
                            if (word.length > 4 && Math.random() > 0.9) {
                                return `<strong>${word}</strong>`;
                            }
                            return word;
                        });
                        result += `<p>${boldedWords.join(" ")}</p>\n\n`;
                    }
                }

                // Add the rest of the paragraphs
                for (let i = 1; i < paragraphs.length; i++) {
                    result += `<p>${paragraphs[i]}</p>\n\n`;
                }
            }

            return result.trim();
        }

        if (formattingType === "markdown") {
            // Convert to markdown format
            let result = "";

            // Make first paragraph a heading
            if (paragraphs.length > 0) {
                const firstPara = paragraphs[0];
                const sentences = firstPara
                    .split(".")
                    .filter((s) => s.trim().length > 0);

                if (sentences.length > 0) {
                    // Add a heading based on the first sentence
                    const heading = sentences[0].trim() + ".";
                    result += `## ${heading}\n\n`;

                    // Rest of the first paragraph
                    if (sentences.length > 1) {
                        const restOfPara = sentences.slice(1).join(".") + ".";
                        result += `${restOfPara}\n\n`;
                    }
                }

                // Add the rest of the paragraphs with some markdown formatting
                for (let i = 1; i < paragraphs.length; i++) {
                    // Add some bullet points or emphasis randomly
                    if (i === 1 || Math.random() > 0.7) {
                        // Split into bullet points
                        const sentences = paragraphs[i]
                            .split(".")
                            .filter((s) => s.trim().length > 0);
                        result +=
                            sentences.map((s) => `* ${s.trim()}.`).join("\n") +
                            "\n\n";
                    } else if (Math.random() > 0.7) {
                        // Add emphasis
                        result += `*${paragraphs[i]}*\n\n`;
                    } else {
                        // Regular paragraph
                        result += `${paragraphs[i]}\n\n`;
                    }
                }
            }

            return result.trim();
        }

        return text;
    };

    // Generate the full text
    const generateText = () => {
        const {
            category,
            paragraphCount,
            sentencesPerParagraph,
            htmlFormatting,
        } = options;
        let generatedText = "";

        // Generate paragraphs
        for (let i = 0; i < paragraphCount; i++) {
            generatedText +=
                generateParagraph(category, sentencesPerParagraph) + "\n\n";
        }

        // Apply HTML formatting if selected
        const formattedText = applyHtmlFormatting(
            generatedText.trim(),
            htmlFormatting
        );

        setOutput(formattedText);
        updateStats(generatedText); // Use the unformatted text for stats
        setCopied(false);
    };

    // Copy to clipboard
    const copyToClipboard = () => {
        navigator.clipboard.writeText(output).then(() => {
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        });
    };

    // Clear output
    const clearOutput = () => {
        setOutput("");
        setStats({
            characters: 0,
            words: 0,
            sentences: 0,
            paragraphs: 0,
        });
        setCopied(false);
    };

    return (
        <ToolContainer
            className="py-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-colors duration-200"
            containerWidth={toolData?.containerWidth || "default"}>
            <ToolHeader
                toolData={toolData}
                defaultTitle="DevText Generator"
                defaultIcon="FileText"
                defaultDescription="Generate developer-focused placeholder text for your mockups, documentation, and testing. Unlike traditional Lorem Ipsum, DevText Generator creates content with programming terminology, variable names, and technical jargon that looks like real developer documentation or comments."
            />

            <form
                onSubmit={(e) => {
                    e.preventDefault();
                    generateText();
                }}
                className="space-y-6">
                {/* Options Panel */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                        <h3 className="font-medium text-gray-800 dark:text-white">
                            Text Options
                        </h3>

                        {/* Category Selection */}
                        <div>
                            <label
                                htmlFor="category"
                                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Content Category
                            </label>
                            <select
                                id="category"
                                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200 focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                value={options.category}
                                onChange={(e) =>
                                    setOptions({
                                        ...options,
                                        category: e.target.value,
                                    })
                                }>
                                {Object.keys(DEV_TEXT_DATA).map((key) => (
                                    <option key={key} value={key}>
                                        {DEV_TEXT_DATA[key].title}
                                    </option>
                                ))}
                            </select>
                        </div>

                        {/* Paragraph Count */}
                        <div>
                            <label
                                htmlFor="paragraphCount"
                                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Paragraph Count: {options.paragraphCount}
                            </label>
                            <input
                                id="paragraphCount"
                                type="range"
                                min="1"
                                max="10"
                                className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer"
                                value={options.paragraphCount}
                                onChange={(e) =>
                                    setOptions({
                                        ...options,
                                        paragraphCount: parseInt(
                                            e.target.value
                                        ),
                                    })
                                }
                            />
                        </div>

                        {/* Sentences Per Paragraph */}
                        <div>
                            <label
                                htmlFor="sentencesPerParagraph"
                                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Sentences Per Paragraph:{" "}
                                {options.sentencesPerParagraph}
                            </label>
                            <input
                                id="sentencesPerParagraph"
                                type="range"
                                min="1"
                                max="10"
                                className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer"
                                value={options.sentencesPerParagraph}
                                onChange={(e) =>
                                    setOptions({
                                        ...options,
                                        sentencesPerParagraph: parseInt(
                                            e.target.value
                                        ),
                                    })
                                }
                            />
                        </div>
                    </div>

                    <div className="space-y-4">
                        <h3 className="font-medium text-gray-800 dark:text-white">
                            HTML Formatting
                        </h3>

                        {/* HTML Formatting Options */}
                        <div>
                            <label
                                htmlFor="htmlFormatting"
                                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Format Output with HTML Tags
                            </label>
                            <select
                                id="htmlFormatting"
                                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200 focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                value={options.htmlFormatting}
                                onChange={(e) =>
                                    setOptions({
                                        ...options,
                                        htmlFormatting: e.target.value,
                                    })
                                }>
                                <option value="none">No HTML Tags</option>
                                <option value="paragraphs">
                                    Wrap Paragraphs in &lt;p&gt; Tags
                                </option>
                                <option value="basic">
                                    Basic HTML (p, h2, strong)
                                </option>
                                <option value="markdown">
                                    Markdown Format
                                </option>
                            </select>
                        </div>

                        <h3 className="font-medium text-gray-800 dark:text-white mt-6">
                            Actions
                        </h3>

                        {/* Action Buttons */}
                        <div className="flex flex-wrap gap-3 mt-6">
                            <button
                                type="submit"
                                className="px-4 py-2 bg-primary dark:bg-primary-dark text-white rounded-lg hover:bg-primary-dark dark:hover:bg-primary transition-colors">
                                Generate Text
                            </button>

                            <button
                                type="button"
                                className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                                onClick={loadSample}>
                                Load Sample
                            </button>

                            {output && (
                                <button
                                    type="button"
                                    className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                                    onClick={clearOutput}>
                                    Clear
                                </button>
                            )}
                        </div>
                    </div>
                </div>

                {/* Output Section */}
                <div className="mt-6">
                    <div className="flex items-center justify-between mb-2">
                        <label className="text-sm font-semibold text-gray-800 dark:text-gray-200">
                            Generated Text
                        </label>
                        <div className="flex space-x-4">
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                                {stats.characters} characters
                            </span>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                                {stats.words} words
                            </span>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                                {stats.sentences} sentences
                            </span>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                                {stats.paragraphs} paragraphs
                            </span>
                        </div>
                    </div>

                    <div className="relative">
                        <textarea
                            className="w-full h-64 p-4 border border-gray-300 dark:border-gray-600 rounded-xl text-sm font-mono bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark transition-all"
                            value={output}
                            readOnly
                            placeholder="Click 'Generate Text' to create developer-focused placeholder text, or 'Load Sample' to see an example."
                        />

                        {output && (
                            <button
                                type="button"
                                className="absolute bottom-4 right-4 px-3 py-1 bg-primary dark:bg-primary-dark text-white rounded-md text-sm hover:bg-primary-dark dark:hover:bg-primary transition-colors"
                                onClick={copyToClipboard}>
                                {copied ? "Copied!" : "Copy"}
                            </button>
                        )}
                    </div>
                </div>
            </form>
        </ToolContainer>
    );
}
