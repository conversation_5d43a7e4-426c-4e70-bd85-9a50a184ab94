import { useState, useEffect } from "react";
import pkg from "js-beautify";
import { getToolById } from "../../data/tools.js";
import ToolHeader from "../ui/ToolHeader.jsx";
import ToolContainer from "../ui/ToolContainer.jsx";
const { html } = pkg;

export default function MarkupCleanerTool() {
    const [toolData, setToolData] = useState(null);

    useEffect(() => {
        // Get the tool data
        const data = getToolById("markupcleaner");
        setToolData(data);
    }, []);
    const [input, setInput] = useState("");
    const [output, setOutput] = useState("");
    const [copied, setCopied] = useState(false);

    // Sample HTML for demonstration
    const sampleHtml = `<div class="container" style="max-width: 1200px; margin: 0 auto; padding: 20px;" id="main-content" data-section="intro">
  <header class="site-header" style="background-color: #f8f9fa; padding: 15px; margin-bottom: 30px;">
    <h1 style="color: #333; font-size: 28px; font-weight: bold; margin: 0;">Welcome to <span style="color: #007bff;">My Website</span></h1>
    <p class="tagline" id="site-description" style="font-style: italic; color: #6c757d;">A place for clean HTML</p>
  </header>

  <nav style="margin-bottom: 20px;">
    <ul style="list-style: none; padding: 0; display: flex; gap: 15px;">
      <li><a href="#" class="nav-link active" style="color: #007bff; text-decoration: none;">Home</a></li>
      <li><a href="#" class="nav-link" style="color: #6c757d; text-decoration: none;">About</a></li>
      <li><a href="#" class="nav-link" style="color: #6c757d; text-decoration: none;">Services</a></li>
      <li><a href="#" class="nav-link" style="color: #6c757d; text-decoration: none;">Contact</a></li>
    </ul>
  </nav>

  <section class="content-section" id="about-us" data-section="about">
    <h2 style="color: #343a40; font-size: 24px; border-bottom: 2px solid #dee2e6; padding-bottom: 10px;">About Us</h2>
    <p style="line-height: 1.6; margin-bottom: 15px;">Lorem ipsum dolor sit amet, <strong style="font-weight: bold; color: #495057;">consectetur adipiscing elit</strong>. Nullam eget felis eget nunc lobortis mattis aliquam faucibus. Vivamus at <em style="font-style: italic;">magna convallis</em>, ultrices arcu nec, tempor eros.</p>
    <div class="info-box" style="background-color: #e9ecef; border-radius: 5px; padding: 15px; margin: 20px 0;">
      <p style="margin: 0;">This is an information box with some <span style="color: #dc3545;">important</span> details.</p>
    </div>
  </section>

  <table class="data-table" style="width: 100%; border-collapse: collapse; margin: 25px 0;">
    <thead>
      <tr style="background-color: #f8f9fa;">
        <th style="border: 1px solid #dee2e6; padding: 12px; text-align: left;">Name</th>
        <th style="border: 1px solid #dee2e6; padding: 12px; text-align: left;">Position</th>
        <th style="border: 1px solid #dee2e6; padding: 12px; text-align: left;">Office</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td style="border: 1px solid #dee2e6; padding: 12px;">John Doe</td>
        <td style="border: 1px solid #dee2e6; padding: 12px;">Developer</td>
        <td style="border: 1px solid #dee2e6; padding: 12px;">New York</td>
      </tr>
      <tr style="background-color: #f8f9fa;">
        <td style="border: 1px solid #dee2e6; padding: 12px;">Jane Smith</td>
        <td style="border: 1px solid #dee2e6; padding: 12px;">Designer</td>
        <td style="border: 1px solid #dee2e6; padding: 12px;">London</td>
      </tr>
    </tbody>
  </table>

  <footer style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; text-align: center; color: #6c757d;">
    <p>&copy; 2023 My Website. All rights reserved.</p>
  </footer>
</div>`;

    // Function to load the sample HTML
    const loadSample = () => {
        setInput(sampleHtml);
        setOutput("");
        setCopied(false);
    };

    const decodeEntities = (input) => {
        const txt = document.createElement("textarea");
        txt.innerHTML = input;
        return txt.value;
    };

    // Function to normalize all types of whitespace
    const normalizeWhitespace = (input) => {
        // Replace all whitespace characters (including tabs, non-breaking spaces, etc.) with a single space
        return input
            .replace(/[\s\t\n\r\u00A0\u2000-\u200A\u202F\u205F\u3000]+/g, " ")
            .trim();
    };

    const cleanMarkup = () => {
        let cleaned = input;

        // First, normalize all quotes and special characters
        cleaned = cleaned.replace(/'/g, "'");
        cleaned = cleaned.replace(/'/g, "'");
        cleaned = cleaned.replace(/"/g, '"');
        cleaned = cleaned.replace(/"/g, '"');
        cleaned = cleaned.replace(/–/g, "-");
        cleaned = cleaned.replace(/—/g, "-");
        cleaned = cleaned.replace(/…/g, "...");
        cleaned = cleaned.replace(/©/g, "(c)");
        cleaned = cleaned.replace(/®/g, "(r)");
        cleaned = cleaned.replace(/™/g, "(tm)");

        // Remove <style> and <title> blocks
        cleaned = cleaned.replace(/<style[^>]*?>[\s\S]*?<\/style>/gi, "");
        cleaned = cleaned.replace(/<title[^>]*?>[\s\S]*?<\/title>/gi, "");

        // Remove unwanted attributes
        cleaned = cleaned.replace(
            /\s*(style|class|id|data-[^=]+|start)\s*=\s*["'][^"']*["']/gi,
            ""
        );

        // Process tags - remove divs and filter other tags
        const allowedTags = [
            "p",
            "h1",
            "h2",
            "h3",
            "h4",
            "h5",
            "h6",
            "ul",
            "ol",
            "li",
            "strong",
            "em",
            "a",
            "table",
            "thead",
            "tbody",
            "tr",
            "th",
            "td",
            "header",
            "nav",
            "section",
            "footer",
            "main",
            "article",
            "aside",
        ];

        // First, unwrap div tags (remove the tags but keep the content)
        cleaned = cleaned.replace(/<div[^>]*>([\s\S]*?)<\/div>/gi, "$1");

        // Then filter other tags
        cleaned = cleaned.replace(/<\/?([a-z0-9]+)[^>]*?>/gi, (match, tag) => {
            return allowedTags.includes(tag.toLowerCase()) ? match : "";
        });

        // Remove attributes from table-related tags
        cleaned = cleaned.replace(
            /<(table|thead|tbody|tr|th|td)[^>]*>/gi,
            (match, tag) => {
                return `<${tag}>`;
            }
        );

        // Unwrap <span> tags
        cleaned = cleaned.replace(/<span[^>]*>([\s\S]*?)<\/span>/gi, "$1");

        // Remove empty paragraphs
        cleaned = cleaned.replace(/<p>\s*<\/p>/gi, "");

        // Normalize whitespace within tags
        cleaned = cleaned.replace(/>([^<]*)</g, (match, content) => {
            return ">" + normalizeWhitespace(content) + "<";
        });

        // Apply beautification
        cleaned = html(cleaned, {
            indent_size: 4,
            indent_char: " ",
            max_preserve_newlines: 2,
            preserve_newlines: true,
            indent_inner_html: true,
            wrap_line_length: 0,
            unformatted: [],
            content_unformatted: ["pre", "code", "textarea"],
            end_with_newline: true,
            brace_style: "expand",
            indent_scripts: "normal",
        });

        // Decode HTML entities
        cleaned = decodeEntities(cleaned);

        // Add <p> tags to lines of text without any HTML tags
        // Split the content into lines
        const lines = cleaned.split("\n");
        const processedLines = lines.map((line) => {
            // Trim the line to check if it's empty
            const trimmedLine = line.trim();

            // Skip empty lines or lines that contain any HTML tags (opening or closing)
            if (trimmedLine === "" || /<\/?[a-z][\s\S]*?>/i.test(trimmedLine)) {
                return line;
            }

            // Add <p> tags to text lines without tags
            return line.replace(/^(\s*)(.*?)(\s*)$/, "$1<p>$2</p>$3");
        });

        // Join the lines back together
        cleaned = processedLines.join("\n");

        // Final pass to ensure all quotes are normalized
        cleaned = cleaned.replace(/'/g, "'");
        cleaned = cleaned.replace(/'/g, "'");
        cleaned = cleaned.replace(/"/g, '"');
        cleaned = cleaned.replace(/"/g, '"');

        // Remove excessive empty lines (more than 2 consecutive newlines)
        cleaned = cleaned.replace(/\n{3,}/g, "\n\n");

        setOutput(cleaned);
        setCopied(false);
    };

    const copyToClipboard = () => {
        // Ensure the copied text has straight quotes
        const cleanedOutput = output
            .replace(/'/g, "'")
            .replace(/'/g, "'")
            .replace(/"/g, '"')
            .replace(/"/g, '"');

        navigator.clipboard.writeText(cleanedOutput).then(() => {
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        });
    };

    const clearInput = () => {
        setInput("");
        setOutput("");
        setCopied(false);
    };

    return (
        <ToolContainer
            className="py-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-colors duration-200"
            containerWidth={toolData?.containerWidth || "default"}>
            <ToolHeader
                toolData={toolData}
                defaultTitle="Markup Cleaner"
                defaultIcon="FileCode"
                defaultDescription="Paste any messy HTML below. We'll automatically strip out unnecessary tags, inline styles, classes, IDs, and any embedded stylesheets — leaving you with clean, readable markup that's safe to paste directly into your CMS, blog editor, or project."
            />

            <form onSubmit={(e) => e.preventDefault()} className="space-y-6">
                <div>
                    <div className="flex justify-between items-center mb-2">
                        <label
                            htmlFor="input"
                            className="block text-sm font-semibold text-gray-800 dark:text-gray-200">
                            Raw Markup
                        </label>
                        <div className="flex space-x-2">
                            <button
                                type="button"
                                className="px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-md text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                                onClick={loadSample}>
                                Load Sample
                            </button>
                            {input && (
                                <button
                                    type="button"
                                    className="px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-md text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                                    onClick={clearInput}>
                                    Clear
                                </button>
                            )}
                        </div>
                    </div>
                    <textarea
                        id="input"
                        rows="10"
                        className="w-full p-4 border border-gray-300 dark:border-gray-600 rounded-xl text-sm font-mono bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark transition-all"
                        value={input}
                        onChange={(e) => setInput(e.target.value)}
                        placeholder="<div style='...'><h1>Hello <span>world</span></h1></div>"
                    />
                </div>

                <div className="flex flex-wrap items-center gap-4">
                    <button
                        type="button"
                        className="bg-primary dark:bg-primary-dark text-white px-5 py-2 rounded-xl hover:bg-primary/90 dark:hover:bg-primary-dark/90 focus:outline-none focus:ring-4 focus:ring-primary/50 dark:focus:ring-primary-dark/50 transition-all"
                        onClick={cleanMarkup}>
                        Clean Markup
                    </button>

                    {output && (
                        <button
                            type="button"
                            className="text-sm text-teal-600 dark:text-teal-400 border border-teal-500 dark:border-teal-400 px-4 py-1 rounded-xl hover:bg-teal-50 dark:hover:bg-teal-900/20 focus:outline-none transition-all"
                            onClick={copyToClipboard}>
                            {copied ? "Copied!" : "Copy to Clipboard"}
                        </button>
                    )}
                </div>

                <div>
                    <div className="flex justify-between items-center mb-2">
                        <label
                            htmlFor="output"
                            className="block text-sm font-semibold text-gray-800 dark:text-gray-200">
                            Cleaned Markup
                        </label>
                        {output && (
                            <div className="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md">
                                Ready to copy and paste
                            </div>
                        )}
                    </div>
                    <textarea
                        id="output"
                        rows="10"
                        className="w-full p-4 border border-gray-300 dark:border-gray-600 rounded-xl text-sm font-mono bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 shadow-sm"
                        readOnly
                        value={output}
                    />
                </div>
            </form>
        </ToolContainer>
    );
}
