import { useState, useEffect } from "react";
import { getToolById } from "../../data/tools.js";
import { HexColorPicker } from "react-colorful";
import ToolHeader from "../ui/ToolHeader.jsx";
import ToolContainer from "../ui/ToolContainer.jsx";
import LucideIcon from "../ui/LucideIcon.jsx";

// Check if we're in the browser environment
const isBrowser = typeof window !== "undefined";

// Safely get tool data
const safeGetToolData = () => {
    try {
        return getToolById("colorpalette");
    } catch (error) {
        console.error("Error getting tool data:", error);
        return {
            name: "Color Palette Generator",
            description:
                "Create harmonious color palettes for your web projects with our intuitive color palette generator.",
            icon: "Palette",
        };
    }
};

// Color scheme types
const COLOR_SCHEMES = {
    MONOCHROMATIC: "monochromatic",
    ANALOGOUS: "analogous",
    COMPLEMENTARY: "complementary",
    TRIADIC: "triadic",
    TETRADIC: "tetradic",
    SPLIT_COMPLEMENTARY: "split-complementary",
    SQUARE: "square",
    CUSTOM: "custom",
};

// Export format options
const EXPORT_FORMATS = {
    HEX: "hex",
    RGB: "rgb",
    HSL: "hsl",
    CSS_VARS: "css-vars",
    TAILWIND: "tailwind",
};

// Default colors
const DEFAULT_COLORS = [
    { id: 1, value: "#3366cc" }, // Primary blue
];

export default function ColorPaletteGenerator() {
    const [toolData, setToolData] = useState(null);
    const [baseColors, setBaseColors] = useState(DEFAULT_COLORS);
    const [activeColorId, setActiveColorId] = useState(1);
    const [colorScheme, setColorScheme] = useState(COLOR_SCHEMES.MONOCHROMATIC);
    const [palette, setPalette] = useState([]);
    const [exportFormat, setExportFormat] = useState(EXPORT_FORMATS.HEX);
    const [showColorPicker, setShowColorPicker] = useState(false);
    const [copiedIndex, setCopiedIndex] = useState(null);
    const [copiedAll, setCopiedAll] = useState(false);
    const [expandedPalette, setExpandedPalette] = useState(true);

    useEffect(() => {
        // Get the tool data safely
        const data = safeGetToolData();
        setToolData(data);
    }, []);

    useEffect(() => {
        // Generate the palette whenever the base colors or scheme changes
        generatePalette();
    }, [baseColors, colorScheme, expandedPalette]);

    // Convert hex to RGB
    const hexToRgb = (hex) => {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result
            ? {
                  r: parseInt(result[1], 16),
                  g: parseInt(result[2], 16),
                  b: parseInt(result[3], 16),
              }
            : null;
    };

    // Convert RGB to hex
    const rgbToHex = (r, g, b) => {
        return `#${((1 << 24) + (r << 16) + (g << 8) + b)
            .toString(16)
            .slice(1)}`;
    };

    // Convert RGB to HSL
    const rgbToHsl = (r, g, b) => {
        r /= 255;
        g /= 255;
        b /= 255;

        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        let h,
            s,
            l = (max + min) / 2;

        if (max === min) {
            h = s = 0; // achromatic
        } else {
            const d = max - min;
            s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

            switch (max) {
                case r:
                    h = (g - b) / d + (g < b ? 6 : 0);
                    break;
                case g:
                    h = (b - r) / d + 2;
                    break;
                case b:
                    h = (r - g) / d + 4;
                    break;
            }

            h /= 6;
        }

        return {
            h: Math.round(h * 360),
            s: Math.round(s * 100),
            l: Math.round(l * 100),
        };
    };

    // Convert HSL to RGB
    const hslToRgb = (h, s, l) => {
        h /= 360;
        s /= 100;
        l /= 100;
        let r, g, b;

        if (s === 0) {
            r = g = b = l; // achromatic
        } else {
            const hue2rgb = (p, q, t) => {
                if (t < 0) t += 1;
                if (t > 1) t -= 1;
                if (t < 1 / 6) return p + (q - p) * 6 * t;
                if (t < 1 / 2) return q;
                if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
                return p;
            };

            const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
            const p = 2 * l - q;

            r = hue2rgb(p, q, h + 1 / 3);
            g = hue2rgb(p, q, h);
            b = hue2rgb(p, q, h - 1 / 3);
        }

        return {
            r: Math.round(r * 255),
            g: Math.round(g * 255),
            b: Math.round(b * 255),
        };
    };

    // Get active color value
    const getActiveColorValue = () => {
        const activeColor = baseColors.find(
            (color) => color.id === activeColorId
        );
        return activeColor ? activeColor.value : "#3366cc";
    };

    // Update a base color
    const updateBaseColor = (id, value) => {
        setBaseColors((prevColors) =>
            prevColors.map((color) =>
                color.id === id ? { ...color, value } : color
            )
        );
    };

    // Add a new base color
    const addBaseColor = () => {
        const newId = Math.max(0, ...baseColors.map((c) => c.id)) + 1;
        setBaseColors([...baseColors, { id: newId, value: "#3366cc" }]);
        setActiveColorId(newId);
    };

    // Remove a base color
    const removeBaseColor = (id) => {
        if (baseColors.length <= 1) return; // Don't remove the last color

        const newColors = baseColors.filter((color) => color.id !== id);
        setBaseColors(newColors);

        // If we removed the active color, set a new active color
        if (activeColorId === id) {
            setActiveColorId(newColors[0].id);
        }
    };

    // Generate monochromatic palette for a single color
    const generateMonochromaticPalette = (hsl, expanded = false) => {
        if (expanded) {
            // Generate 9 shades of the same hue
            return [
                { h: hsl.h, s: hsl.s, l: 95 }, // Lightest
                { h: hsl.h, s: hsl.s, l: 85 },
                { h: hsl.h, s: hsl.s, l: 75 },
                { h: hsl.h, s: hsl.s, l: 65 },
                { h: hsl.h, s: Math.min(hsl.s + 10, 100), l: 50 }, // Base color with slightly increased saturation
                { h: hsl.h, s: hsl.s, l: 40 },
                { h: hsl.h, s: hsl.s, l: 30 },
                { h: hsl.h, s: hsl.s, l: 20 },
                { h: hsl.h, s: hsl.s, l: 10 }, // Darkest
            ];
        } else {
            // Generate 5 shades of the same hue
            return [
                { h: hsl.h, s: hsl.s, l: 90 }, // Lightest
                { h: hsl.h, s: hsl.s, l: 70 },
                { h: hsl.h, s: hsl.s, l: 50 }, // Base color
                { h: hsl.h, s: hsl.s, l: 30 },
                { h: hsl.h, s: hsl.s, l: 10 }, // Darkest
            ];
        }
    };

    // Generate colors for a specific scheme based on a base color
    const generateColorsForScheme = (hsl, scheme, expanded = false) => {
        let colors = [];

        switch (scheme) {
            case COLOR_SCHEMES.MONOCHROMATIC:
                colors = generateMonochromaticPalette(hsl, expanded);
                break;

            case COLOR_SCHEMES.ANALOGOUS:
                if (expanded) {
                    // Generate 7 colors - base color and 3 colors on each side
                    colors = [
                        { h: (hsl.h - 60 + 360) % 360, s: hsl.s, l: hsl.l },
                        { h: (hsl.h - 40 + 360) % 360, s: hsl.s, l: hsl.l },
                        { h: (hsl.h - 20 + 360) % 360, s: hsl.s, l: hsl.l },
                        { h: hsl.h, s: hsl.s, l: hsl.l }, // Base color
                        { h: (hsl.h + 20) % 360, s: hsl.s, l: hsl.l },
                        { h: (hsl.h + 40) % 360, s: hsl.s, l: hsl.l },
                        { h: (hsl.h + 60) % 360, s: hsl.s, l: hsl.l },
                    ];

                    // Add variations of the base color
                    colors.push({ h: hsl.h, s: hsl.s, l: 80 }); // Light base
                    colors.push({ h: hsl.h, s: hsl.s, l: 30 }); // Dark base
                } else {
                    // Base color and 2 colors on each side
                    colors = [
                        { h: (hsl.h - 40 + 360) % 360, s: hsl.s, l: hsl.l },
                        { h: (hsl.h - 20 + 360) % 360, s: hsl.s, l: hsl.l },
                        { h: hsl.h, s: hsl.s, l: hsl.l }, // Base color
                        { h: (hsl.h + 20) % 360, s: hsl.s, l: hsl.l },
                        { h: (hsl.h + 40) % 360, s: hsl.s, l: hsl.l },
                    ];
                }
                break;

            case COLOR_SCHEMES.COMPLEMENTARY:
                if (expanded) {
                    // Base color, complementary, and more variations
                    colors = [
                        { h: hsl.h, s: hsl.s, l: 90 }, // Lightest base
                        { h: hsl.h, s: hsl.s, l: 70 }, // Light base
                        { h: hsl.h, s: hsl.s, l: hsl.l }, // Base color
                        { h: hsl.h, s: hsl.s, l: 30 }, // Dark base
                        { h: hsl.h, s: hsl.s, l: 10 }, // Darkest base
                        { h: (hsl.h + 180) % 360, s: hsl.s, l: 90 }, // Lightest complement
                        { h: (hsl.h + 180) % 360, s: hsl.s, l: 70 }, // Light complement
                        { h: (hsl.h + 180) % 360, s: hsl.s, l: hsl.l }, // Complement
                        { h: (hsl.h + 180) % 360, s: hsl.s, l: 30 }, // Dark complement
                        { h: (hsl.h + 180) % 360, s: hsl.s, l: 10 }, // Darkest complement
                    ];
                } else {
                    // Base color, complementary, and variations
                    colors = [
                        { h: hsl.h, s: hsl.s, l: 80 }, // Light base
                        { h: hsl.h, s: hsl.s, l: hsl.l }, // Base color
                        { h: hsl.h, s: hsl.s, l: 30 }, // Dark base
                        { h: (hsl.h + 180) % 360, s: hsl.s, l: 80 }, // Light complement
                        { h: (hsl.h + 180) % 360, s: hsl.s, l: hsl.l }, // Complement
                    ];
                }
                break;

            case COLOR_SCHEMES.SPLIT_COMPLEMENTARY:
                // Base color and two colors adjacent to its complement
                colors = [
                    { h: hsl.h, s: hsl.s, l: 80 }, // Light base
                    { h: hsl.h, s: hsl.s, l: hsl.l }, // Base color
                    { h: hsl.h, s: hsl.s, l: 30 }, // Dark base
                    { h: (hsl.h + 150) % 360, s: hsl.s, l: hsl.l }, // Split complement 1
                    { h: (hsl.h + 210) % 360, s: hsl.s, l: hsl.l }, // Split complement 2
                ];

                if (expanded) {
                    // Add more variations
                    colors.push({ h: (hsl.h + 150) % 360, s: hsl.s, l: 80 }); // Light split 1
                    colors.push({ h: (hsl.h + 150) % 360, s: hsl.s, l: 30 }); // Dark split 1
                    colors.push({ h: (hsl.h + 210) % 360, s: hsl.s, l: 80 }); // Light split 2
                    colors.push({ h: (hsl.h + 210) % 360, s: hsl.s, l: 30 }); // Dark split 2
                }
                break;

            case COLOR_SCHEMES.TRIADIC:
                if (expanded) {
                    // Three colors evenly spaced with variations
                    colors = [
                        { h: hsl.h, s: hsl.s, l: 90 }, // Lightest base
                        { h: hsl.h, s: hsl.s, l: 70 }, // Light base
                        { h: hsl.h, s: hsl.s, l: hsl.l }, // Base color
                        { h: hsl.h, s: hsl.s, l: 30 }, // Dark base
                        { h: (hsl.h + 120) % 360, s: hsl.s, l: 90 }, // Light triad 1
                        { h: (hsl.h + 120) % 360, s: hsl.s, l: hsl.l }, // Triad 1
                        { h: (hsl.h + 120) % 360, s: hsl.s, l: 30 }, // Dark triad 1
                        { h: (hsl.h + 240) % 360, s: hsl.s, l: 90 }, // Light triad 2
                        { h: (hsl.h + 240) % 360, s: hsl.s, l: hsl.l }, // Triad 2
                        { h: (hsl.h + 240) % 360, s: hsl.s, l: 30 }, // Dark triad 2
                    ];
                } else {
                    // Three colors evenly spaced
                    colors = [
                        { h: hsl.h, s: hsl.s, l: 80 }, // Light base
                        { h: hsl.h, s: hsl.s, l: hsl.l }, // Base color
                        { h: (hsl.h + 120) % 360, s: hsl.s, l: hsl.l }, // Triad 1
                        { h: (hsl.h + 240) % 360, s: hsl.s, l: hsl.l }, // Triad 2
                        { h: hsl.h, s: hsl.s, l: 30 }, // Dark base
                    ];
                }
                break;

            case COLOR_SCHEMES.SQUARE:
                // Four colors evenly spaced (90 degrees apart)
                colors = [
                    { h: hsl.h, s: hsl.s, l: hsl.l }, // Base color
                    { h: (hsl.h + 90) % 360, s: hsl.s, l: hsl.l }, // Square 1
                    { h: (hsl.h + 180) % 360, s: hsl.s, l: hsl.l }, // Square 2
                    { h: (hsl.h + 270) % 360, s: hsl.s, l: hsl.l }, // Square 3
                ];

                if (expanded) {
                    // Add light and dark variations of each color
                    colors.push({ h: hsl.h, s: hsl.s, l: 80 }); // Light base
                    colors.push({ h: hsl.h, s: hsl.s, l: 30 }); // Dark base
                    colors.push({ h: (hsl.h + 90) % 360, s: hsl.s, l: 80 }); // Light square 1
                    colors.push({ h: (hsl.h + 90) % 360, s: hsl.s, l: 30 }); // Dark square 1
                    colors.push({ h: (hsl.h + 180) % 360, s: hsl.s, l: 80 }); // Light square 2
                    colors.push({ h: (hsl.h + 180) % 360, s: hsl.s, l: 30 }); // Dark square 2
                    colors.push({ h: (hsl.h + 270) % 360, s: hsl.s, l: 80 }); // Light square 3
                    colors.push({ h: (hsl.h + 270) % 360, s: hsl.s, l: 30 }); // Dark square 3
                }
                break;

            case COLOR_SCHEMES.TETRADIC:
                if (expanded) {
                    // Four colors in a rectangle with variations
                    colors = [
                        { h: hsl.h, s: hsl.s, l: 80 }, // Light base
                        { h: hsl.h, s: hsl.s, l: hsl.l }, // Base color
                        { h: hsl.h, s: hsl.s, l: 30 }, // Dark base
                        { h: (hsl.h + 60) % 360, s: hsl.s, l: 80 }, // Light tetrad 1
                        { h: (hsl.h + 60) % 360, s: hsl.s, l: hsl.l }, // Tetrad 1
                        { h: (hsl.h + 60) % 360, s: hsl.s, l: 30 }, // Dark tetrad 1
                        { h: (hsl.h + 180) % 360, s: hsl.s, l: 80 }, // Light tetrad 2
                        { h: (hsl.h + 180) % 360, s: hsl.s, l: hsl.l }, // Tetrad 2
                        { h: (hsl.h + 240) % 360, s: hsl.s, l: 80 }, // Light tetrad 3
                        { h: (hsl.h + 240) % 360, s: hsl.s, l: hsl.l }, // Tetrad 3
                        { h: (hsl.h + 240) % 360, s: hsl.s, l: 30 }, // Dark tetrad 3
                    ];
                } else {
                    // Four colors in a rectangle
                    colors = [
                        { h: hsl.h, s: hsl.s, l: hsl.l }, // Base color
                        { h: (hsl.h + 60) % 360, s: hsl.s, l: hsl.l }, // Tetrad 1
                        { h: (hsl.h + 180) % 360, s: hsl.s, l: hsl.l }, // Tetrad 2
                        { h: (hsl.h + 240) % 360, s: hsl.s, l: hsl.l }, // Tetrad 3
                        { h: hsl.h, s: hsl.s, l: 30 }, // Dark base
                    ];
                }
                break;

            default:
                colors = [{ h: hsl.h, s: hsl.s, l: hsl.l }];
        }

        return colors;
    };

    // Generate the color palette based on the selected scheme
    const generatePalette = () => {
        // For custom scheme, we just use the base colors directly
        if (colorScheme === COLOR_SCHEMES.CUSTOM) {
            const hexColors = baseColors
                .map((color) => {
                    const rgb = hexToRgb(color.value);
                    if (!rgb) return null;

                    const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);
                    return {
                        hex: color.value,
                        rgb: `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})`,
                        hsl: `hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)`,
                        isBaseColor: true,
                        baseColorId: color.id,
                    };
                })
                .filter(Boolean);

            setPalette(hexColors);
            return;
        }

        // For other schemes, we'll process each base color
        let colors = [];

        // If we have multiple base colors, we'll generate a palette for each base color
        if (baseColors.length > 1) {
            let allColors = [];

            baseColors.forEach((baseColor, index) => {
                const rgb = hexToRgb(baseColor.value);
                if (!rgb) return;

                const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);

                // Generate colors for this base color
                let schemeColors;
                if (colorScheme === COLOR_SCHEMES.MONOCHROMATIC) {
                    schemeColors = generateMonochromaticPalette(hsl, false);
                } else {
                    schemeColors = generateColorsForScheme(
                        hsl,
                        colorScheme,
                        expandedPalette
                    );
                }

                // Mark the base color in the generated palette
                // For monochromatic, it's the middle color (index 2)
                // For other schemes, it depends on the scheme
                let baseColorIndex;

                if (colorScheme === COLOR_SCHEMES.MONOCHROMATIC) {
                    baseColorIndex = 2; // Middle shade
                } else if (colorScheme === COLOR_SCHEMES.COMPLEMENTARY) {
                    baseColorIndex = expandedPalette ? 2 : 1; // Base color
                } else if (colorScheme === COLOR_SCHEMES.ANALOGOUS) {
                    baseColorIndex = expandedPalette ? 3 : 2; // Base color
                } else if (colorScheme === COLOR_SCHEMES.TRIADIC) {
                    baseColorIndex = expandedPalette ? 2 : 1; // Base color
                } else if (
                    colorScheme === COLOR_SCHEMES.SQUARE ||
                    colorScheme === COLOR_SCHEMES.TETRADIC
                ) {
                    baseColorIndex = expandedPalette ? 1 : 0; // Base color
                } else if (colorScheme === COLOR_SCHEMES.SPLIT_COMPLEMENTARY) {
                    baseColorIndex = 1; // Base color
                } else {
                    baseColorIndex = 0; // Default
                }

                // Mark the base color
                if (schemeColors[baseColorIndex]) {
                    schemeColors[baseColorIndex].isBaseColor = true;
                    schemeColors[baseColorIndex].baseColorId = baseColor.id;
                }

                allColors = allColors.concat(schemeColors);
            });

            colors = allColors;

            // Convert HSL colors to hex and set the palette
            const hexColors = colors.map((color) => {
                const rgb = hslToRgb(color.h, color.s, color.l);
                return {
                    hex: rgbToHex(rgb.r, rgb.g, rgb.b),
                    rgb: `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})`,
                    hsl: `hsl(${color.h}, ${color.s}%, ${color.l}%)`,
                    isBaseColor: color.isBaseColor || false,
                    baseColorId: color.baseColorId,
                };
            });

            setPalette(hexColors);
            return;
        }

        // For single color, use it as the primary base
        const primaryColor = baseColors[0]?.value || "#3366cc";
        const rgb = hexToRgb(primaryColor);
        if (!rgb) return;

        const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);

        // For single color case
        if (colorScheme === COLOR_SCHEMES.MONOCHROMATIC) {
            // Generate monochromatic palette
            colors = generateMonochromaticPalette(hsl, expandedPalette);

            // Mark the base color (middle shade)
            const baseColorIndex = Math.floor(colors.length / 2);
            if (colors[baseColorIndex]) {
                colors[baseColorIndex].isBaseColor = true;
                colors[baseColorIndex].baseColorId = baseColors[0].id;
            }
        } else {
            // For other schemes, use the generateColorsForScheme function
            colors = generateColorsForScheme(hsl, colorScheme, expandedPalette);

            // Mark the base color based on the scheme
            let baseColorIndex;
            if (colorScheme === COLOR_SCHEMES.COMPLEMENTARY) {
                baseColorIndex = expandedPalette ? 2 : 1;
            } else if (colorScheme === COLOR_SCHEMES.ANALOGOUS) {
                baseColorIndex = expandedPalette ? 3 : 2;
            } else if (colorScheme === COLOR_SCHEMES.TRIADIC) {
                baseColorIndex = expandedPalette ? 2 : 1;
            } else if (
                colorScheme === COLOR_SCHEMES.SQUARE ||
                colorScheme === COLOR_SCHEMES.TETRADIC
            ) {
                baseColorIndex = expandedPalette ? 1 : 0;
            } else if (colorScheme === COLOR_SCHEMES.SPLIT_COMPLEMENTARY) {
                baseColorIndex = 1;
            } else {
                baseColorIndex = 0;
            }

            if (colors[baseColorIndex]) {
                colors[baseColorIndex].isBaseColor = true;
                colors[baseColorIndex].baseColorId = baseColors[0].id;
            }
        }

        // Convert HSL colors to hex
        const hexColors = colors.map((color) => {
            const rgb = hslToRgb(color.h, color.s, color.l);
            return {
                hex: rgbToHex(rgb.r, rgb.g, rgb.b),
                rgb: `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})`,
                hsl: `hsl(${color.h}, ${color.s}%, ${color.l}%)`,
                isBaseColor: color.isBaseColor || false,
                baseColorId: color.baseColorId,
            };
        });

        setPalette(hexColors);
    };

    // Get all base colors directly from the user input
    const getAllBaseColors = () => {
        // Create color objects from the user's input base colors
        return baseColors
            .map((color) => {
                const rgb = hexToRgb(color.value);
                if (!rgb) return null;

                const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);
                return {
                    hex: color.value,
                    rgb: `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})`,
                    hsl: `hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)`,
                    isBaseColor: true,
                    baseColorId: color.id,
                };
            })
            .filter(Boolean);
    };

    // Get the base colors for display
    const getBaseColors = (primaryColors) => {
        // Get all base colors directly from user input
        const userBaseColors = getAllBaseColors();

        // If no primary colors provided, return all base colors
        if (!primaryColors || primaryColors.length === 0) {
            return userBaseColors;
        }

        // If there are multiple base colors, always show all of them
        if (userBaseColors.length > 1) {
            return userBaseColors;
        }

        // For a single base color, only show it if it's different from all primary colors
        // Check if the base color is already in the primary colors
        const baseColorInPrimary = userBaseColors.every((baseColor) =>
            primaryColors.some(
                (primaryColor) =>
                    primaryColor.hex.toLowerCase() ===
                    baseColor.hex.toLowerCase()
            )
        );

        // If the base color is already in primary colors, don't show it separately
        if (baseColorInPrimary) {
            return [];
        }

        // Otherwise, return the base color
        return userBaseColors;
    };

    // Categorize colors in the palette
    const categorizeColors = () => {
        if (palette.length === 0)
            return { primary: [], secondary: [], tertiary: [] };

        // First, extract all base colors from the palette
        const baseColorEntries = palette.filter((color) => color.isBaseColor);

        // If we have base colors marked in the palette, always put them in primary
        if (baseColorEntries.length > 0) {
            // For all schemes, put all base colors in primary
            const primary = [...baseColorEntries];

            // Get the remaining colors (non-base colors)
            const remainingColors = palette.filter(
                (color) => !color.isBaseColor
            );

            // Divide remaining colors between secondary and tertiary
            const secondaryCount = Math.min(
                Math.ceil(remainingColors.length / 2),
                remainingColors.length
            );
            const secondary = remainingColors.slice(0, secondaryCount);
            const tertiary = remainingColors.slice(secondaryCount);

            return { primary, secondary, tertiary };
        }

        // Fallback for custom scheme if no base colors are marked
        if (colorScheme === COLOR_SCHEMES.CUSTOM) {
            // If we have multiple base colors, use them all as primary
            if (baseColors.length > 1) {
                return {
                    primary: palette.slice(0, baseColors.length),
                    secondary: palette.slice(
                        baseColors.length,
                        baseColors.length +
                            Math.min(2, palette.length - baseColors.length)
                    ),
                    tertiary: palette.slice(
                        baseColors.length +
                            Math.min(2, palette.length - baseColors.length)
                    ),
                };
            } else {
                // Original behavior for single base color
                return {
                    primary: palette.slice(0, 1),
                    secondary: palette.slice(1, 3),
                    tertiary: palette.slice(3),
                };
            }
        }

        // For monochromatic with a single base color (fallback)
        if (
            colorScheme === COLOR_SCHEMES.MONOCHROMATIC &&
            baseColors.length === 1
        ) {
            const middle = Math.floor(palette.length / 2);
            return {
                primary: [palette[middle]], // Middle shade (base color)
                secondary: palette
                    .slice(middle - 1, middle)
                    .concat(palette.slice(middle + 1, middle + 2))
                    .filter(Boolean), // Shades close to base
                tertiary: palette
                    .slice(0, middle - 1)
                    .concat(palette.slice(middle + 2))
                    .filter(Boolean), // Remaining shades
            };
        }

        // For multiple base colors with non-monochromatic schemes (fallback)
        if (
            baseColors.length > 1 &&
            colorScheme !== COLOR_SCHEMES.MONOCHROMATIC
        ) {
            // Divide the palette into sections based on the number of base colors
            const colorsPerBaseColor = Math.floor(
                palette.length / baseColors.length
            );

            // Select primary colors (one for each base color)
            const primary = [];
            const secondary = [];
            const tertiary = [];

            for (let i = 0; i < baseColors.length; i++) {
                const startIdx = i * colorsPerBaseColor;
                const endIdx = (i + 1) * colorsPerBaseColor;

                // Add the middle color of each section to primary
                const middleIdx = startIdx + Math.floor(colorsPerBaseColor / 2);
                if (palette[middleIdx]) {
                    primary.push(palette[middleIdx]);
                }

                // Add some colors to secondary
                for (let j = startIdx; j < endIdx; j++) {
                    if (
                        j !== middleIdx &&
                        palette[j] &&
                        secondary.length < baseColors.length * 2
                    ) {
                        secondary.push(palette[j]);
                    } else if (j !== middleIdx && palette[j]) {
                        tertiary.push(palette[j]);
                    }
                }
            }

            // Add any remaining colors to tertiary
            const usedIndices = new Set(
                [...primary, ...secondary].map((color) =>
                    palette.indexOf(color)
                )
            );
            const remainingColors = palette.filter(
                (_, idx) => !usedIndices.has(idx)
            );
            tertiary.push(...remainingColors);

            return { primary, secondary, tertiary };
        }

        // For other schemes, categorize based on the scheme type
        switch (colorScheme) {
            case COLOR_SCHEMES.MONOCHROMATIC:
                // For multiple base colors, each base color is primary for its group
                return {
                    primary: baseColors
                        .map((_, i) => palette[i * 5 + 2])
                        .filter(Boolean),
                    secondary: baseColors
                        .flatMap((_, i) => [
                            palette[i * 5 + 1],
                            palette[i * 5 + 3],
                        ])
                        .filter(Boolean),
                    tertiary: baseColors
                        .flatMap((_, i) => [palette[i * 5], palette[i * 5 + 4]])
                        .filter(Boolean),
                };

            case COLOR_SCHEMES.COMPLEMENTARY:
                if (expandedPalette) {
                    // Make sure we have enough colors in the palette
                    if (palette.length >= 10) {
                        return {
                            primary: [palette[2], palette[7]].filter(Boolean), // Base color and complement
                            secondary: [
                                palette[1],
                                palette[3],
                                palette[6],
                                palette[8],
                            ].filter(Boolean), // Medium shades
                            tertiary: [
                                palette[0],
                                palette[4],
                                palette[5],
                                palette[9],
                            ].filter(Boolean), // Extreme shades
                        };
                    } else {
                        // Fallback for when we don't have enough colors
                        return {
                            primary: palette.slice(0, 2),
                            secondary: palette.slice(2, 4),
                            tertiary: palette.slice(4),
                        };
                    }
                } else {
                    // Make sure we have enough colors in the palette
                    if (palette.length >= 5) {
                        return {
                            primary: [palette[1], palette[4]].filter(Boolean), // Base color and complement
                            secondary: [palette[0], palette[3]].filter(Boolean), // Light shades
                            tertiary: [palette[2]].filter(Boolean), // Dark base
                        };
                    } else {
                        // Fallback for when we don't have enough colors
                        return {
                            primary: palette.slice(0, 1),
                            secondary: palette.slice(1, 2),
                            tertiary: palette.slice(2),
                        };
                    }
                }

            case COLOR_SCHEMES.ANALOGOUS:
                if (expandedPalette) {
                    // Make sure we have enough colors in the palette
                    if (palette.length >= 5) {
                        return {
                            primary: [palette[3]].filter(Boolean), // Base color
                            secondary: [palette[2], palette[4]].filter(Boolean), // Adjacent colors
                            tertiary: palette.filter(
                                (_, i) => i !== 2 && i !== 3 && i !== 4
                            ), // Other colors
                        };
                    } else {
                        // Fallback for when we don't have enough colors
                        return {
                            primary: palette.slice(0, 1),
                            secondary: palette.slice(1, 3),
                            tertiary: palette.slice(3),
                        };
                    }
                } else {
                    // Make sure we have enough colors in the palette
                    if (palette.length >= 5) {
                        return {
                            primary: [palette[2]].filter(Boolean), // Base color
                            secondary: [palette[1], palette[3]].filter(Boolean), // Adjacent colors
                            tertiary: [palette[0], palette[4]].filter(Boolean), // Outer colors
                        };
                    } else {
                        // Fallback for when we don't have enough colors
                        return {
                            primary: palette.slice(0, 1),
                            secondary: palette.slice(1, 3),
                            tertiary: palette.slice(3),
                        };
                    }
                }

            case COLOR_SCHEMES.TRIADIC:
                if (expandedPalette) {
                    // Make sure we have enough colors in the palette
                    if (palette.length >= 9) {
                        return {
                            primary: [palette[2]].filter(Boolean), // Base color
                            secondary: [palette[5], palette[8]].filter(Boolean), // Triad colors
                            tertiary: palette.filter(
                                (_, i) => i !== 2 && i !== 5 && i !== 8
                            ), // Other colors
                        };
                    } else {
                        // Fallback for when we don't have enough colors
                        return {
                            primary: palette.slice(0, 1),
                            secondary: palette.slice(1, 3),
                            tertiary: palette.slice(3),
                        };
                    }
                } else {
                    // Make sure we have enough colors in the palette
                    if (palette.length >= 5) {
                        return {
                            primary: [palette[1]].filter(Boolean), // Base color
                            secondary: [palette[2], palette[3]].filter(Boolean), // Triad colors
                            tertiary: [palette[0], palette[4]].filter(Boolean), // Light and dark shades
                        };
                    } else {
                        // Fallback for when we don't have enough colors
                        return {
                            primary: palette.slice(0, 1),
                            secondary: palette.slice(1, 3),
                            tertiary: palette.slice(3),
                        };
                    }
                }

            case COLOR_SCHEMES.TETRADIC:
            case COLOR_SCHEMES.SQUARE:
                if (expandedPalette) {
                    // Make sure we have enough colors in the palette
                    if (palette.length >= 8) {
                        // For expanded palettes, pick the main colors as primary/secondary
                        return {
                            primary: [palette[1]].filter(Boolean), // Base color
                            secondary: [palette[4], palette[7]].filter(Boolean), // Two of the tetrad/square colors
                            tertiary: palette.filter(
                                (_, i) => i !== 1 && i !== 4 && i !== 7
                            ), // Other colors
                        };
                    } else {
                        // Fallback for when we don't have enough colors
                        return {
                            primary: palette.slice(0, 1),
                            secondary: palette.slice(1, 3),
                            tertiary: palette.slice(3),
                        };
                    }
                } else {
                    // Make sure we have enough colors in the palette
                    if (palette.length >= 5) {
                        return {
                            primary: [palette[0]].filter(Boolean), // Base color
                            secondary: [palette[1], palette[2]].filter(Boolean), // Two of the tetrad/square colors
                            tertiary: [palette[3], palette[4]].filter(Boolean), // Remaining colors
                        };
                    } else {
                        // Fallback for when we don't have enough colors
                        return {
                            primary: palette.slice(0, 1),
                            secondary: palette.slice(
                                1,
                                Math.min(3, palette.length)
                            ),
                            tertiary: palette.slice(
                                Math.min(3, palette.length)
                            ),
                        };
                    }
                }

            case COLOR_SCHEMES.SPLIT_COMPLEMENTARY:
                if (expandedPalette) {
                    // Make sure we have enough colors in the palette
                    if (palette.length >= 5) {
                        return {
                            primary: [palette[1]].filter(Boolean), // Base color
                            secondary: [palette[3], palette[4]].filter(Boolean), // Split complements
                            tertiary: palette.filter(
                                (_, i) => i !== 1 && i !== 3 && i !== 4
                            ), // Other colors
                        };
                    } else {
                        // Fallback for when we don't have enough colors
                        return {
                            primary: palette.slice(0, 1),
                            secondary: palette.slice(
                                1,
                                Math.min(3, palette.length)
                            ),
                            tertiary: palette.slice(
                                Math.min(3, palette.length)
                            ),
                        };
                    }
                } else {
                    // Make sure we have enough colors in the palette
                    if (palette.length >= 5) {
                        return {
                            primary: [palette[1]].filter(Boolean), // Base color
                            secondary: [palette[3], palette[4]].filter(Boolean), // Split complements
                            tertiary: [palette[0], palette[2]].filter(Boolean), // Light and dark base
                        };
                    } else {
                        // Fallback for when we don't have enough colors
                        return {
                            primary: palette.slice(0, 1),
                            secondary: palette.slice(
                                1,
                                Math.min(3, palette.length)
                            ),
                            tertiary: palette.slice(
                                Math.min(3, palette.length)
                            ),
                        };
                    }
                }

            default:
                // Default categorization for any other scheme
                const third = Math.ceil(palette.length / 3);
                return {
                    primary: palette.slice(0, third),
                    secondary: palette.slice(third, third * 2),
                    tertiary: palette.slice(third * 2),
                };
        }
    };

    // Format a color based on the selected export format and category
    const formatColor = (color, index, category) => {
        const prefix = category ? `${category}-` : "color-";

        switch (exportFormat) {
            case EXPORT_FORMATS.RGB:
                return color.rgb;
            case EXPORT_FORMATS.HSL:
                return color.hsl;
            case EXPORT_FORMATS.CSS_VARS:
                return `--${prefix}${index + 1}: ${color.hex};`;
            case EXPORT_FORMATS.TAILWIND:
                return `'${prefix}${index + 1}': '${color.hex}',`;
            default:
                return color.hex;
        }
    };

    // Generate the export code with categories
    const generateExportCode = () => {
        if (palette.length === 0) return "";

        const categories = categorizeColors();
        const baseColors = getBaseColors(categories.primary);

        // Generate base colors section if we have base colors that are different from primary colors
        const shouldShowBaseColors = baseColors.length > 0;

        const baseColorsSection = shouldShowBaseColors
            ? `/* Base Colors (Your Input) */\n${baseColors
                  .map((color, index) => formatColor(color, index, "base"))
                  .join("\n")}\n\n`
            : "";

        const baseColorsCssVars = shouldShowBaseColors
            ? `  /* Base Colors (Your Input) */\n  ${baseColors
                  .map((color, index) => formatColor(color, index, "base"))
                  .join("\n  ")}\n\n`
            : "";

        const baseColorsTailwind = shouldShowBaseColors
            ? `        /* Base Colors (Your Input) */\n        ${baseColors
                  .map((color, index) => formatColor(color, index, "base"))
                  .join("\n        ")}\n        \n`
            : "";

        switch (exportFormat) {
            case EXPORT_FORMATS.CSS_VARS:
                return `:root {\n${baseColorsCssVars}  /* Primary Colors */\n  ${categories.primary
                    .map((color, index) => formatColor(color, index, "primary"))
                    .join(
                        "\n  "
                    )}\n\n  /* Secondary Colors */\n  ${categories.secondary
                    .map((color, index) =>
                        formatColor(color, index, "secondary")
                    )
                    .join(
                        "\n  "
                    )}\n\n  /* Tertiary Colors */\n  ${categories.tertiary
                    .map((color, index) =>
                        formatColor(color, index, "tertiary")
                    )
                    .join("\n  ")}\n}`;

            case EXPORT_FORMATS.TAILWIND:
                return `module.exports = {\n  theme: {\n    extend: {\n      colors: {\n${baseColorsTailwind}        /* Primary Colors */\n        ${categories.primary
                    .map((color, index) => formatColor(color, index, "primary"))
                    .join(
                        "\n        "
                    )}\n        \n        /* Secondary Colors */\n        ${categories.secondary
                    .map((color, index) =>
                        formatColor(color, index, "secondary")
                    )
                    .join(
                        "\n        "
                    )}\n        \n        /* Tertiary Colors */\n        ${categories.tertiary
                    .map((color, index) =>
                        formatColor(color, index, "tertiary")
                    )
                    .join("\n        ")}\n      }\n    }\n  }\n}`;

            default:
                return `${baseColorsSection}/* Primary Colors */\n${categories.primary
                    .map((color, index) => formatColor(color, index, "primary"))
                    .join(
                        "\n"
                    )}\n\n/* Secondary Colors */\n${categories.secondary
                    .map((color, index) =>
                        formatColor(color, index, "secondary")
                    )
                    .join("\n")}\n\n/* Tertiary Colors */\n${categories.tertiary
                    .map((color, index) =>
                        formatColor(color, index, "tertiary")
                    )
                    .join("\n")}`;
        }
    };

    // Copy a single color to clipboard
    const copyColor = (color, index, category = null) => {
        if (!isBrowser) return;

        try {
            navigator.clipboard
                .writeText(formatColor(color, index, category))
                .then(() => {
                    setCopiedIndex(`${category || "color"}-${index}`);
                    setTimeout(() => setCopiedIndex(null), 1500);
                })
                .catch((err) => {
                    console.error("Failed to copy: ", err);
                });
        } catch (err) {
            console.error("Clipboard API not available: ", err);
        }
    };

    // Copy all colors to clipboard
    const copyAllColors = () => {
        if (!isBrowser) return;

        try {
            navigator.clipboard
                .writeText(generateExportCode())
                .then(() => {
                    setCopiedAll(true);
                    setTimeout(() => setCopiedAll(false), 1500);
                })
                .catch((err) => {
                    console.error("Failed to copy: ", err);
                });
        } catch (err) {
            console.error("Clipboard API not available: ", err);
        }
    };

    return (
        <ToolContainer
            className="py-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-colors duration-200"
            containerWidth={toolData?.containerWidth || "default"}>
            <ToolHeader
                toolData={toolData}
                defaultTitle="Color Palette Generator"
                defaultIcon="Palette"
                defaultDescription="Create harmonious color palettes for your web projects with our intuitive color palette generator."
            />

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Controls Section */}
                <div>
                    <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                        Controls
                    </h2>

                    {/* Base Colors Section */}
                    <div className="mb-6 bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <div className="flex justify-between items-center mb-2">
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Base Colors
                            </label>
                            <div
                                onClick={addBaseColor}
                                className="text-sm text-primary dark:text-primary-dark flex items-center cursor-pointer">
                                <LucideIcon
                                    name="Plus"
                                    size={16}
                                    className="mr-1"
                                />
                                Add Color
                            </div>
                        </div>

                        {/* Color List */}
                        <div className="space-y-3 mb-3">
                            {baseColors.map((color) => (
                                <div
                                    key={color.id}
                                    className="flex items-center">
                                    <div
                                        className={`w-10 h-10 rounded cursor-pointer border-2 ${
                                            activeColorId === color.id
                                                ? "border-primary dark:border-primary-dark"
                                                : "border-gray-300 dark:border-gray-600"
                                        } mr-3`}
                                        style={{ backgroundColor: color.value }}
                                        onClick={() =>
                                            setActiveColorId(color.id)
                                        }></div>
                                    <input
                                        type="text"
                                        value={color.value}
                                        onChange={(e) =>
                                            updateBaseColor(
                                                color.id,
                                                e.target.value
                                            )
                                        }
                                        className="flex-1 p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800"
                                    />
                                    {baseColors.length > 1 && (
                                        <div
                                            onClick={() =>
                                                removeBaseColor(color.id)
                                            }
                                            className="ml-2 text-gray-500 hover:text-red-500 dark:text-gray-400 dark:hover:text-red-400 cursor-pointer">
                                            <LucideIcon
                                                name="Trash2"
                                                size={16}
                                            />
                                        </div>
                                    )}
                                </div>
                            ))}
                        </div>

                        {/* Color Picker for Active Color */}
                        {showColorPicker && (
                            <div className="mt-2 p-2 bg-white dark:bg-gray-800 rounded shadow-lg">
                                <HexColorPicker
                                    color={getActiveColorValue()}
                                    onChange={(color) =>
                                        updateBaseColor(activeColorId, color)
                                    }
                                />
                                <div className="mt-2 flex justify-between">
                                    <span className="text-xs">
                                        {getActiveColorValue()}
                                    </span>
                                    <button
                                        className="text-xs text-primary dark:text-primary-dark"
                                        onClick={() =>
                                            setShowColorPicker(false)
                                        }>
                                        Close
                                    </button>
                                </div>
                            </div>
                        )}

                        {/* Color Picker Toggle Button */}
                        <div
                            onClick={() => setShowColorPicker(!showColorPicker)}
                            className="w-full mt-2 p-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 flex items-center justify-center cursor-pointer">
                            <LucideIcon
                                name={showColorPicker ? "EyeOff" : "Eye"}
                                size={16}
                                className="mr-2"
                            />
                            {showColorPicker
                                ? "Hide Color Picker"
                                : "Show Color Picker"}
                        </div>
                    </div>

                    {/* Color Scheme Selector */}
                    <div className="mb-6 bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Color Scheme
                        </label>
                        <select
                            value={colorScheme}
                            onChange={(e) => setColorScheme(e.target.value)}
                            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800">
                            <option value={COLOR_SCHEMES.MONOCHROMATIC}>
                                Monochromatic
                            </option>
                            <option value={COLOR_SCHEMES.ANALOGOUS}>
                                Analogous
                            </option>
                            <option value={COLOR_SCHEMES.COMPLEMENTARY}>
                                Complementary
                            </option>
                            <option value={COLOR_SCHEMES.TRIADIC}>
                                Triadic
                            </option>
                            <option value={COLOR_SCHEMES.TETRADIC}>
                                Tetradic
                            </option>
                            <option value={COLOR_SCHEMES.SPLIT_COMPLEMENTARY}>
                                Split Complementary
                            </option>
                            <option value={COLOR_SCHEMES.SQUARE}>Square</option>
                            <option value={COLOR_SCHEMES.CUSTOM}>
                                Custom (Use Base Colors Only)
                            </option>
                        </select>

                        {/* Expanded Palette Toggle */}
                        <div className="mt-3 flex items-center">
                            <input
                                type="checkbox"
                                id="expandedPalette"
                                checked={expandedPalette}
                                onChange={() =>
                                    setExpandedPalette(!expandedPalette)
                                }
                                className="mr-2 h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                            />
                            <label
                                htmlFor="expandedPalette"
                                className="text-sm text-gray-700 dark:text-gray-300">
                                Generate expanded palette (more colors)
                            </label>
                        </div>
                    </div>

                    {/* Export Format Selector */}
                    <div className="mb-6 bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Export Format
                        </label>
                        <select
                            value={exportFormat}
                            onChange={(e) => setExportFormat(e.target.value)}
                            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800">
                            <option value={EXPORT_FORMATS.HEX}>HEX</option>
                            <option value={EXPORT_FORMATS.RGB}>RGB</option>
                            <option value={EXPORT_FORMATS.HSL}>HSL</option>
                            <option value={EXPORT_FORMATS.CSS_VARS}>
                                CSS Variables
                            </option>
                            <option value={EXPORT_FORMATS.TAILWIND}>
                                Tailwind Config
                            </option>
                        </select>
                    </div>

                    {/* Export Code */}
                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <div className="flex justify-between items-center mb-2">
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Export Code
                            </label>
                            <div
                                onClick={copyAllColors}
                                className="text-sm text-primary dark:text-primary-dark flex items-center cursor-pointer">
                                {copiedAll ? (
                                    <>
                                        <LucideIcon
                                            name="Check"
                                            size={16}
                                            className="mr-1"
                                        />
                                        Copied!
                                    </>
                                ) : (
                                    <>
                                        <LucideIcon
                                            name="Copy"
                                            size={16}
                                            className="mr-1"
                                        />
                                        Copy All
                                    </>
                                )}
                            </div>
                        </div>
                        <pre className="bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 p-3 rounded text-sm overflow-x-auto max-h-60 overflow-y-auto border border-gray-200 dark:border-gray-700">
                            {generateExportCode()}
                        </pre>
                    </div>
                </div>

                {/* Preview Section */}
                <div>
                    <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                        Color Palette
                        <span className="text-sm font-normal ml-2 text-gray-500 dark:text-gray-400">
                            ({palette.length} colors)
                        </span>
                    </h2>

                    <div className="space-y-6 max-h-[calc(100vh-300px)] overflow-y-auto pr-2">
                        {/* Get categorized colors */}
                        {(() => {
                            const categories = categorizeColors();
                            return (
                                <>
                                    {/* Base Colors */}
                                    {(() => {
                                        const baseColors = getBaseColors(
                                            categories.primary
                                        );
                                        return baseColors.length > 0 ? (
                                            <div>
                                                <h3 className="text-lg font-semibold mb-3 text-gray-800 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-1">
                                                    Base Colors
                                                    <span className="text-sm font-normal ml-2 text-gray-500 dark:text-gray-400">
                                                        (Your Input)
                                                    </span>
                                                </h3>
                                                <div className="space-y-3">
                                                    {baseColors.map(
                                                        (color, index) => (
                                                            <div
                                                                key={`base-${index}`}
                                                                className="flex items-center">
                                                                <div
                                                                    className="w-16 h-16 rounded-lg mr-4 border-2 border-primary dark:border-primary-dark flex-shrink-0"
                                                                    style={{
                                                                        backgroundColor:
                                                                            color.hex,
                                                                    }}></div>
                                                                <div className="flex-1 min-w-0">
                                                                    <div className="font-mono text-sm mb-1 break-all">
                                                                        {formatColor(
                                                                            color,
                                                                            index,
                                                                            "base"
                                                                        )}
                                                                    </div>
                                                                    <div
                                                                        onClick={() =>
                                                                            copyColor(
                                                                                color,
                                                                                index,
                                                                                "base"
                                                                            )
                                                                        }
                                                                        className="text-sm text-primary dark:text-primary-dark flex items-center cursor-pointer">
                                                                        {copiedIndex ===
                                                                        `base-${index}` ? (
                                                                            <>
                                                                                <LucideIcon
                                                                                    name="Check"
                                                                                    size={
                                                                                        16
                                                                                    }
                                                                                    className="mr-1"
                                                                                />
                                                                                Copied!
                                                                            </>
                                                                        ) : (
                                                                            <>
                                                                                <LucideIcon
                                                                                    name="Copy"
                                                                                    size={
                                                                                        16
                                                                                    }
                                                                                    className="mr-1"
                                                                                />
                                                                                Copy
                                                                            </>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        )
                                                    )}
                                                </div>
                                            </div>
                                        ) : null;
                                    })()}

                                    {/* Primary Colors */}
                                    {categories.primary.length > 0 && (
                                        <div>
                                            <h3 className="text-lg font-semibold mb-3 text-gray-800 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-1">
                                                Primary Colors
                                            </h3>
                                            <div className="space-y-3">
                                                {categories.primary.map(
                                                    (color, index) => (
                                                        <div
                                                            key={`primary-${index}`}
                                                            className="flex items-center">
                                                            <div
                                                                className="w-16 h-16 rounded-lg mr-4 border border-gray-200 dark:border-gray-700 flex-shrink-0"
                                                                style={{
                                                                    backgroundColor:
                                                                        color.hex,
                                                                }}></div>
                                                            <div className="flex-1 min-w-0">
                                                                <div className="font-mono text-sm mb-1 break-all">
                                                                    {formatColor(
                                                                        color,
                                                                        index,
                                                                        "primary"
                                                                    )}
                                                                </div>
                                                                <div
                                                                    onClick={() =>
                                                                        copyColor(
                                                                            color,
                                                                            index,
                                                                            "primary"
                                                                        )
                                                                    }
                                                                    className="text-sm text-primary dark:text-primary-dark flex items-center cursor-pointer">
                                                                    {copiedIndex ===
                                                                    `primary-${index}` ? (
                                                                        <>
                                                                            <LucideIcon
                                                                                name="Check"
                                                                                size={
                                                                                    16
                                                                                }
                                                                                className="mr-1"
                                                                            />
                                                                            Copied!
                                                                        </>
                                                                    ) : (
                                                                        <>
                                                                            <LucideIcon
                                                                                name="Copy"
                                                                                size={
                                                                                    16
                                                                                }
                                                                                className="mr-1"
                                                                            />
                                                                            Copy
                                                                        </>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    )
                                                )}
                                            </div>
                                        </div>
                                    )}

                                    {/* Secondary Colors */}
                                    {categories.secondary.length > 0 && (
                                        <div>
                                            <h3 className="text-lg font-semibold mb-3 text-gray-800 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-1">
                                                Secondary Colors
                                                <span className="text-sm font-normal ml-2 text-gray-500 dark:text-gray-400">
                                                    (Generated)
                                                </span>
                                            </h3>
                                            <div className="space-y-3">
                                                {categories.secondary.map(
                                                    (color, index) => (
                                                        <div
                                                            key={`secondary-${index}`}
                                                            className="flex items-center">
                                                            <div
                                                                className="w-16 h-16 rounded-lg mr-4 border border-gray-200 dark:border-gray-700 flex-shrink-0"
                                                                style={{
                                                                    backgroundColor:
                                                                        color.hex,
                                                                }}></div>
                                                            <div className="flex-1 min-w-0">
                                                                <div className="font-mono text-sm mb-1 break-all">
                                                                    {formatColor(
                                                                        color,
                                                                        index,
                                                                        "secondary"
                                                                    )}
                                                                </div>
                                                                <div
                                                                    onClick={() =>
                                                                        copyColor(
                                                                            color,
                                                                            index,
                                                                            "secondary"
                                                                        )
                                                                    }
                                                                    className="text-sm text-primary dark:text-primary-dark flex items-center cursor-pointer">
                                                                    {copiedIndex ===
                                                                    `secondary-${index}` ? (
                                                                        <>
                                                                            <LucideIcon
                                                                                name="Check"
                                                                                size={
                                                                                    16
                                                                                }
                                                                                className="mr-1"
                                                                            />
                                                                            Copied!
                                                                        </>
                                                                    ) : (
                                                                        <>
                                                                            <LucideIcon
                                                                                name="Copy"
                                                                                size={
                                                                                    16
                                                                                }
                                                                                className="mr-1"
                                                                            />
                                                                            Copy
                                                                        </>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    )
                                                )}
                                            </div>
                                        </div>
                                    )}

                                    {/* Tertiary Colors */}
                                    {categories.tertiary.length > 0 && (
                                        <div>
                                            <h3 className="text-lg font-semibold mb-3 text-gray-800 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-1">
                                                Tertiary Colors
                                                <span className="text-sm font-normal ml-2 text-gray-500 dark:text-gray-400">
                                                    (Generated)
                                                </span>
                                            </h3>
                                            <div className="space-y-3">
                                                {categories.tertiary.map(
                                                    (color, index) => (
                                                        <div
                                                            key={`tertiary-${index}`}
                                                            className="flex items-center">
                                                            <div
                                                                className="w-16 h-16 rounded-lg mr-4 border border-gray-200 dark:border-gray-700 flex-shrink-0"
                                                                style={{
                                                                    backgroundColor:
                                                                        color.hex,
                                                                }}></div>
                                                            <div className="flex-1 min-w-0">
                                                                <div className="font-mono text-sm mb-1 break-all">
                                                                    {formatColor(
                                                                        color,
                                                                        index,
                                                                        "tertiary"
                                                                    )}
                                                                </div>
                                                                <div
                                                                    onClick={() =>
                                                                        copyColor(
                                                                            color,
                                                                            index,
                                                                            "tertiary"
                                                                        )
                                                                    }
                                                                    className="text-sm text-primary dark:text-primary-dark flex items-center cursor-pointer">
                                                                    {copiedIndex ===
                                                                    `tertiary-${index}` ? (
                                                                        <>
                                                                            <LucideIcon
                                                                                name="Check"
                                                                                size={
                                                                                    16
                                                                                }
                                                                                className="mr-1"
                                                                            />
                                                                            Copied!
                                                                        </>
                                                                    ) : (
                                                                        <>
                                                                            <LucideIcon
                                                                                name="Copy"
                                                                                size={
                                                                                    16
                                                                                }
                                                                                className="mr-1"
                                                                            />
                                                                            Copy
                                                                        </>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    )
                                                )}
                                            </div>
                                        </div>
                                    )}
                                </>
                            );
                        })()}
                    </div>

                    {/* Color Palette Preview Grid */}
                    <div className="mt-8">
                        <h3 className="text-lg font-semibold mb-3 text-gray-800 dark:text-white">
                            Quick Preview
                        </h3>

                        {/* Get categorized colors for the grid */}
                        {(() => {
                            const categories = categorizeColors();
                            return (
                                <div className="space-y-4">
                                    {/* Base Colors Grid */}
                                    {(() => {
                                        const baseColors = getBaseColors(
                                            categories.primary
                                        );
                                        return baseColors.length > 0 ? (
                                            <div>
                                                <h4 className="text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                                                    Base Colors (Your Input)
                                                </h4>
                                                <div className="grid grid-cols-5 sm:grid-cols-8 gap-2">
                                                    {baseColors.map(
                                                        (color, index) => (
                                                            <div
                                                                key={`grid-base-${index}`}
                                                                className="aspect-square rounded border-2 border-primary dark:border-primary-dark cursor-pointer hover:scale-110 transition-transform"
                                                                style={{
                                                                    backgroundColor:
                                                                        color.hex,
                                                                }}
                                                                onClick={() =>
                                                                    copyColor(
                                                                        color,
                                                                        index,
                                                                        "base"
                                                                    )
                                                                }
                                                                title={`${
                                                                    color.hex
                                                                } (Base ${
                                                                    index + 1
                                                                })`}></div>
                                                        )
                                                    )}
                                                </div>
                                            </div>
                                        ) : null;
                                    })()}

                                    {/* Primary Colors Grid */}
                                    {categories.primary.length > 0 && (
                                        <div>
                                            <h4 className="text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                                                Primary Colors
                                            </h4>
                                            <div className="grid grid-cols-5 sm:grid-cols-8 gap-2">
                                                {categories.primary.map(
                                                    (color, index) => (
                                                        <div
                                                            key={`grid-primary-${index}`}
                                                            className="aspect-square rounded border border-gray-200 dark:border-gray-700 cursor-pointer hover:scale-110 transition-transform"
                                                            style={{
                                                                backgroundColor:
                                                                    color.hex,
                                                            }}
                                                            onClick={() =>
                                                                copyColor(
                                                                    color,
                                                                    index,
                                                                    "primary"
                                                                )
                                                            }
                                                            title={`${
                                                                color.hex
                                                            } (Primary ${
                                                                index + 1
                                                            })`}></div>
                                                    )
                                                )}
                                            </div>
                                        </div>
                                    )}

                                    {/* Secondary Colors Grid */}
                                    {categories.secondary.length > 0 && (
                                        <div>
                                            <h4 className="text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                                                Secondary (Generated)
                                            </h4>
                                            <div className="grid grid-cols-5 sm:grid-cols-8 gap-2">
                                                {categories.secondary.map(
                                                    (color, index) => (
                                                        <div
                                                            key={`grid-secondary-${index}`}
                                                            className="aspect-square rounded border border-gray-200 dark:border-gray-700 cursor-pointer hover:scale-110 transition-transform"
                                                            style={{
                                                                backgroundColor:
                                                                    color.hex,
                                                            }}
                                                            onClick={() =>
                                                                copyColor(
                                                                    color,
                                                                    index,
                                                                    "secondary"
                                                                )
                                                            }
                                                            title={`${
                                                                color.hex
                                                            } (Secondary ${
                                                                index + 1
                                                            })`}></div>
                                                    )
                                                )}
                                            </div>
                                        </div>
                                    )}

                                    {/* Tertiary Colors Grid */}
                                    {categories.tertiary.length > 0 && (
                                        <div>
                                            <h4 className="text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                                                Tertiary (Generated)
                                            </h4>
                                            <div className="grid grid-cols-5 sm:grid-cols-8 gap-2">
                                                {categories.tertiary.map(
                                                    (color, index) => (
                                                        <div
                                                            key={`grid-tertiary-${index}`}
                                                            className="aspect-square rounded border border-gray-200 dark:border-gray-700 cursor-pointer hover:scale-110 transition-transform"
                                                            style={{
                                                                backgroundColor:
                                                                    color.hex,
                                                            }}
                                                            onClick={() =>
                                                                copyColor(
                                                                    color,
                                                                    index,
                                                                    "tertiary"
                                                                )
                                                            }
                                                            title={`${
                                                                color.hex
                                                            } (Tertiary ${
                                                                index + 1
                                                            })`}></div>
                                                    )
                                                )}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            );
                        })()}
                    </div>
                </div>
            </div>
        </ToolContainer>
    );
}
