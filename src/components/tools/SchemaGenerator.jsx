import { useState, useEffect } from "react";
import { getToolById } from "../../data/tools.js";
import ToolHeader from "../ui/ToolHeader.jsx";
import ToolContainer from "../ui/ToolContainer.jsx";
import LucideIcon from "../ui/LucideIcon.jsx";

// Check if we're in the browser environment
const isBrowser = typeof window !== "undefined";

export default function SchemaGenerator() {
    const [toolData, setToolData] = useState(null);
    const [schemaType, setSchemaType] = useState("Organization");
    const [formData, setFormData] = useState({});
    const [generatedSchema, setGeneratedSchema] = useState("");
    const [copied, setCopied] = useState(false);
    const [activeTab, setActiveTab] = useState("form");
    const [validationErrors, setValidationErrors] = useState({});

    // Schema types available in the tool
    const schemaTypes = [
        {
            id: "Organization",
            name: "Organization",
            description: "Business, NGO, sports team, etc.",
        },
        {
            id: "LocalBusiness",
            name: "Local Business",
            description: "Physical business or branch with local address",
        },
        { id: "Person", name: "Person", description: "An individual" },
        { id: "Product", name: "Product", description: "A product or service" },
        {
            id: "Article",
            name: "Article",
            description: "News article, blog post, etc.",
        },
        {
            id: "Event",
            name: "Event",
            description: "Concert, conference, festival, etc.",
        },
        { id: "Recipe", name: "Recipe", description: "Food or drink recipe" },
        {
            id: "FAQPage",
            name: "FAQ Page",
            description: "Page with a list of Frequently Asked Questions",
        },
    ];

    useEffect(() => {
        // Get the tool data
        const data = getToolById("schemagenerator");
        setToolData(data);

        // Initialize form data based on default schema type
        resetFormData("Organization");
    }, []);

    // Sample data for each schema type
    const getSampleData = (type) => {
        switch (type) {
            case "Organization":
                return {
                    name: "DevBottle",
                    url: "https://www.devbottle.com",
                    logo: "https://www.devbottle.com/images/logo.png",
                    description:
                        "DevBottle provides essential tools for web developers to streamline their workflow and improve productivity.",
                    sameAs: [
                        "https://twitter.com/devbottle",
                        "https://github.com/devbottle",
                        "https://www.linkedin.com/company/devbottle",
                    ],
                };
            case "LocalBusiness":
                return {
                    name: "DevBottle Café",
                    url: "https://www.devbottlecafe.com",
                    logo: "https://www.devbottlecafe.com/images/logo.png",
                    description:
                        "A cozy café where developers can work, collaborate, and enjoy great coffee.",
                    telephone: "******-123-4567",
                    address: {
                        streetAddress: "123 Code Avenue",
                        addressLocality: "San Francisco",
                        addressRegion: "CA",
                        postalCode: "94107",
                        addressCountry: "US",
                    },
                    openingHours: ["Mo-Fr 08:00-20:00", "Sa-Su 10:00-18:00"],
                };
            case "Person":
                return {
                    name: "Jane Developer",
                    jobTitle: "Senior Software Engineer",
                    url: "https://www.janedeveloper.com",
                    image: "https://www.janedeveloper.com/images/profile.jpg",
                    sameAs: [
                        "https://twitter.com/janedeveloper",
                        "https://github.com/janedeveloper",
                        "https://www.linkedin.com/in/janedeveloper",
                    ],
                };
            case "Product":
                return {
                    name: "DevBottle Pro",
                    description:
                        "The ultimate developer toolkit with premium features for professional web developers.",
                    image: "https://www.devbottle.com/images/devbottle-pro.jpg",
                    brand: "DevBottle",
                    offers: {
                        price: "99.99",
                        priceCurrency: "USD",
                        availability: "InStock",
                    },
                };
            case "Article":
                return {
                    headline: "10 Essential Web Development Tools for 2025",
                    description:
                        "Discover the must-have tools that will revolutionize your web development workflow in 2025.",
                    image: "https://www.devbottle.com/images/web-dev-tools-2025.jpg",
                    author: {
                        name: "Jane Developer",
                        url: "https://www.devbottle.com/authors/jane-developer",
                    },
                    publisher: {
                        name: "DevBottle",
                        logo: "https://www.devbottle.com/images/logo.png",
                    },
                    datePublished: "2025-01-15",
                };
            case "Event":
                return {
                    name: "DevBottle Conference 2025",
                    description:
                        "The premier conference for web developers featuring workshops, talks, and networking opportunities.",
                    image: "https://www.devbottle.com/images/conference-2025.jpg",
                    startDate: "2025-06-15T09:00",
                    endDate: "2025-06-17T18:00",
                    location: {
                        name: "Tech Convention Center",
                        address: {
                            streetAddress: "456 Innovation Boulevard",
                            addressLocality: "San Francisco",
                            addressRegion: "CA",
                            postalCode: "94107",
                            addressCountry: "US",
                        },
                    },
                };
            case "Recipe":
                return {
                    name: "Developer's Coffee",
                    description:
                        "The perfect coffee recipe to fuel your coding sessions.",
                    image: "https://www.devbottle.com/images/developer-coffee.jpg",
                    author: {
                        name: "Jane Developer",
                    },
                    recipeIngredient: [
                        "2 tablespoons freshly ground coffee beans",
                        "8 ounces hot water",
                        "1 tablespoon coconut oil",
                        "1 teaspoon honey",
                    ],
                    recipeInstructions: [
                        "Brew coffee using your preferred method.",
                        "Add coconut oil and honey to the hot coffee.",
                        "Blend for 30 seconds until frothy.",
                        "Enjoy while coding!",
                    ],
                    cookTime: "PT5M",
                    prepTime: "PT2M",
                };
            case "FAQPage":
                return {
                    mainEntity: [
                        {
                            question: "What is structured data?",
                            answer: "Structured data is a standardized format for providing information about a page and classifying its content. Search engines like Google use structured data to understand the content of your page and to display it in a special way in search results, known as rich results.",
                        },
                        {
                            question:
                                "Why should I use structured data on my website?",
                            answer: "Using structured data can help search engines better understand your content, potentially leading to enhanced search results with rich snippets, which can improve click-through rates. It also helps your content appear in specialized search features like knowledge panels, carousels, and voice search results.",
                        },
                        {
                            question:
                                "How do I implement structured data on my website?",
                            answer: "You can implement structured data using JSON-LD (recommended by Google), Microdata, or RDFa formats. Simply generate the appropriate markup using this tool, then add it to the HTML of your webpage, preferably in the <head> section when using JSON-LD.",
                        },
                    ],
                };
            default:
                return {};
        }
    };

    // Reset form data when schema type changes
    const resetFormData = (type) => {
        let initialData = {};

        switch (type) {
            case "Organization":
                initialData = {
                    name: "",
                    url: "",
                    logo: "",
                    description: "",
                    sameAs: [""],
                };
                break;
            case "LocalBusiness":
                initialData = {
                    name: "",
                    url: "",
                    logo: "",
                    description: "",
                    telephone: "",
                    address: {
                        streetAddress: "",
                        addressLocality: "",
                        addressRegion: "",
                        postalCode: "",
                        addressCountry: "",
                    },
                    openingHours: [""],
                };
                break;
            case "Person":
                initialData = {
                    name: "",
                    jobTitle: "",
                    url: "",
                    image: "",
                    sameAs: [""],
                };
                break;
            case "Product":
                initialData = {
                    name: "",
                    description: "",
                    image: "",
                    brand: "",
                    offers: {
                        price: "",
                        priceCurrency: "USD",
                        availability: "InStock",
                    },
                };
                break;
            case "Article":
                initialData = {
                    headline: "",
                    description: "",
                    image: "",
                    author: {
                        name: "",
                        url: "",
                    },
                    publisher: {
                        name: "",
                        logo: "",
                    },
                    datePublished: "",
                };
                break;
            case "Event":
                initialData = {
                    name: "",
                    description: "",
                    image: "",
                    startDate: "",
                    endDate: "",
                    location: {
                        name: "",
                        address: {
                            streetAddress: "",
                            addressLocality: "",
                            addressRegion: "",
                            postalCode: "",
                            addressCountry: "",
                        },
                    },
                };
                break;
            case "Recipe":
                initialData = {
                    name: "",
                    description: "",
                    image: "",
                    author: {
                        name: "",
                    },
                    recipeIngredient: [""],
                    recipeInstructions: [""],
                    cookTime: "",
                    prepTime: "",
                };
                break;
            case "FAQPage":
                initialData = {
                    mainEntity: [
                        {
                            question: "",
                            answer: "",
                        },
                    ],
                };
                break;
            default:
                initialData = {};
        }

        setFormData(initialData);
        setGeneratedSchema("");
        setValidationErrors({});
    };

    // Load sample data for the current schema type
    const loadSample = () => {
        const sampleData = getSampleData(schemaType);
        setFormData(sampleData);
        setValidationErrors({});
    };

    // Handle schema type change
    const handleSchemaTypeChange = (e) => {
        const newType = e.target.value;
        setSchemaType(newType);
        resetFormData(newType);
    };

    // Handle form field changes
    const handleInputChange = (e, field, nestedField = null, index = null) => {
        const { value } = e.target;

        setFormData((prevData) => {
            const newData = { ...prevData };

            if (nestedField && index !== null) {
                // Handle array of objects (like FAQ questions)
                newData[field][index][nestedField] = value;
            } else if (nestedField) {
                // Handle nested objects (like address)
                newData[field] = { ...newData[field], [nestedField]: value };
            } else if (index !== null) {
                // Handle arrays (like sameAs)
                newData[field][index] = value;
            } else {
                // Handle simple fields
                newData[field] = value;
            }

            return newData;
        });
    };

    // Add item to array fields
    const addArrayItem = (field, defaultValue = "", nestedObject = null) => {
        setFormData((prevData) => {
            const newData = { ...prevData };

            if (nestedObject) {
                // Add item to array of objects (like FAQ questions)
                newData[field] = [...newData[field], { ...nestedObject }];
            } else {
                // Add item to simple array (like sameAs)
                newData[field] = [...newData[field], defaultValue];
            }

            return newData;
        });
    };

    // Remove item from array fields
    const removeArrayItem = (field, index) => {
        setFormData((prevData) => {
            const newData = { ...prevData };
            newData[field] = newData[field].filter((_, i) => i !== index);
            return newData;
        });
    };

    // Validate form data
    const validateForm = () => {
        const errors = {};

        // Common validation for all schema types
        if (schemaType === "Organization" || schemaType === "LocalBusiness") {
            if (!formData.name) errors.name = "Name is required";
            if (!formData.url) errors.url = "URL is required";
        } else if (schemaType === "Person") {
            if (!formData.name) errors.name = "Name is required";
        } else if (schemaType === "Product") {
            if (!formData.name) errors.name = "Name is required";
            if (!formData.description)
                errors.description = "Description is required";
            if (formData.offers && !formData.offers.price)
                errors.price = "Price is required";
        } else if (schemaType === "Article") {
            if (!formData.headline) errors.headline = "Headline is required";
            if (!formData.description)
                errors.description = "Description is required";
        } else if (schemaType === "Event") {
            if (!formData.name) errors.name = "Name is required";
            if (!formData.startDate)
                errors.startDate = "Start date is required";
        } else if (schemaType === "Recipe") {
            if (!formData.name) errors.name = "Name is required";
            if (
                !formData.recipeIngredient ||
                formData.recipeIngredient.length === 0
            ) {
                errors.recipeIngredient = "At least one ingredient is required";
            }
            if (
                !formData.recipeInstructions ||
                formData.recipeInstructions.length === 0
            ) {
                errors.recipeInstructions =
                    "At least one instruction is required";
            }
        } else if (schemaType === "FAQPage") {
            if (!formData.mainEntity || formData.mainEntity.length === 0) {
                errors.mainEntity = "At least one FAQ item is required";
            } else {
                formData.mainEntity.forEach((item, index) => {
                    if (!item.question) {
                        errors[`question_${index}`] = "Question is required";
                    }
                    if (!item.answer) {
                        errors[`answer_${index}`] = "Answer is required";
                    }
                });
            }
        }

        setValidationErrors(errors);
        return Object.keys(errors).length === 0;
    };

    // Render form based on selected schema type
    const renderForm = () => {
        return (
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 mb-6">
                {schemaType === "Organization" && (
                    <div className="space-y-6">
                        <div>
                            <label
                                htmlFor="name"
                                className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Organization Name{" "}
                                <span className="text-red-500">*</span>
                            </label>
                            <input
                                id="name"
                                type="text"
                                value={formData.name || ""}
                                onChange={(e) => handleInputChange(e, "name")}
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                placeholder="e.g., Acme Corporation"
                            />
                            {validationErrors.name && (
                                <p className="mt-1 text-red-500 text-sm">
                                    {validationErrors.name}
                                </p>
                            )}
                        </div>

                        <div>
                            <label
                                htmlFor="url"
                                className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Website URL{" "}
                                <span className="text-red-500">*</span>
                            </label>
                            <input
                                id="url"
                                type="url"
                                value={formData.url || ""}
                                onChange={(e) => handleInputChange(e, "url")}
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                placeholder="e.g., https://www.example.com"
                            />
                            {validationErrors.url && (
                                <p className="mt-1 text-red-500 text-sm">
                                    {validationErrors.url}
                                </p>
                            )}
                        </div>

                        <div>
                            <label
                                htmlFor="logo"
                                className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Logo URL
                            </label>
                            <input
                                id="logo"
                                type="url"
                                value={formData.logo || ""}
                                onChange={(e) => handleInputChange(e, "logo")}
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                placeholder="e.g., https://www.example.com/logo.png"
                            />
                            <p className="mt-1 text-gray-500 dark:text-gray-400 text-sm">
                                URL to your organization's logo (minimum
                                112x112px, .jpg, .png, or .gif format)
                            </p>
                        </div>

                        <div>
                            <label
                                htmlFor="description"
                                className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Description
                            </label>
                            <textarea
                                id="description"
                                value={formData.description || ""}
                                onChange={(e) =>
                                    handleInputChange(e, "description")
                                }
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                rows="3"
                                placeholder="e.g., Acme Corporation is a leading provider of..."></textarea>
                        </div>

                        <div>
                            <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Social Profiles
                            </label>
                            {formData.sameAs &&
                                formData.sameAs.map((url, index) => (
                                    <div
                                        key={index}
                                        className="flex items-center mb-2">
                                        <input
                                            type="url"
                                            value={url}
                                            onChange={(e) =>
                                                handleInputChange(
                                                    e,
                                                    "sameAs",
                                                    null,
                                                    index
                                                )
                                            }
                                            className="flex-grow p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                            placeholder="e.g., https://www.facebook.com/example"
                                        />
                                        <button
                                            type="button"
                                            onClick={() =>
                                                removeArrayItem("sameAs", index)
                                            }
                                            className="ml-2 p-2 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                                            aria-label="Remove social profile">
                                            <LucideIcon
                                                name="Trash2"
                                                size={20}
                                            />
                                        </button>
                                    </div>
                                ))}
                            <button
                                type="button"
                                onClick={() => addArrayItem("sameAs")}
                                className="mt-2 text-primary dark:text-primary-dark hover:text-primary/80 dark:hover:text-primary-dark/80 flex items-center">
                                <LucideIcon
                                    name="Plus"
                                    size={18}
                                    className="mr-1"
                                />
                                Add Social Profile
                            </button>
                            <p className="mt-1 text-gray-500 dark:text-gray-400 text-sm">
                                Add URLs to your social media profiles
                                (Facebook, Twitter, LinkedIn, etc.)
                            </p>
                        </div>
                    </div>
                )}

                {schemaType === "LocalBusiness" && (
                    <div className="space-y-6">
                        <div>
                            <label
                                htmlFor="name"
                                className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Business Name{" "}
                                <span className="text-red-500">*</span>
                            </label>
                            <input
                                id="name"
                                type="text"
                                value={formData.name || ""}
                                onChange={(e) => handleInputChange(e, "name")}
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                placeholder="e.g., Joe's Pizza"
                            />
                            {validationErrors.name && (
                                <p className="mt-1 text-red-500 text-sm">
                                    {validationErrors.name}
                                </p>
                            )}
                        </div>

                        <div>
                            <label
                                htmlFor="url"
                                className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Website URL{" "}
                                <span className="text-red-500">*</span>
                            </label>
                            <input
                                id="url"
                                type="url"
                                value={formData.url || ""}
                                onChange={(e) => handleInputChange(e, "url")}
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                placeholder="e.g., https://www.joespizza.com"
                            />
                            {validationErrors.url && (
                                <p className="mt-1 text-red-500 text-sm">
                                    {validationErrors.url}
                                </p>
                            )}
                        </div>

                        <div>
                            <label
                                htmlFor="telephone"
                                className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Telephone
                            </label>
                            <input
                                id="telephone"
                                type="tel"
                                value={formData.telephone || ""}
                                onChange={(e) =>
                                    handleInputChange(e, "telephone")
                                }
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                placeholder="e.g., ******-555-1234"
                            />
                        </div>

                        <div>
                            <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Address
                            </label>
                            <div className="space-y-3 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                                <div>
                                    <label
                                        htmlFor="streetAddress"
                                        className="block text-gray-700 dark:text-gray-300 mb-1 text-sm">
                                        Street Address
                                    </label>
                                    <input
                                        id="streetAddress"
                                        type="text"
                                        value={
                                            formData.address?.streetAddress ||
                                            ""
                                        }
                                        onChange={(e) =>
                                            handleInputChange(
                                                e,
                                                "address",
                                                "streetAddress"
                                            )
                                        }
                                        className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                        placeholder="e.g., 123 Main St"
                                    />
                                </div>
                                <div>
                                    <label
                                        htmlFor="addressLocality"
                                        className="block text-gray-700 dark:text-gray-300 mb-1 text-sm">
                                        City
                                    </label>
                                    <input
                                        id="addressLocality"
                                        type="text"
                                        value={
                                            formData.address?.addressLocality ||
                                            ""
                                        }
                                        onChange={(e) =>
                                            handleInputChange(
                                                e,
                                                "address",
                                                "addressLocality"
                                            )
                                        }
                                        className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                        placeholder="e.g., New York"
                                    />
                                </div>
                                <div>
                                    <label
                                        htmlFor="addressRegion"
                                        className="block text-gray-700 dark:text-gray-300 mb-1 text-sm">
                                        State/Province
                                    </label>
                                    <input
                                        id="addressRegion"
                                        type="text"
                                        value={
                                            formData.address?.addressRegion ||
                                            ""
                                        }
                                        onChange={(e) =>
                                            handleInputChange(
                                                e,
                                                "address",
                                                "addressRegion"
                                            )
                                        }
                                        className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                        placeholder="e.g., NY"
                                    />
                                </div>
                                <div>
                                    <label
                                        htmlFor="postalCode"
                                        className="block text-gray-700 dark:text-gray-300 mb-1 text-sm">
                                        Postal Code
                                    </label>
                                    <input
                                        id="postalCode"
                                        type="text"
                                        value={
                                            formData.address?.postalCode || ""
                                        }
                                        onChange={(e) =>
                                            handleInputChange(
                                                e,
                                                "address",
                                                "postalCode"
                                            )
                                        }
                                        className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                        placeholder="e.g., 10001"
                                    />
                                </div>
                                <div>
                                    <label
                                        htmlFor="addressCountry"
                                        className="block text-gray-700 dark:text-gray-300 mb-1 text-sm">
                                        Country
                                    </label>
                                    <input
                                        id="addressCountry"
                                        type="text"
                                        value={
                                            formData.address?.addressCountry ||
                                            ""
                                        }
                                        onChange={(e) =>
                                            handleInputChange(
                                                e,
                                                "address",
                                                "addressCountry"
                                            )
                                        }
                                        className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                        placeholder="e.g., US"
                                    />
                                </div>
                            </div>
                        </div>

                        <div>
                            <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Opening Hours
                            </label>
                            {formData.openingHours &&
                                formData.openingHours.map((hours, index) => (
                                    <div
                                        key={index}
                                        className="flex items-center mb-2">
                                        <input
                                            type="text"
                                            value={hours}
                                            onChange={(e) =>
                                                handleInputChange(
                                                    e,
                                                    "openingHours",
                                                    null,
                                                    index
                                                )
                                            }
                                            className="flex-grow p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                            placeholder="e.g., Mo-Fr 09:00-17:00"
                                        />
                                        <button
                                            type="button"
                                            onClick={() =>
                                                removeArrayItem(
                                                    "openingHours",
                                                    index
                                                )
                                            }
                                            className="ml-2 p-2 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                                            aria-label="Remove opening hours">
                                            <LucideIcon
                                                name="Trash2"
                                                size={20}
                                            />
                                        </button>
                                    </div>
                                ))}
                            <button
                                type="button"
                                onClick={() => addArrayItem("openingHours")}
                                className="mt-2 text-primary dark:text-primary-dark hover:text-primary/80 dark:hover:text-primary-dark/80 flex items-center">
                                <LucideIcon
                                    name="Plus"
                                    size={18}
                                    className="mr-1"
                                />
                                Add Opening Hours
                            </button>
                            <p className="mt-1 text-gray-500 dark:text-gray-400 text-sm">
                                Format: Day(s) HH:MM-HH:MM, e.g., Mo-Fr
                                09:00-17:00
                            </p>
                        </div>
                    </div>
                )}

                {schemaType === "Person" && (
                    <div className="space-y-6">
                        <div>
                            <label
                                htmlFor="name"
                                className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Full Name{" "}
                                <span className="text-red-500">*</span>
                            </label>
                            <input
                                id="name"
                                type="text"
                                value={formData.name || ""}
                                onChange={(e) => handleInputChange(e, "name")}
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                placeholder="e.g., John Doe"
                            />
                            {validationErrors.name && (
                                <p className="mt-1 text-red-500 text-sm">
                                    {validationErrors.name}
                                </p>
                            )}
                        </div>

                        <div>
                            <label
                                htmlFor="jobTitle"
                                className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Job Title
                            </label>
                            <input
                                id="jobTitle"
                                type="text"
                                value={formData.jobTitle || ""}
                                onChange={(e) =>
                                    handleInputChange(e, "jobTitle")
                                }
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                placeholder="e.g., Software Engineer"
                            />
                        </div>

                        <div>
                            <label
                                htmlFor="url"
                                className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Website URL
                            </label>
                            <input
                                id="url"
                                type="url"
                                value={formData.url || ""}
                                onChange={(e) => handleInputChange(e, "url")}
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                placeholder="e.g., https://www.johndoe.com"
                            />
                        </div>

                        <div>
                            <label
                                htmlFor="image"
                                className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Profile Image URL
                            </label>
                            <input
                                id="image"
                                type="url"
                                value={formData.image || ""}
                                onChange={(e) => handleInputChange(e, "image")}
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                placeholder="e.g., https://www.example.com/johndoe.jpg"
                            />
                            <p className="mt-1 text-gray-500 dark:text-gray-400 text-sm">
                                URL to a profile image (minimum 112x112px, .jpg,
                                .png, or .gif format)
                            </p>
                        </div>

                        <div>
                            <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Social Profiles
                            </label>
                            {formData.sameAs &&
                                formData.sameAs.map((url, index) => (
                                    <div
                                        key={index}
                                        className="flex items-center mb-2">
                                        <input
                                            type="url"
                                            value={url}
                                            onChange={(e) =>
                                                handleInputChange(
                                                    e,
                                                    "sameAs",
                                                    null,
                                                    index
                                                )
                                            }
                                            className="flex-grow p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                            placeholder="e.g., https://www.linkedin.com/in/johndoe"
                                        />
                                        <button
                                            type="button"
                                            onClick={() =>
                                                removeArrayItem("sameAs", index)
                                            }
                                            className="ml-2 p-2 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                                            aria-label="Remove social profile">
                                            <LucideIcon
                                                name="Trash2"
                                                size={20}
                                            />
                                        </button>
                                    </div>
                                ))}
                            <button
                                type="button"
                                onClick={() => addArrayItem("sameAs")}
                                className="mt-2 text-primary dark:text-primary-dark hover:text-primary/80 dark:hover:text-primary-dark/80 flex items-center">
                                <LucideIcon
                                    name="Plus"
                                    size={18}
                                    className="mr-1"
                                />
                                Add Social Profile
                            </button>
                            <p className="mt-1 text-gray-500 dark:text-gray-400 text-sm">
                                Add URLs to your social media profiles
                                (LinkedIn, Twitter, GitHub, etc.)
                            </p>
                        </div>
                    </div>
                )}

                {schemaType === "FAQPage" && (
                    <div className="space-y-6">
                        <div className="flex justify-between items-center mb-4">
                            <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                                FAQ Items
                            </h3>
                            <button
                                type="button"
                                onClick={() =>
                                    addArrayItem("mainEntity", "", {
                                        question: "",
                                        answer: "",
                                    })
                                }
                                className="text-primary dark:text-primary-dark hover:text-primary/80 dark:hover:text-primary-dark/80 flex items-center">
                                <LucideIcon
                                    name="Plus"
                                    size={18}
                                    className="mr-1"
                                />
                                Add FAQ Item
                            </button>
                        </div>

                        {validationErrors.mainEntity && (
                            <p className="mt-1 text-red-500 text-sm">
                                {validationErrors.mainEntity}
                            </p>
                        )}

                        {formData.mainEntity &&
                            formData.mainEntity.map((item, index) => (
                                <div
                                    key={index}
                                    className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg mb-4">
                                    <div className="flex justify-between items-center mb-3">
                                        <h4 className="font-medium text-gray-700 dark:text-gray-300">
                                            FAQ Item #{index + 1}
                                        </h4>
                                        <button
                                            type="button"
                                            onClick={() =>
                                                removeArrayItem(
                                                    "mainEntity",
                                                    index
                                                )
                                            }
                                            className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 flex items-center"
                                            aria-label="Remove FAQ item">
                                            <LucideIcon
                                                name="Trash2"
                                                size={18}
                                                className="mr-1"
                                            />
                                            Remove
                                        </button>
                                    </div>

                                    <div className="mb-3">
                                        <label
                                            htmlFor={`question_${index}`}
                                            className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                            Question{" "}
                                            <span className="text-red-500">
                                                *
                                            </span>
                                        </label>
                                        <input
                                            id={`question_${index}`}
                                            type="text"
                                            value={item.question || ""}
                                            onChange={(e) =>
                                                handleInputChange(
                                                    e,
                                                    "mainEntity",
                                                    "question",
                                                    index
                                                )
                                            }
                                            className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                            placeholder="e.g., How do I reset my password?"
                                        />
                                        {validationErrors[
                                            `question_${index}`
                                        ] && (
                                            <p className="mt-1 text-red-500 text-sm">
                                                {
                                                    validationErrors[
                                                        `question_${index}`
                                                    ]
                                                }
                                            </p>
                                        )}
                                    </div>

                                    <div>
                                        <label
                                            htmlFor={`answer_${index}`}
                                            className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                            Answer{" "}
                                            <span className="text-red-500">
                                                *
                                            </span>
                                        </label>
                                        <textarea
                                            id={`answer_${index}`}
                                            value={item.answer || ""}
                                            onChange={(e) =>
                                                handleInputChange(
                                                    e,
                                                    "mainEntity",
                                                    "answer",
                                                    index
                                                )
                                            }
                                            className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                            rows="3"
                                            placeholder="e.g., To reset your password, click on the 'Forgot Password' link on the login page..."></textarea>
                                        {validationErrors[
                                            `answer_${index}`
                                        ] && (
                                            <p className="mt-1 text-red-500 text-sm">
                                                {
                                                    validationErrors[
                                                        `answer_${index}`
                                                    ]
                                                }
                                            </p>
                                        )}
                                    </div>
                                </div>
                            ))}

                        {(!formData.mainEntity ||
                            formData.mainEntity.length === 0) && (
                            <div className="text-center p-8 border border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
                                <p className="text-gray-500 dark:text-gray-400">
                                    No FAQ items added yet. Click "Add FAQ Item"
                                    to get started.
                                </p>
                            </div>
                        )}
                    </div>
                )}

                {schemaType === "Product" && (
                    <div className="space-y-6">
                        <div>
                            <label
                                htmlFor="name"
                                className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Product Name{" "}
                                <span className="text-red-500">*</span>
                            </label>
                            <input
                                id="name"
                                type="text"
                                value={formData.name || ""}
                                onChange={(e) => handleInputChange(e, "name")}
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                placeholder="e.g., Wireless Headphones"
                            />
                            {validationErrors.name && (
                                <p className="mt-1 text-red-500 text-sm">
                                    {validationErrors.name}
                                </p>
                            )}
                        </div>

                        <div>
                            <label
                                htmlFor="description"
                                className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Description{" "}
                                <span className="text-red-500">*</span>
                            </label>
                            <textarea
                                id="description"
                                value={formData.description || ""}
                                onChange={(e) =>
                                    handleInputChange(e, "description")
                                }
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                rows="3"
                                placeholder="e.g., High-quality wireless headphones with noise cancellation and 20-hour battery life."></textarea>
                            {validationErrors.description && (
                                <p className="mt-1 text-red-500 text-sm">
                                    {validationErrors.description}
                                </p>
                            )}
                        </div>

                        <div>
                            <label
                                htmlFor="image"
                                className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Product Image URL
                            </label>
                            <input
                                id="image"
                                type="url"
                                value={formData.image || ""}
                                onChange={(e) => handleInputChange(e, "image")}
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                placeholder="e.g., https://www.example.com/headphones.jpg"
                            />
                            <p className="mt-1 text-gray-500 dark:text-gray-400 text-sm">
                                URL to the product image (high-quality, at least
                                1200px on the longest side)
                            </p>
                        </div>

                        <div>
                            <label
                                htmlFor="brand"
                                className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Brand
                            </label>
                            <input
                                id="brand"
                                type="text"
                                value={formData.brand || ""}
                                onChange={(e) => handleInputChange(e, "brand")}
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                placeholder="e.g., SoundMaster"
                            />
                        </div>

                        <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                                Offer Details
                            </h3>

                            <div className="space-y-4">
                                <div>
                                    <label
                                        htmlFor="price"
                                        className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                        Price{" "}
                                        <span className="text-red-500">*</span>
                                    </label>
                                    <input
                                        id="price"
                                        type="text"
                                        value={formData.offers?.price || ""}
                                        onChange={(e) =>
                                            handleInputChange(
                                                e,
                                                "offers",
                                                "price"
                                            )
                                        }
                                        className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                        placeholder="e.g., 99.99"
                                    />
                                    {validationErrors.price && (
                                        <p className="mt-1 text-red-500 text-sm">
                                            {validationErrors.price}
                                        </p>
                                    )}
                                    <p className="mt-1 text-gray-500 dark:text-gray-400 text-sm">
                                        Enter the price without currency symbol
                                    </p>
                                </div>

                                <div>
                                    <label
                                        htmlFor="priceCurrency"
                                        className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                        Currency
                                    </label>
                                    <input
                                        id="priceCurrency"
                                        type="text"
                                        value={
                                            formData.offers?.priceCurrency || ""
                                        }
                                        onChange={(e) =>
                                            handleInputChange(
                                                e,
                                                "offers",
                                                "priceCurrency"
                                            )
                                        }
                                        className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                        placeholder="e.g., USD"
                                    />
                                    <p className="mt-1 text-gray-500 dark:text-gray-400 text-sm">
                                        Use 3-letter currency code (USD, EUR,
                                        GBP, etc.)
                                    </p>
                                </div>

                                <div>
                                    <label
                                        htmlFor="availability"
                                        className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                        Availability
                                    </label>
                                    <select
                                        id="availability"
                                        value={
                                            formData.offers?.availability ||
                                            "InStock"
                                        }
                                        onChange={(e) =>
                                            handleInputChange(
                                                e,
                                                "offers",
                                                "availability"
                                            )
                                        }
                                        className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark">
                                        <option value="InStock">
                                            In Stock
                                        </option>
                                        <option value="OutOfStock">
                                            Out of Stock
                                        </option>
                                        <option value="PreOrder">
                                            Pre-Order
                                        </option>
                                        <option value="BackOrder">
                                            Back Order
                                        </option>
                                        <option value="LimitedAvailability">
                                            Limited Availability
                                        </option>
                                        <option value="SoldOut">
                                            Sold Out
                                        </option>
                                        <option value="Discontinued">
                                            Discontinued
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {schemaType === "Article" && (
                    <div className="space-y-6">
                        <div>
                            <label
                                htmlFor="headline"
                                className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Headline <span className="text-red-500">*</span>
                            </label>
                            <input
                                id="headline"
                                type="text"
                                value={formData.headline || ""}
                                onChange={(e) =>
                                    handleInputChange(e, "headline")
                                }
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                placeholder="e.g., 10 Essential Web Development Tools for 2025"
                            />
                            {validationErrors.headline && (
                                <p className="mt-1 text-red-500 text-sm">
                                    {validationErrors.headline}
                                </p>
                            )}
                        </div>

                        <div>
                            <label
                                htmlFor="description"
                                className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Description{" "}
                                <span className="text-red-500">*</span>
                            </label>
                            <textarea
                                id="description"
                                value={formData.description || ""}
                                onChange={(e) =>
                                    handleInputChange(e, "description")
                                }
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                rows="3"
                                placeholder="e.g., Discover the must-have tools that will revolutionize your web development workflow in 2025."></textarea>
                            {validationErrors.description && (
                                <p className="mt-1 text-red-500 text-sm">
                                    {validationErrors.description}
                                </p>
                            )}
                        </div>

                        <div>
                            <label
                                htmlFor="image"
                                className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Featured Image URL
                            </label>
                            <input
                                id="image"
                                type="url"
                                value={formData.image || ""}
                                onChange={(e) => handleInputChange(e, "image")}
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                placeholder="e.g., https://www.example.com/article-image.jpg"
                            />
                            <p className="mt-1 text-gray-500 dark:text-gray-400 text-sm">
                                URL to the article's featured image
                                (high-quality, at least 1200px on the longest
                                side)
                            </p>
                        </div>

                        <div>
                            <label
                                htmlFor="datePublished"
                                className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Date Published
                            </label>
                            <input
                                id="datePublished"
                                type="date"
                                value={formData.datePublished || ""}
                                onChange={(e) =>
                                    handleInputChange(e, "datePublished")
                                }
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                            />
                        </div>

                        <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                                Author Information
                            </h3>

                            <div className="space-y-4">
                                <div>
                                    <label
                                        htmlFor="authorName"
                                        className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                        Author Name
                                    </label>
                                    <input
                                        id="authorName"
                                        type="text"
                                        value={formData.author?.name || ""}
                                        onChange={(e) =>
                                            handleInputChange(
                                                e,
                                                "author",
                                                "name"
                                            )
                                        }
                                        className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                        placeholder="e.g., Jane Developer"
                                    />
                                </div>

                                <div>
                                    <label
                                        htmlFor="authorUrl"
                                        className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                        Author URL
                                    </label>
                                    <input
                                        id="authorUrl"
                                        type="url"
                                        value={formData.author?.url || ""}
                                        onChange={(e) =>
                                            handleInputChange(
                                                e,
                                                "author",
                                                "url"
                                            )
                                        }
                                        className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                        placeholder="e.g., https://www.example.com/authors/jane-developer"
                                    />
                                </div>
                            </div>
                        </div>

                        <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                                Publisher Information
                            </h3>

                            <div className="space-y-4">
                                <div>
                                    <label
                                        htmlFor="publisherName"
                                        className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                        Publisher Name
                                    </label>
                                    <input
                                        id="publisherName"
                                        type="text"
                                        value={formData.publisher?.name || ""}
                                        onChange={(e) =>
                                            handleInputChange(
                                                e,
                                                "publisher",
                                                "name"
                                            )
                                        }
                                        className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                        placeholder="e.g., DevBottle"
                                    />
                                </div>

                                <div>
                                    <label
                                        htmlFor="publisherLogo"
                                        className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                        Publisher Logo URL
                                    </label>
                                    <input
                                        id="publisherLogo"
                                        type="url"
                                        value={formData.publisher?.logo || ""}
                                        onChange={(e) =>
                                            handleInputChange(
                                                e,
                                                "publisher",
                                                "logo"
                                            )
                                        }
                                        className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                        placeholder="e.g., https://www.example.com/logo.png"
                                    />
                                    <p className="mt-1 text-gray-500 dark:text-gray-400 text-sm">
                                        URL to the publisher's logo (minimum
                                        112x112px, .jpg, .png, or .gif format)
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {schemaType === "Event" && (
                    <div className="space-y-6">
                        <div>
                            <label
                                htmlFor="name"
                                className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Event Name{" "}
                                <span className="text-red-500">*</span>
                            </label>
                            <input
                                id="name"
                                type="text"
                                value={formData.name || ""}
                                onChange={(e) => handleInputChange(e, "name")}
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                placeholder="e.g., DevBottle Conference 2025"
                            />
                            {validationErrors.name && (
                                <p className="mt-1 text-red-500 text-sm">
                                    {validationErrors.name}
                                </p>
                            )}
                        </div>

                        <div>
                            <label
                                htmlFor="description"
                                className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Description
                            </label>
                            <textarea
                                id="description"
                                value={formData.description || ""}
                                onChange={(e) =>
                                    handleInputChange(e, "description")
                                }
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                rows="3"
                                placeholder="e.g., The premier conference for web developers featuring workshops, talks, and networking opportunities."></textarea>
                        </div>

                        <div>
                            <label
                                htmlFor="image"
                                className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Event Image URL
                            </label>
                            <input
                                id="image"
                                type="url"
                                value={formData.image || ""}
                                onChange={(e) => handleInputChange(e, "image")}
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                placeholder="e.g., https://www.example.com/event-image.jpg"
                            />
                            <p className="mt-1 text-gray-500 dark:text-gray-400 text-sm">
                                URL to the event image (high-quality, at least
                                1200px on the longest side)
                            </p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label
                                    htmlFor="startDate"
                                    className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                    Start Date & Time{" "}
                                    <span className="text-red-500">*</span>
                                </label>
                                <input
                                    id="startDate"
                                    type="datetime-local"
                                    value={formData.startDate || ""}
                                    onChange={(e) =>
                                        handleInputChange(e, "startDate")
                                    }
                                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                />
                                {validationErrors.startDate && (
                                    <p className="mt-1 text-red-500 text-sm">
                                        {validationErrors.startDate}
                                    </p>
                                )}
                            </div>

                            <div>
                                <label
                                    htmlFor="endDate"
                                    className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                    End Date & Time
                                </label>
                                <input
                                    id="endDate"
                                    type="datetime-local"
                                    value={formData.endDate || ""}
                                    onChange={(e) =>
                                        handleInputChange(e, "endDate")
                                    }
                                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                />
                            </div>
                        </div>

                        <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                                Event Location
                            </h3>

                            <div className="space-y-4">
                                <div>
                                    <label
                                        htmlFor="locationName"
                                        className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                        Venue Name
                                    </label>
                                    <input
                                        id="locationName"
                                        type="text"
                                        value={formData.location?.name || ""}
                                        onChange={(e) =>
                                            handleInputChange(
                                                e,
                                                "location",
                                                "name"
                                            )
                                        }
                                        className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                        placeholder="e.g., Tech Convention Center"
                                    />
                                </div>

                                <div className="space-y-3">
                                    <h4 className="font-medium text-gray-700 dark:text-gray-300">
                                        Address
                                    </h4>

                                    <div>
                                        <label
                                            htmlFor="streetAddress"
                                            className="block text-gray-700 dark:text-gray-300 mb-1 text-sm">
                                            Street Address
                                        </label>
                                        <input
                                            id="streetAddress"
                                            type="text"
                                            value={
                                                formData.location?.address
                                                    ?.streetAddress || ""
                                            }
                                            onChange={(e) => {
                                                const newValue = e.target.value;
                                                setFormData((prevData) => ({
                                                    ...prevData,
                                                    location: {
                                                        ...prevData.location,
                                                        address: {
                                                            ...prevData.location
                                                                ?.address,
                                                            streetAddress:
                                                                newValue,
                                                        },
                                                    },
                                                }));
                                            }}
                                            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                            placeholder="e.g., 456 Innovation Boulevard"
                                        />
                                    </div>

                                    <div>
                                        <label
                                            htmlFor="addressLocality"
                                            className="block text-gray-700 dark:text-gray-300 mb-1 text-sm">
                                            City
                                        </label>
                                        <input
                                            id="addressLocality"
                                            type="text"
                                            value={
                                                formData.location?.address
                                                    ?.addressLocality || ""
                                            }
                                            onChange={(e) => {
                                                const newValue = e.target.value;
                                                setFormData((prevData) => ({
                                                    ...prevData,
                                                    location: {
                                                        ...prevData.location,
                                                        address: {
                                                            ...prevData.location
                                                                ?.address,
                                                            addressLocality:
                                                                newValue,
                                                        },
                                                    },
                                                }));
                                            }}
                                            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                            placeholder="e.g., San Francisco"
                                        />
                                    </div>

                                    <div>
                                        <label
                                            htmlFor="addressRegion"
                                            className="block text-gray-700 dark:text-gray-300 mb-1 text-sm">
                                            State/Province
                                        </label>
                                        <input
                                            id="addressRegion"
                                            type="text"
                                            value={
                                                formData.location?.address
                                                    ?.addressRegion || ""
                                            }
                                            onChange={(e) => {
                                                const newValue = e.target.value;
                                                setFormData((prevData) => ({
                                                    ...prevData,
                                                    location: {
                                                        ...prevData.location,
                                                        address: {
                                                            ...prevData.location
                                                                ?.address,
                                                            addressRegion:
                                                                newValue,
                                                        },
                                                    },
                                                }));
                                            }}
                                            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                            placeholder="e.g., CA"
                                        />
                                    </div>

                                    <div>
                                        <label
                                            htmlFor="postalCode"
                                            className="block text-gray-700 dark:text-gray-300 mb-1 text-sm">
                                            Postal Code
                                        </label>
                                        <input
                                            id="postalCode"
                                            type="text"
                                            value={
                                                formData.location?.address
                                                    ?.postalCode || ""
                                            }
                                            onChange={(e) => {
                                                const newValue = e.target.value;
                                                setFormData((prevData) => ({
                                                    ...prevData,
                                                    location: {
                                                        ...prevData.location,
                                                        address: {
                                                            ...prevData.location
                                                                ?.address,
                                                            postalCode:
                                                                newValue,
                                                        },
                                                    },
                                                }));
                                            }}
                                            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                            placeholder="e.g., 94107"
                                        />
                                    </div>

                                    <div>
                                        <label
                                            htmlFor="addressCountry"
                                            className="block text-gray-700 dark:text-gray-300 mb-1 text-sm">
                                            Country
                                        </label>
                                        <input
                                            id="addressCountry"
                                            type="text"
                                            value={
                                                formData.location?.address
                                                    ?.addressCountry || ""
                                            }
                                            onChange={(e) => {
                                                const newValue = e.target.value;
                                                setFormData((prevData) => ({
                                                    ...prevData,
                                                    location: {
                                                        ...prevData.location,
                                                        address: {
                                                            ...prevData.location
                                                                ?.address,
                                                            addressCountry:
                                                                newValue,
                                                        },
                                                    },
                                                }));
                                            }}
                                            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                            placeholder="e.g., US"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {schemaType === "Recipe" && (
                    <div className="space-y-6">
                        <div>
                            <label
                                htmlFor="name"
                                className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Recipe Name{" "}
                                <span className="text-red-500">*</span>
                            </label>
                            <input
                                id="name"
                                type="text"
                                value={formData.name || ""}
                                onChange={(e) => handleInputChange(e, "name")}
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                placeholder="e.g., Developer's Coffee"
                            />
                            {validationErrors.name && (
                                <p className="mt-1 text-red-500 text-sm">
                                    {validationErrors.name}
                                </p>
                            )}
                        </div>

                        <div>
                            <label
                                htmlFor="description"
                                className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Description
                            </label>
                            <textarea
                                id="description"
                                value={formData.description || ""}
                                onChange={(e) =>
                                    handleInputChange(e, "description")
                                }
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                rows="3"
                                placeholder="e.g., The perfect coffee recipe to fuel your coding sessions."></textarea>
                        </div>

                        <div>
                            <label
                                htmlFor="image"
                                className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Recipe Image URL
                            </label>
                            <input
                                id="image"
                                type="url"
                                value={formData.image || ""}
                                onChange={(e) => handleInputChange(e, "image")}
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                placeholder="e.g., https://www.example.com/recipe-image.jpg"
                            />
                            <p className="mt-1 text-gray-500 dark:text-gray-400 text-sm">
                                URL to the recipe image (high-quality, at least
                                1200px on the longest side)
                            </p>
                        </div>

                        <div>
                            <label
                                htmlFor="authorName"
                                className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Author Name
                            </label>
                            <input
                                id="authorName"
                                type="text"
                                value={formData.author?.name || ""}
                                onChange={(e) =>
                                    handleInputChange(e, "author", "name")
                                }
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                placeholder="e.g., Jane Developer"
                            />
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label
                                    htmlFor="prepTime"
                                    className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                    Preparation Time
                                </label>
                                <input
                                    id="prepTime"
                                    type="text"
                                    value={formData.prepTime || ""}
                                    onChange={(e) =>
                                        handleInputChange(e, "prepTime")
                                    }
                                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                    placeholder="e.g., PT10M (10 minutes)"
                                />
                                <p className="mt-1 text-gray-500 dark:text-gray-400 text-sm">
                                    Use ISO 8601 duration format: PT1H30M (1
                                    hour 30 minutes)
                                </p>
                            </div>

                            <div>
                                <label
                                    htmlFor="cookTime"
                                    className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                    Cooking Time
                                </label>
                                <input
                                    id="cookTime"
                                    type="text"
                                    value={formData.cookTime || ""}
                                    onChange={(e) =>
                                        handleInputChange(e, "cookTime")
                                    }
                                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                    placeholder="e.g., PT20M (20 minutes)"
                                />
                                <p className="mt-1 text-gray-500 dark:text-gray-400 text-sm">
                                    Use ISO 8601 duration format: PT1H30M (1
                                    hour 30 minutes)
                                </p>
                            </div>
                        </div>

                        <div>
                            <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Ingredients{" "}
                                <span className="text-red-500">*</span>
                            </label>
                            {validationErrors.recipeIngredient && (
                                <p className="mt-1 text-red-500 text-sm">
                                    {validationErrors.recipeIngredient}
                                </p>
                            )}
                            {formData.recipeIngredient &&
                                formData.recipeIngredient.map(
                                    (ingredient, index) => (
                                        <div
                                            key={index}
                                            className="flex items-center mb-2">
                                            <input
                                                type="text"
                                                value={ingredient}
                                                onChange={(e) =>
                                                    handleInputChange(
                                                        e,
                                                        "recipeIngredient",
                                                        null,
                                                        index
                                                    )
                                                }
                                                className="flex-grow p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                                placeholder="e.g., 2 tablespoons freshly ground coffee beans"
                                            />
                                            <button
                                                type="button"
                                                onClick={() =>
                                                    removeArrayItem(
                                                        "recipeIngredient",
                                                        index
                                                    )
                                                }
                                                className="ml-2 p-2 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                                                aria-label="Remove ingredient">
                                                <LucideIcon
                                                    name="Trash2"
                                                    size={20}
                                                />
                                            </button>
                                        </div>
                                    )
                                )}
                            <button
                                type="button"
                                onClick={() => addArrayItem("recipeIngredient")}
                                className="mt-2 text-primary dark:text-primary-dark hover:text-primary/80 dark:hover:text-primary-dark/80 flex items-center">
                                <LucideIcon
                                    name="Plus"
                                    size={18}
                                    className="mr-1"
                                />
                                Add Ingredient
                            </button>
                        </div>

                        <div>
                            <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                Instructions{" "}
                                <span className="text-red-500">*</span>
                            </label>
                            {validationErrors.recipeInstructions && (
                                <p className="mt-1 text-red-500 text-sm">
                                    {validationErrors.recipeInstructions}
                                </p>
                            )}
                            {formData.recipeInstructions &&
                                formData.recipeInstructions.map(
                                    (instruction, index) => (
                                        <div
                                            key={index}
                                            className="flex items-center mb-2">
                                            <textarea
                                                value={instruction}
                                                onChange={(e) =>
                                                    handleInputChange(
                                                        e,
                                                        "recipeInstructions",
                                                        null,
                                                        index
                                                    )
                                                }
                                                className="flex-grow p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                                rows="2"
                                                placeholder={`Step ${
                                                    index + 1
                                                }: e.g., Brew coffee using your preferred method.`}></textarea>
                                            <button
                                                type="button"
                                                onClick={() =>
                                                    removeArrayItem(
                                                        "recipeInstructions",
                                                        index
                                                    )
                                                }
                                                className="ml-2 p-2 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                                                aria-label="Remove instruction">
                                                <LucideIcon
                                                    name="Trash2"
                                                    size={20}
                                                />
                                            </button>
                                        </div>
                                    )
                                )}
                            <button
                                type="button"
                                onClick={() =>
                                    addArrayItem("recipeInstructions")
                                }
                                className="mt-2 text-primary dark:text-primary-dark hover:text-primary/80 dark:hover:text-primary-dark/80 flex items-center">
                                <LucideIcon
                                    name="Plus"
                                    size={18}
                                    className="mr-1"
                                />
                                Add Instruction
                            </button>
                        </div>
                    </div>
                )}

                {/* All schema types are now implemented */}
            </div>
        );
    };

    // Generate the schema based on the form data
    const generateSchema = () => {
        // Validate the form data first
        if (!validateForm()) {
            // Scroll to the top to show validation errors
            window.scrollTo({ top: 0, behavior: "smooth" });
            return;
        }

        let schema = {
            "@context": "https://schema.org",
            "@type": schemaType,
        };

        // Process the form data based on the schema type
        switch (schemaType) {
            case "Organization":
                schema = {
                    ...schema,
                    name: formData.name,
                    url: formData.url,
                };

                // Add optional fields if they exist
                if (formData.logo) schema.logo = formData.logo;
                if (formData.description)
                    schema.description = formData.description;

                // Add social profiles if they exist and are not empty
                if (formData.sameAs && formData.sameAs.length > 0) {
                    const filteredSameAs = formData.sameAs.filter(
                        (url) => url.trim() !== ""
                    );
                    if (filteredSameAs.length > 0) {
                        schema.sameAs = filteredSameAs;
                    }
                }
                break;

            case "LocalBusiness":
                schema = {
                    ...schema,
                    name: formData.name,
                    url: formData.url,
                };

                // Add optional fields if they exist
                if (formData.telephone) schema.telephone = formData.telephone;

                // Add address if any of the address fields are filled
                const addressFields = formData.address || {};
                const hasAddressData = Object.values(addressFields).some(
                    (value) => value && value.trim() !== ""
                );

                if (hasAddressData) {
                    schema.address = {
                        "@type": "PostalAddress",
                    };

                    if (addressFields.streetAddress)
                        schema.address.streetAddress =
                            addressFields.streetAddress;
                    if (addressFields.addressLocality)
                        schema.address.addressLocality =
                            addressFields.addressLocality;
                    if (addressFields.addressRegion)
                        schema.address.addressRegion =
                            addressFields.addressRegion;
                    if (addressFields.postalCode)
                        schema.address.postalCode = addressFields.postalCode;
                    if (addressFields.addressCountry)
                        schema.address.addressCountry =
                            addressFields.addressCountry;
                }

                // Add opening hours if they exist and are not empty
                if (formData.openingHours && formData.openingHours.length > 0) {
                    const filteredHours = formData.openingHours.filter(
                        (hours) => hours.trim() !== ""
                    );
                    if (filteredHours.length > 0) {
                        schema.openingHours = filteredHours;
                    }
                }
                break;

            case "Person":
                schema = {
                    ...schema,
                    name: formData.name,
                };

                // Add optional fields if they exist
                if (formData.jobTitle) schema.jobTitle = formData.jobTitle;
                if (formData.url) schema.url = formData.url;
                if (formData.image) schema.image = formData.image;

                // Add social profiles if they exist and are not empty
                if (formData.sameAs && formData.sameAs.length > 0) {
                    const filteredSameAs = formData.sameAs.filter(
                        (url) => url.trim() !== ""
                    );
                    if (filteredSameAs.length > 0) {
                        schema.sameAs = filteredSameAs;
                    }
                }
                break;

            case "Product":
                schema = {
                    ...schema,
                    name: formData.name,
                    description: formData.description,
                };

                // Add optional fields if they exist
                if (formData.image) schema.image = formData.image;
                if (formData.brand) {
                    schema.brand = {
                        "@type": "Brand",
                        name: formData.brand,
                    };
                }

                // Add offer details if they exist
                if (formData.offers) {
                    const offerData = {
                        "@type": "Offer",
                    };

                    if (formData.offers.price)
                        offerData.price = formData.offers.price;
                    if (formData.offers.priceCurrency)
                        offerData.priceCurrency = formData.offers.priceCurrency;
                    if (formData.offers.availability) {
                        offerData.availability = `https://schema.org/${formData.offers.availability}`;
                    }

                    schema.offers = offerData;
                }
                break;

            case "Article":
                schema = {
                    ...schema,
                    headline: formData.headline,
                    description: formData.description,
                };

                // Add optional fields if they exist
                if (formData.image) schema.image = formData.image;
                if (formData.datePublished)
                    schema.datePublished = formData.datePublished;

                // Add author information if it exists
                if (formData.author && formData.author.name) {
                    schema.author = {
                        "@type": "Person",
                        name: formData.author.name,
                    };

                    if (formData.author.url)
                        schema.author.url = formData.author.url;
                }

                // Add publisher information if it exists
                if (formData.publisher && formData.publisher.name) {
                    schema.publisher = {
                        "@type": "Organization",
                        name: formData.publisher.name,
                    };

                    if (formData.publisher.logo) {
                        schema.publisher.logo = {
                            "@type": "ImageObject",
                            url: formData.publisher.logo,
                        };
                    }
                }
                break;

            case "Event":
                schema = {
                    ...schema,
                    name: formData.name,
                };

                // Add required fields
                if (formData.startDate) schema.startDate = formData.startDate;

                // Add optional fields if they exist
                if (formData.description)
                    schema.description = formData.description;
                if (formData.image) schema.image = formData.image;
                if (formData.endDate) schema.endDate = formData.endDate;

                // Add location information if it exists
                if (formData.location) {
                    const locationData = {
                        "@type": "Place",
                    };

                    if (formData.location.name)
                        locationData.name = formData.location.name;

                    // Add address if any of the address fields are filled
                    if (formData.location.address) {
                        const addressFields = formData.location.address;
                        const hasAddressData = Object.values(
                            addressFields
                        ).some((value) => value && value.trim() !== "");

                        if (hasAddressData) {
                            locationData.address = {
                                "@type": "PostalAddress",
                            };

                            if (addressFields.streetAddress)
                                locationData.address.streetAddress =
                                    addressFields.streetAddress;
                            if (addressFields.addressLocality)
                                locationData.address.addressLocality =
                                    addressFields.addressLocality;
                            if (addressFields.addressRegion)
                                locationData.address.addressRegion =
                                    addressFields.addressRegion;
                            if (addressFields.postalCode)
                                locationData.address.postalCode =
                                    addressFields.postalCode;
                            if (addressFields.addressCountry)
                                locationData.address.addressCountry =
                                    addressFields.addressCountry;
                        }
                    }

                    schema.location = locationData;
                }
                break;

            case "Recipe":
                schema = {
                    ...schema,
                    name: formData.name,
                };

                // Add optional fields if they exist
                if (formData.description)
                    schema.description = formData.description;
                if (formData.image) schema.image = formData.image;
                if (formData.prepTime) schema.prepTime = formData.prepTime;
                if (formData.cookTime) schema.cookTime = formData.cookTime;

                // Add author information if it exists
                if (formData.author && formData.author.name) {
                    schema.author = {
                        "@type": "Person",
                        name: formData.author.name,
                    };
                }

                // Add ingredients if they exist and are not empty
                if (
                    formData.recipeIngredient &&
                    formData.recipeIngredient.length > 0
                ) {
                    const filteredIngredients =
                        formData.recipeIngredient.filter(
                            (ingredient) => ingredient.trim() !== ""
                        );
                    if (filteredIngredients.length > 0) {
                        schema.recipeIngredient = filteredIngredients;
                    }
                }

                // Add instructions if they exist and are not empty
                if (
                    formData.recipeInstructions &&
                    formData.recipeInstructions.length > 0
                ) {
                    const filteredInstructions =
                        formData.recipeInstructions.filter(
                            (instruction) => instruction.trim() !== ""
                        );
                    if (filteredInstructions.length > 0) {
                        schema.recipeInstructions = filteredInstructions;
                    }
                }
                break;

            case "FAQPage":
                // For FAQPage, we need to structure the mainEntity as an array of Question objects
                if (formData.mainEntity && formData.mainEntity.length > 0) {
                    schema.mainEntity = formData.mainEntity.map((item) => ({
                        "@type": "Question",
                        name: item.question,
                        acceptedAnswer: {
                            "@type": "Answer",
                            text: item.answer,
                        },
                    }));
                }
                break;

            // We'll add more schema types in the next parts
            default:
                // For now, just use the raw form data for other schema types
                schema = {
                    ...schema,
                    ...formData,
                };
        }

        setGeneratedSchema(JSON.stringify(schema, null, 2));
        setActiveTab("code");
    };

    // Copy generated schema to clipboard
    const copyToClipboard = () => {
        if (!isBrowser || !generatedSchema) return;

        navigator.clipboard.writeText(generatedSchema).then(() => {
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        });
    };

    return (
        <ToolContainer
            className="py-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-colors duration-200"
            containerWidth={toolData?.containerWidth || "default"}>
            <ToolHeader
                toolData={toolData}
                defaultTitle="Schema Generator"
                defaultIcon="Code2"
                defaultDescription="Create structured data markup that helps search engines understand your content better. This tool generates JSON-LD code for common schema types like Organization, Person, Local Business, Product, Article, Event, Recipe, and FAQ."
            />

            <div className="mb-8">
                <div className="flex justify-between items-center mb-2">
                    <label
                        htmlFor="schemaType"
                        className="block text-gray-700 dark:text-gray-300 font-medium">
                        Select Schema Type
                    </label>
                    <button
                        type="button"
                        onClick={loadSample}
                        className="text-primary dark:text-primary-dark hover:text-primary/80 dark:hover:text-primary-dark/80 flex items-center text-sm">
                        <LucideIcon
                            name="FileText"
                            size={16}
                            className="mr-1"
                        />
                        Load Sample
                    </button>
                </div>
                <select
                    id="schemaType"
                    value={schemaType}
                    onChange={handleSchemaTypeChange}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark">
                    {schemaTypes.map((type) => (
                        <option key={type.id} value={type.id}>
                            {type.name} - {type.description}
                        </option>
                    ))}
                </select>
            </div>

            <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
                <nav className="flex space-x-8">
                    <button
                        onClick={() => setActiveTab("form")}
                        className={`py-4 px-1 border-b-2 font-medium text-sm ${
                            activeTab === "form"
                                ? "border-primary dark:border-primary-dark text-primary dark:text-primary-dark"
                                : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"
                        }`}>
                        Form
                    </button>
                    <button
                        onClick={() => setActiveTab("code")}
                        className={`py-4 px-1 border-b-2 font-medium text-sm ${
                            activeTab === "code"
                                ? "border-primary dark:border-primary-dark text-primary dark:text-primary-dark"
                                : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"
                        }`}>
                        Generated Code
                    </button>
                </nav>
            </div>

            {activeTab === "form" ? (
                <>
                    {renderForm()}

                    <div className="flex justify-center mt-6">
                        <button
                            onClick={generateSchema}
                            className="bg-primary dark:bg-primary-dark text-white px-6 py-3 rounded-lg hover:bg-primary/90 dark:hover:bg-primary-dark/90 focus:outline-none focus:ring-4 focus:ring-primary/50 dark:focus:ring-primary-dark/50 transition-all flex items-center">
                            <LucideIcon
                                name="Code2"
                                size={20}
                                className="mr-2"
                            />
                            Generate Schema
                        </button>
                    </div>
                </>
            ) : (
                <div className="bg-white dark:bg-gray-800 rounded-xl p-6">
                    <div className="flex justify-between items-center mb-4">
                        <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                            Generated JSON-LD Schema
                        </h3>
                        <button
                            onClick={copyToClipboard}
                            className="text-primary dark:text-primary-dark hover:text-primary/80 dark:hover:text-primary-dark/80 transition-colors flex items-center"
                            disabled={!generatedSchema}>
                            {copied ? (
                                <>
                                    <LucideIcon
                                        name="Check"
                                        size={18}
                                        className="mr-1"
                                    />
                                    Copied!
                                </>
                            ) : (
                                <>
                                    <LucideIcon
                                        name="Copy"
                                        size={18}
                                        className="mr-1"
                                    />
                                    Copy to Clipboard
                                </>
                            )}
                        </button>
                    </div>

                    {generatedSchema ? (
                        <pre className="bg-gray-100 dark:bg-gray-900 p-4 rounded-lg overflow-x-auto text-sm font-mono text-gray-800 dark:text-gray-200">
                            {`<script type="application/ld+json">\n${generatedSchema}\n</script>`}
                        </pre>
                    ) : (
                        <div className="text-center p-8 text-gray-500 dark:text-gray-400">
                            Fill out the form and click "Generate Schema" to see
                            the code here.
                        </div>
                    )}

                    <div className="mt-6 bg-blue-50 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 p-4 rounded-lg">
                        <div className="flex items-start">
                            <LucideIcon
                                name="Info"
                                size={20}
                                className="mr-2 flex-shrink-0 mt-1"
                            />
                            <div>
                                <p className="font-medium">
                                    How to use this code:
                                </p>
                                <ol className="list-decimal ml-5 mt-2 space-y-1">
                                    <li>Copy the generated code</li>
                                    <li>
                                        Paste it into the{" "}
                                        <code className="bg-blue-100 dark:bg-blue-800 px-1 rounded">
                                            &lt;head&gt;
                                        </code>{" "}
                                        section of your HTML
                                    </li>
                                    <li>
                                        Test your implementation using{" "}
                                        <a
                                            href="https://search.google.com/test/rich-results"
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-primary dark:text-primary-dark underline">
                                            Google's Rich Results Test
                                        </a>
                                    </li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </ToolContainer>
    );
}
