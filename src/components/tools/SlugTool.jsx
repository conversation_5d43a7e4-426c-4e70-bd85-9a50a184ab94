import { useState, useEffect } from "react";
import { getToolById } from "../../data/tools.js";
import LucideIcon from "../ui/LucideIcon.jsx";
import ToolContainer from "../ui/ToolContainer.jsx";

export default function SlugifyTool() {
    const [toolData, setToolData] = useState(null);

    useEffect(() => {
        // Get the tool data
        const data = getToolById("slugtool");
        setToolData(data);
    }, []);
    const [input, setInput] = useState("");
    const [output, setOutput] = useState("");
    const [copied, setCopied] = useState(false);
    const [options, setOptions] = useState({
        lowercase: true,
        removeStopWords: true,
        removeNumbers: false,
        separator: "-",
    });

    // Sample text with stop words, numbers, and uppercase
    const sampleText =
        "The ULTIMATE Guide to Web Development in 2025: 10 Essential Tips & Tricks for Beginners.";

    // Function to load the sample text
    const loadSample = () => {
        setInput(sampleText);
        setCopied(false);

        // Generate the slug automatically after a short delay
        setTimeout(() => {
            generateSlug();
        }, 100);
    };

    const DEFAULT_STOP_WORDS = [
        "a",
        "an",
        "the",
        "and",
        "or",
        "but",
        "of",
        "for",
        "in",
        "to",
        "with",
        "by",
        "at",
        "from",
        "up",
        "about",
        "into",
        "over",
        "after",
        "beneath",
        "under",
        "above",
        "is",
        "are",
        "am",
        "was",
        "were",
        "be",
        "been",
        "being",
    ];

    const generateSlug = () => {
        let slug = input.trim();

        if (options.removeStopWords) {
            const stopWordsPattern = `\\s*\\b(${DEFAULT_STOP_WORDS.join(
                "|"
            )})\\b\\s*`;
            const stopWordsRegex = new RegExp(stopWordsPattern, "gi");
            slug = slug
                .replace(stopWordsRegex, " ")
                .replace(/\s+/g, " ")
                .trim();
        }

        if (options.lowercase) {
            slug = slug.toLowerCase();
        }

        const keepChars = options.separator === "_" ? "\\w_" : "\\w-";
        slug = slug.replace(new RegExp(`[^${keepChars}\\s]`, "g"), "");

        slug =
            options.separator === ""
                ? slug.replace(/\s+/g, "")
                : slug.replace(/\s+/g, options.separator);

        if (options.removeNumbers) {
            slug = slug.replace(/[0-9]/g, "");
        }

        // Limit consecutive separators to a single character
        if (options.separator !== "") {
            const consecutiveSeparatorsRegex = new RegExp(
                `${options.separator}{2,}`,
                "g"
            );
            slug = slug.replace(consecutiveSeparatorsRegex, options.separator);

            // Remove trailing separators
            slug = slug.replace(new RegExp(`${options.separator}+$`, "g"), "");

            // Also remove leading separators
            slug = slug.replace(new RegExp(`^${options.separator}+`, "g"), "");
        }

        setOutput(slug);
        setCopied(false);
    };

    const copyToClipboard = () => {
        navigator.clipboard.writeText(output).then(() => {
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        });
    };

    const clearInput = () => {
        setInput("");
        setOutput("");
        setCopied(false);
    };

    return (
        <ToolContainer
            className="py-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-colors duration-200"
            containerWidth={toolData?.containerWidth || "default"}>
            <div className="flex items-center mb-6">
                <div className="text-primary dark:text-primary-dark mr-4">
                    {toolData?.icon ? (
                        <LucideIcon name={toolData.icon} size={36} />
                    ) : null}
                </div>
                <h1 className="text-3xl md:text-4xl font-bold text-gray-800 dark:text-white">
                    {toolData?.name || "Slug Tool"}
                </h1>
            </div>
            <p className="mb-8 text-gray-600 dark:text-gray-400 leading-relaxed text-lg">
                {toolData?.fullDescription ||
                    "Convert any text into a clean, URL-friendly slug. Perfect for SEO, filenames, or creating readable identifiers for your content."}
            </p>

            <form
                onSubmit={(e) => {
                    e.preventDefault();
                    generateSlug();
                }}
                className="space-y-6">
                <div>
                    <div className="flex justify-between items-center mb-2">
                        <label
                            htmlFor="input"
                            className="block text-sm font-semibold text-gray-800 dark:text-gray-200">
                            INPUT STRING: (Article title, tutorial title or any
                            web page title)
                        </label>
                        <div className="flex space-x-2">
                            <button
                                type="button"
                                className="px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-md text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                                onClick={loadSample}>
                                Load Sample
                            </button>
                            {input && (
                                <button
                                    type="button"
                                    className="px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-md text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                                    onClick={clearInput}>
                                    Clear
                                </button>
                            )}
                        </div>
                    </div>
                    <input
                        id="input"
                        type="text"
                        className="w-full p-4 border border-gray-300 dark:border-gray-600 rounded-xl text-xl font-mono bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark transition-all"
                        value={input}
                        onChange={(e) => setInput(e.target.value)}
                        placeholder="Enter text or click 'Load Sample' to try with example text"
                    />
                </div>

                {/* Options Panel */}
                <div className="space-y-4">
                    <h3 className="font-medium text-gray-800 dark:text-white">
                        Slug Options
                    </h3>

                    <div className="flex flex-wrap gap-4">
                        {/* Lowercase Option */}
                        <div className="flex border border-gray-200 dark:border-gray-600 rounded-lg p-4 bg-gray-50 dark:bg-gray-700">
                            <label className="flex items-center gap-3 cursor-pointer">
                                <input
                                    type="checkbox"
                                    checked={options.lowercase}
                                    onChange={() =>
                                        setOptions({
                                            ...options,
                                            lowercase: !options.lowercase,
                                        })
                                    }
                                    className="h-4 w-4 text-primary dark:text-primary-dark rounded border-gray-300 focus:ring-primary dark:focus:ring-primary-dark"
                                />
                                <div>
                                    <span className="text-md font-medium text-gray-700 dark:text-gray-200">
                                        Lowercase
                                    </span>
                                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        Converts all characters to lowercase
                                    </div>
                                </div>
                            </label>
                        </div>

                        {/* Remove Stop Words */}
                        <div className="flex border border-gray-200 dark:border-gray-600 rounded-lg p-4 bg-gray-50 dark:bg-gray-700">
                            <label className="flex items-center gap-3 cursor-pointer">
                                <input
                                    type="checkbox"
                                    checked={options.removeStopWords}
                                    onChange={() =>
                                        setOptions({
                                            ...options,
                                            removeStopWords:
                                                !options.removeStopWords,
                                        })
                                    }
                                    className="h-4 w-4 text-primary dark:text-primary-dark rounded border-gray-300 focus:ring-primary dark:focus:ring-primary-dark"
                                />
                                <div>
                                    <span className="text-md font-medium text-gray-700 dark:text-gray-200">
                                        Remove stop words
                                    </span>
                                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        Removes common words like 'the', 'and',
                                        'of', etc.
                                    </div>
                                </div>
                            </label>
                        </div>

                        {/* Remove Numbers */}
                        <div className="flex border border-gray-200 dark:border-gray-600 rounded-lg p-4 bg-gray-50 dark:bg-gray-700">
                            <label className="flex items-center gap-3 cursor-pointer">
                                <input
                                    type="checkbox"
                                    checked={options.removeNumbers}
                                    onChange={() =>
                                        setOptions({
                                            ...options,
                                            removeNumbers:
                                                !options.removeNumbers,
                                        })
                                    }
                                    className="h-4 w-4 text-primary dark:text-primary-dark rounded border-gray-300 focus:ring-primary dark:focus:ring-primary-dark"
                                />
                                <div>
                                    <span className="text-md font-medium text-gray-700 dark:text-gray-200">
                                        Remove numbers
                                    </span>
                                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        Removes all numeric digits from the slug
                                    </div>
                                </div>
                            </label>
                        </div>

                        {/* Separator Select */}
                        <div className="flex border border-gray-200 dark:border-gray-600 rounded-lg p-4 bg-gray-50 dark:bg-gray-700">
                            <label className="flex flex-col gap-2">
                                <div>
                                    <span className="text-md font-medium text-gray-700 dark:text-gray-200">
                                        Separator
                                    </span>
                                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        Character used to replace spaces
                                    </div>
                                </div>
                                <select
                                    value={options.separator}
                                    onChange={(e) =>
                                        setOptions({
                                            ...options,
                                            separator: e.target.value,
                                        })
                                    }
                                    className="border border-gray-300 dark:border-gray-600 rounded px-2 py-1 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-primary dark:focus:ring-primary-dark focus:outline-none">
                                    <option value="-">Hyphen (-)</option>
                                    <option value="_">Underscore (_)</option>
                                    <option value="">None</option>
                                </select>
                            </label>
                        </div>
                    </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-wrap items-center gap-4">
                    <button
                        type="button"
                        className="bg-primary dark:bg-primary-dark text-white px-5 py-2 rounded-xl hover:bg-primary/90 dark:hover:bg-primary-dark/90 focus:outline-none focus:ring-4 focus:ring-primary/50 dark:focus:ring-primary-dark/50 transition-all"
                        onClick={generateSlug}>
                        Generate Slug
                    </button>

                    {output && (
                        <button
                            type="button"
                            className="text-sm text-primary dark:text-primary-dark border border-primary dark:border-primary-dark px-4 py-1 rounded-xl hover:bg-primary/10 dark:hover:bg-primary-dark/10 focus:outline-none transition-all"
                            onClick={copyToClipboard}>
                            {copied ? "Copied!" : "Copy to Clipboard"}
                        </button>
                    )}
                </div>

                {/* Output Section */}
                {output && (
                    <div className="mt-6">
                        <div className="flex items-center justify-between mb-2">
                            <label className="text-sm font-semibold text-gray-800 dark:text-gray-200">
                                Your Slug
                            </label>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                                {output.length} characters
                            </span>
                        </div>
                        <div
                            className="p-4 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-xl cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors shadow-sm"
                            onClick={copyToClipboard}>
                            <div className="font-mono text-xl break-all mb-4 text-gray-900 dark:text-white">
                                {output}
                            </div>
                            <div className="mt-2 flex justify-end">
                                <span className="text-md text-primary dark:text-primary-dark flex items-center">
                                    <svg
                                        className="w-4 h-4 mr-1"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"
                                        />
                                    </svg>
                                    {copied
                                        ? "Copied!"
                                        : "Click anywhere to copy"}
                                </span>
                            </div>
                        </div>
                    </div>
                )}
            </form>
        </ToolContainer>
    );
}
