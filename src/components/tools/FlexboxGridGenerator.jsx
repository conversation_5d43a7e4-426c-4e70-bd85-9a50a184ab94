import { useState, useEffect } from "react";
import { getToolById } from "../../data/tools.js";
import ToolHeader from "../ui/ToolHeader.jsx";
import ToolContainer from "../ui/ToolContainer.jsx";
import LucideIcon from "../ui/LucideIcon.jsx";

// Flexbox properties
const FLEX_DIRECTIONS = ["row", "row-reverse", "column", "column-reverse"];
const JUSTIFY_CONTENTS = [
    "flex-start",
    "flex-end",
    "center",
    "space-between",
    "space-around",
    "space-evenly",
];
const ALIGN_ITEMS = ["flex-start", "flex-end", "center", "stretch", "baseline"];
const FLEX_WRAPS = ["nowrap", "wrap", "wrap-reverse"];
const ALIGN_CONTENTS = [
    "flex-start",
    "flex-end",
    "center",
    "stretch",
    "space-between",
    "space-around",
    "space-evenly",
];
const GAP_SIZES = ["0", "0.25rem", "0.5rem", "1rem", "1.5rem", "2rem"];

// Grid properties
const GRID_TEMPLATE_COLUMNS = [
    "1fr",
    "1fr 1fr",
    "1fr 1fr 1fr",
    "repeat(2, 1fr)",
    "repeat(3, 1fr)",
    "repeat(4, 1fr)",
];
const GRID_TEMPLATE_ROWS = [
    "auto",
    "1fr",
    "auto 1fr",
    "1fr 1fr",
    "repeat(2, 1fr)",
    "repeat(3, 1fr)",
];
const GRID_AUTO_FLOWS = ["row", "column", "row dense", "column dense"];

// Presets for flexbox layouts
const FLEXBOX_PRESETS = [
    {
        name: "Centered Content",
        container: {
            display: "flex",
            flexDirection: "row",
            justifyContent: "center",
            alignItems: "center",
            flexWrap: "nowrap",
            gap: "0.5rem",
        },
        items: [
            { flexGrow: 0, flexShrink: 1, flexBasis: "auto" },
            { flexGrow: 0, flexShrink: 1, flexBasis: "auto" },
            { flexGrow: 0, flexShrink: 1, flexBasis: "auto" },
        ],
    },
    {
        name: "Navigation Bar",
        container: {
            display: "flex",
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
            flexWrap: "nowrap",
            gap: "1rem",
        },
        items: [
            { flexGrow: 1, flexShrink: 1, flexBasis: "auto" },
            { flexGrow: 0, flexShrink: 0, flexBasis: "auto" },
            { flexGrow: 0, flexShrink: 0, flexBasis: "auto" },
        ],
    },
    {
        name: "Card Layout",
        container: {
            display: "flex",
            flexDirection: "row",
            justifyContent: "flex-start",
            alignItems: "stretch",
            flexWrap: "wrap",
            gap: "1rem",
        },
        items: [
            { flexGrow: 1, flexShrink: 1, flexBasis: "300px" },
            { flexGrow: 1, flexShrink: 1, flexBasis: "300px" },
            { flexGrow: 1, flexShrink: 1, flexBasis: "300px" },
            { flexGrow: 1, flexShrink: 1, flexBasis: "300px" },
        ],
    },
    {
        name: "Sidebar Layout",
        container: {
            display: "flex",
            flexDirection: "row",
            justifyContent: "flex-start",
            alignItems: "stretch",
            flexWrap: "nowrap",
            gap: "1rem",
        },
        items: [
            { flexGrow: 0, flexShrink: 0, flexBasis: "200px" },
            { flexGrow: 1, flexShrink: 1, flexBasis: "0" },
        ],
    },
];

// Presets for grid layouts
const GRID_PRESETS = [
    {
        name: "Basic Grid",
        container: {
            display: "grid",
            gridTemplateColumns: "repeat(3, 1fr)",
            gridTemplateRows: "auto",
            gridGap: "1rem",
            gridAutoFlow: "row",
        },
        items: Array(6).fill({}),
    },
    {
        name: "Dashboard Layout",
        container: {
            display: "grid",
            gridTemplateColumns: "repeat(4, 1fr)",
            gridTemplateRows: "auto auto 1fr",
            gridGap: "1rem",
            gridAutoFlow: "row",
        },
        items: [
            { gridColumn: "1 / -1", gridRow: "1" },
            { gridColumn: "1 / 3", gridRow: "2" },
            { gridColumn: "3 / 5", gridRow: "2" },
            { gridColumn: "1", gridRow: "3" },
            { gridColumn: "2 / 4", gridRow: "3" },
            { gridColumn: "4", gridRow: "3" },
        ],
    },
];

export default function FlexboxGridGenerator() {
    const [toolData, setToolData] = useState(null);
    const [mode, setMode] = useState("flexbox"); // "flexbox" or "grid"
    const [copied, setCopied] = useState(false);

    // Flexbox container properties
    const [flexContainer, setFlexContainer] = useState({
        display: "flex",
        flexDirection: "row",
        justifyContent: "flex-start",
        alignItems: "stretch",
        flexWrap: "nowrap",
        alignContent: "stretch",
        gap: "0.5rem",
    });

    // Grid container properties
    const [gridContainer, setGridContainer] = useState({
        display: "grid",
        gridTemplateColumns: "repeat(3, 1fr)",
        gridTemplateRows: "auto",
        gridGap: "1rem",
        gridAutoFlow: "row",
    });

    // Flex items
    const [flexItems, setFlexItems] = useState([
        {
            id: 1,
            flexGrow: 0,
            flexShrink: 1,
            flexBasis: "auto",
            content: "Item 1",
        },
        {
            id: 2,
            flexGrow: 0,
            flexShrink: 1,
            flexBasis: "auto",
            content: "Item 2",
        },
        {
            id: 3,
            flexGrow: 0,
            flexShrink: 1,
            flexBasis: "auto",
            content: "Item 3",
        },
    ]);

    // Grid items
    const [gridItems, setGridItems] = useState([
        { id: 1, gridColumn: "auto", gridRow: "auto", content: "Item 1" },
        { id: 2, gridColumn: "auto", gridRow: "auto", content: "Item 2" },
        { id: 3, gridColumn: "auto", gridRow: "auto", content: "Item 3" },
        { id: 4, gridColumn: "auto", gridRow: "auto", content: "Item 4" },
        { id: 5, gridColumn: "auto", gridRow: "auto", content: "Item 5" },
        { id: 6, gridColumn: "auto", gridRow: "auto", content: "Item 6" },
    ]);

    useEffect(() => {
        // Get the tool data
        const data = getToolById("flexboxgrid");
        setToolData(data);
    }, []);

    // Get the current container properties based on mode
    const getContainerProps = () => {
        return mode === "flexbox" ? flexContainer : gridContainer;
    };

    // Get the current items based on mode
    const getItems = () => {
        return mode === "flexbox" ? flexItems : gridItems;
    };

    // Update a flex item property
    const updateFlexItem = (id, property, value) => {
        setFlexItems(
            flexItems.map((item) =>
                item.id === id ? { ...item, [property]: value } : item
            )
        );
    };

    // Update a grid item property
    const updateGridItem = (id, property, value) => {
        setGridItems(
            gridItems.map((item) =>
                item.id === id ? { ...item, [property]: value } : item
            )
        );
    };

    // Add a new item
    const addItem = () => {
        if (mode === "flexbox") {
            const newId = Math.max(0, ...flexItems.map((item) => item.id)) + 1;
            setFlexItems([
                ...flexItems,
                {
                    id: newId,
                    flexGrow: 0,
                    flexShrink: 1,
                    flexBasis: "auto",
                    content: `Item ${newId}`,
                },
            ]);
        } else {
            const newId = Math.max(0, ...gridItems.map((item) => item.id)) + 1;
            setGridItems([
                ...gridItems,
                {
                    id: newId,
                    gridColumn: "auto",
                    gridRow: "auto",
                    content: `Item ${newId}`,
                },
            ]);
        }
    };

    // Remove an item
    const removeItem = (id) => {
        if (mode === "flexbox") {
            if (flexItems.length <= 1) return; // Don't remove the last item
            setFlexItems(flexItems.filter((item) => item.id !== id));
        } else {
            if (gridItems.length <= 1) return; // Don't remove the last item
            setGridItems(gridItems.filter((item) => item.id !== id));
        }
    };

    // Load a preset
    const loadPreset = (preset) => {
        if (mode === "flexbox") {
            setFlexContainer(preset.container);
            setFlexItems(
                preset.items.map((item, index) => ({
                    ...item,
                    id: index + 1,
                    content: `Item ${index + 1}`,
                }))
            );
        } else {
            setGridContainer(preset.container);
            setGridItems(
                preset.items.map((item, index) => ({
                    ...item,
                    id: index + 1,
                    content: `Item ${index + 1}`,
                }))
            );
        }
    };

    // Generate CSS code
    const generateCSSCode = () => {
        const containerProps = getContainerProps();
        const items = getItems();

        let css = `.container {\n`;
        Object.entries(containerProps).forEach(([prop, value]) => {
            css += `  ${prop}: ${value};\n`;
        });
        css += `}\n\n`;

        if (mode === "flexbox") {
            items.forEach((item, index) => {
                css += `.item-${index + 1} {\n`;
                css += `  flex-grow: ${item.flexGrow};\n`;
                css += `  flex-shrink: ${item.flexShrink};\n`;
                css += `  flex-basis: ${item.flexBasis};\n`;
                css += `}\n\n`;
            });
        } else {
            items.forEach((item, index) => {
                if (item.gridColumn !== "auto" || item.gridRow !== "auto") {
                    css += `.item-${index + 1} {\n`;
                    if (item.gridColumn !== "auto") {
                        css += `  grid-column: ${item.gridColumn};\n`;
                    }
                    if (item.gridRow !== "auto") {
                        css += `  grid-row: ${item.gridRow};\n`;
                    }
                    css += `}\n\n`;
                }
            });
        }

        return css;
    };

    // Copy CSS code to clipboard
    const copyToClipboard = () => {
        navigator.clipboard.writeText(generateCSSCode()).then(() => {
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        });
    };

    // Get container style for preview
    const getContainerStyle = () => {
        const containerProps = getContainerProps();
        return {
            ...containerProps,
            border: "2px dashed #cbd5e1",
            padding: "1rem",
            backgroundColor: "#f8fafc",
            minHeight: "200px",
        };
    };

    // Get item style based on mode and item properties
    const getItemStyle = (item, index) => {
        const baseStyle = {
            padding: "1rem",
            backgroundColor: "#e2e8f0",
            border: "1px solid #94a3b8",
            borderRadius: "0.25rem",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            fontWeight: "bold",
            color: "#334155",
        };

        if (mode === "flexbox") {
            return {
                ...baseStyle,
                flexGrow: item.flexGrow,
                flexShrink: item.flexShrink,
                flexBasis: item.flexBasis,
            };
        } else {
            return {
                ...baseStyle,
                gridColumn: item.gridColumn,
                gridRow: item.gridRow,
            };
        }
    };

    return (
        <ToolContainer
            className="py-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-colors duration-200"
            containerWidth={toolData?.containerWidth || "full"}>
            <ToolHeader
                toolData={toolData}
                defaultTitle="Flexbox & Grid Generator"
                defaultIcon="Layout"
                defaultDescription="Create and customize flexbox and CSS grid layouts with an interactive visual editor. Adjust container and item properties, see real-time previews, and generate clean CSS code."
            />

            <div className="mb-6">
                <div className="flex space-x-4 mb-6">
                    <button
                        className={`px-4 py-2 rounded-lg ${
                            mode === "flexbox"
                                ? "bg-primary text-white"
                                : "bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white"
                        }`}
                        onClick={() => setMode("flexbox")}>
                        Flexbox Mode
                    </button>
                    <button
                        className={`px-4 py-2 rounded-lg ${
                            mode === "grid"
                                ? "bg-primary text-white"
                                : "bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white"
                        }`}
                        onClick={() => setMode("grid")}>
                        Grid Mode
                    </button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Controls Panel */}
                    <div className="lg:col-span-1 space-y-6">
                        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                                Container Properties
                            </h2>

                            {mode === "flexbox" ? (
                                <div className="space-y-4">
                                    {/* Flex Direction */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            flex-direction
                                        </label>
                                        <select
                                            value={flexContainer.flexDirection}
                                            onChange={(e) =>
                                                setFlexContainer({
                                                    ...flexContainer,
                                                    flexDirection:
                                                        e.target.value,
                                                })
                                            }
                                            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800">
                                            {FLEX_DIRECTIONS.map(
                                                (direction) => (
                                                    <option
                                                        key={direction}
                                                        value={direction}>
                                                        {direction}
                                                    </option>
                                                )
                                            )}
                                        </select>
                                    </div>

                                    {/* Justify Content */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            justify-content
                                        </label>
                                        <select
                                            value={flexContainer.justifyContent}
                                            onChange={(e) =>
                                                setFlexContainer({
                                                    ...flexContainer,
                                                    justifyContent:
                                                        e.target.value,
                                                })
                                            }
                                            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800">
                                            {JUSTIFY_CONTENTS.map((value) => (
                                                <option
                                                    key={value}
                                                    value={value}>
                                                    {value}
                                                </option>
                                            ))}
                                        </select>
                                    </div>

                                    {/* Align Items */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            align-items
                                        </label>
                                        <select
                                            value={flexContainer.alignItems}
                                            onChange={(e) =>
                                                setFlexContainer({
                                                    ...flexContainer,
                                                    alignItems: e.target.value,
                                                })
                                            }
                                            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800">
                                            {ALIGN_ITEMS.map((value) => (
                                                <option
                                                    key={value}
                                                    value={value}>
                                                    {value}
                                                </option>
                                            ))}
                                        </select>
                                    </div>

                                    {/* Flex Wrap */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            flex-wrap
                                        </label>
                                        <select
                                            value={flexContainer.flexWrap}
                                            onChange={(e) =>
                                                setFlexContainer({
                                                    ...flexContainer,
                                                    flexWrap: e.target.value,
                                                })
                                            }
                                            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800">
                                            {FLEX_WRAPS.map((value) => (
                                                <option
                                                    key={value}
                                                    value={value}>
                                                    {value}
                                                </option>
                                            ))}
                                        </select>
                                    </div>

                                    {/* Align Content (only visible when flex-wrap is not nowrap) */}
                                    {flexContainer.flexWrap !== "nowrap" && (
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                align-content
                                            </label>
                                            <select
                                                value={
                                                    flexContainer.alignContent
                                                }
                                                onChange={(e) =>
                                                    setFlexContainer({
                                                        ...flexContainer,
                                                        alignContent:
                                                            e.target.value,
                                                    })
                                                }
                                                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800">
                                                {ALIGN_CONTENTS.map((value) => (
                                                    <option
                                                        key={value}
                                                        value={value}>
                                                        {value}
                                                    </option>
                                                ))}
                                            </select>
                                        </div>
                                    )}

                                    {/* Gap */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            gap
                                        </label>
                                        <select
                                            value={flexContainer.gap}
                                            onChange={(e) =>
                                                setFlexContainer({
                                                    ...flexContainer,
                                                    gap: e.target.value,
                                                })
                                            }
                                            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800">
                                            {GAP_SIZES.map((value) => (
                                                <option
                                                    key={value}
                                                    value={value}>
                                                    {value}
                                                </option>
                                            ))}
                                        </select>
                                    </div>
                                </div>
                            ) : (
                                <div className="space-y-4">
                                    {/* Grid Template Columns */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            grid-template-columns
                                        </label>
                                        <select
                                            value={
                                                gridContainer.gridTemplateColumns
                                            }
                                            onChange={(e) =>
                                                setGridContainer({
                                                    ...gridContainer,
                                                    gridTemplateColumns:
                                                        e.target.value,
                                                })
                                            }
                                            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800">
                                            {GRID_TEMPLATE_COLUMNS.map(
                                                (value) => (
                                                    <option
                                                        key={value}
                                                        value={value}>
                                                        {value}
                                                    </option>
                                                )
                                            )}
                                        </select>
                                    </div>

                                    {/* Grid Template Rows */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            grid-template-rows
                                        </label>
                                        <select
                                            value={
                                                gridContainer.gridTemplateRows
                                            }
                                            onChange={(e) =>
                                                setGridContainer({
                                                    ...gridContainer,
                                                    gridTemplateRows:
                                                        e.target.value,
                                                })
                                            }
                                            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800">
                                            {GRID_TEMPLATE_ROWS.map((value) => (
                                                <option
                                                    key={value}
                                                    value={value}>
                                                    {value}
                                                </option>
                                            ))}
                                        </select>
                                    </div>

                                    {/* Grid Gap */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            grid-gap
                                        </label>
                                        <select
                                            value={gridContainer.gridGap}
                                            onChange={(e) =>
                                                setGridContainer({
                                                    ...gridContainer,
                                                    gridGap: e.target.value,
                                                })
                                            }
                                            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800">
                                            {GAP_SIZES.map((value) => (
                                                <option
                                                    key={value}
                                                    value={value}>
                                                    {value}
                                                </option>
                                            ))}
                                        </select>
                                    </div>

                                    {/* Grid Auto Flow */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            grid-auto-flow
                                        </label>
                                        <select
                                            value={gridContainer.gridAutoFlow}
                                            onChange={(e) =>
                                                setGridContainer({
                                                    ...gridContainer,
                                                    gridAutoFlow:
                                                        e.target.value,
                                                })
                                            }
                                            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800">
                                            {GRID_AUTO_FLOWS.map((value) => (
                                                <option
                                                    key={value}
                                                    value={value}>
                                                    {value}
                                                </option>
                                            ))}
                                        </select>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Presets */}
                        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                                Presets
                            </h2>
                            <div className="space-y-2">
                                {(mode === "flexbox"
                                    ? FLEXBOX_PRESETS
                                    : GRID_PRESETS
                                ).map((preset, index) => (
                                    <button
                                        key={index}
                                        className="w-full text-left p-2 bg-white dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-600 rounded border border-gray-300 dark:border-gray-600"
                                        onClick={() => loadPreset(preset)}>
                                        {preset.name}
                                    </button>
                                ))}
                            </div>
                        </div>
                    </div>

                    {/* Preview and Item Controls */}
                    <div className="lg:col-span-2 space-y-6">
                        {/* Preview */}
                        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                                Preview
                            </h2>
                            <div
                                style={getContainerStyle()}
                                className="dark:bg-gray-800 dark:border-gray-600">
                                {getItems().map((item, index) => (
                                    <div
                                        key={item.id}
                                        style={getItemStyle(item, index)}>
                                        {item.content}
                                    </div>
                                ))}
                            </div>
                            <div className="mt-4 flex justify-end">
                                <button
                                    className="px-3 py-1 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
                                    onClick={addItem}>
                                    <LucideIcon
                                        name="Plus"
                                        size={16}
                                        className="inline mr-1"
                                    />
                                    Add Item
                                </button>
                            </div>
                        </div>

                        {/* Item Properties */}
                        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                                Item Properties
                            </h2>
                            <div className="space-y-4">
                                {mode === "flexbox" ? (
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                        {flexItems.map((item) => (
                                            <div
                                                key={item.id}
                                                className="p-3 bg-white dark:bg-gray-800 rounded border border-gray-300 dark:border-gray-600">
                                                <div className="flex justify-between items-center mb-2">
                                                    <span className="font-medium">
                                                        {item.content}
                                                    </span>
                                                    <button
                                                        className="text-red-500 hover:text-red-700"
                                                        onClick={() =>
                                                            removeItem(item.id)
                                                        }
                                                        disabled={
                                                            flexItems.length <=
                                                            1
                                                        }>
                                                        <LucideIcon
                                                            name="X"
                                                            size={16}
                                                        />
                                                    </button>
                                                </div>
                                                <div className="space-y-2">
                                                    <div>
                                                        <label className="block text-xs text-gray-500 dark:text-gray-400">
                                                            flex-grow
                                                        </label>
                                                        <input
                                                            type="number"
                                                            min="0"
                                                            step="1"
                                                            value={
                                                                item.flexGrow
                                                            }
                                                            onChange={(e) =>
                                                                updateFlexItem(
                                                                    item.id,
                                                                    "flexGrow",
                                                                    parseInt(
                                                                        e.target
                                                                            .value
                                                                    )
                                                                )
                                                            }
                                                            className="w-full p-1 text-sm border border-gray-300 dark:border-gray-600 rounded"
                                                        />
                                                    </div>
                                                    <div>
                                                        <label className="block text-xs text-gray-500 dark:text-gray-400">
                                                            flex-shrink
                                                        </label>
                                                        <input
                                                            type="number"
                                                            min="0"
                                                            step="1"
                                                            value={
                                                                item.flexShrink
                                                            }
                                                            onChange={(e) =>
                                                                updateFlexItem(
                                                                    item.id,
                                                                    "flexShrink",
                                                                    parseInt(
                                                                        e.target
                                                                            .value
                                                                    )
                                                                )
                                                            }
                                                            className="w-full p-1 text-sm border border-gray-300 dark:border-gray-600 rounded"
                                                        />
                                                    </div>
                                                    <div>
                                                        <label className="block text-xs text-gray-500 dark:text-gray-400">
                                                            flex-basis
                                                        </label>
                                                        <input
                                                            type="text"
                                                            value={
                                                                item.flexBasis
                                                            }
                                                            onChange={(e) =>
                                                                updateFlexItem(
                                                                    item.id,
                                                                    "flexBasis",
                                                                    e.target
                                                                        .value
                                                                )
                                                            }
                                                            className="w-full p-1 text-sm border border-gray-300 dark:border-gray-600 rounded"
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                        {gridItems.map((item) => (
                                            <div
                                                key={item.id}
                                                className="p-3 bg-white dark:bg-gray-800 rounded border border-gray-300 dark:border-gray-600">
                                                <div className="flex justify-between items-center mb-2">
                                                    <span className="font-medium">
                                                        {item.content}
                                                    </span>
                                                    <button
                                                        className="text-red-500 hover:text-red-700"
                                                        onClick={() =>
                                                            removeItem(item.id)
                                                        }
                                                        disabled={
                                                            gridItems.length <=
                                                            1
                                                        }>
                                                        <LucideIcon
                                                            name="X"
                                                            size={16}
                                                        />
                                                    </button>
                                                </div>
                                                <div className="space-y-2">
                                                    <div>
                                                        <label className="block text-xs text-gray-500 dark:text-gray-400">
                                                            grid-column
                                                        </label>
                                                        <input
                                                            type="text"
                                                            value={
                                                                item.gridColumn
                                                            }
                                                            onChange={(e) =>
                                                                updateGridItem(
                                                                    item.id,
                                                                    "gridColumn",
                                                                    e.target
                                                                        .value
                                                                )
                                                            }
                                                            className="w-full p-1 text-sm border border-gray-300 dark:border-gray-600 rounded"
                                                        />
                                                    </div>
                                                    <div>
                                                        <label className="block text-xs text-gray-500 dark:text-gray-400">
                                                            grid-row
                                                        </label>
                                                        <input
                                                            type="text"
                                                            value={item.gridRow}
                                                            onChange={(e) =>
                                                                updateGridItem(
                                                                    item.id,
                                                                    "gridRow",
                                                                    e.target
                                                                        .value
                                                                )
                                                            }
                                                            className="w-full p-1 text-sm border border-gray-300 dark:border-gray-600 rounded"
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* CSS Output */}
                        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <div className="flex justify-between items-center mb-4">
                                <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
                                    CSS Code
                                </h2>
                                <button
                                    className="px-3 py-1 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors flex items-center"
                                    onClick={copyToClipboard}>
                                    <LucideIcon
                                        name={copied ? "Check" : "Copy"}
                                        size={16}
                                        className="mr-1"
                                    />
                                    {copied ? "Copied!" : "Copy Code"}
                                </button>
                            </div>
                            <pre className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-300 dark:border-gray-600 overflow-x-auto text-sm">
                                {generateCSSCode()}
                            </pre>
                        </div>
                    </div>
                </div>
            </div>
        </ToolContainer>
    );
}
