import React, { useState, useEffect } from "react";
import { getToolById } from "../../data/tools.js";
import ToolHeader from "../ui/ToolHeader.jsx";
import ToolContainer from "../ui/ToolContainer.jsx";
import LucideIcon from "../ui/LucideIcon.jsx";

// Sample patterns for quick testing
const SAMPLE_PATTERNS = [
    {
        name: "Email Validation",
        pattern: "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$",
        testString: "<EMAIL>\ninvalid-email\<EMAIL>",
        flags: "gm",
        description: "Validates email addresses",
    },
    {
        name: "URL Validation",
        pattern:
            "https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)",
        testString:
            "https://devbottle.com\nhttp://www.example.com/path?query=123\nNot a URL",
        flags: "g",
        description: "Matches URLs with http or https protocol",
    },
    {
        name: "Phone Number",
        pattern:
            "\\+?\\d{1,4}?[-\\.\\s]?\\(?\\d{1,3}?\\)?[-\\.\\s]?\\d{1,4}[-\\.\\s]?\\d{1,4}[-\\.\\s]?\\d{1,9}",
        testString: "+****************\n555-123-4567\n5551234567",
        flags: "g",
        description: "Matches various phone number formats",
    },
    {
        name: "Date Format (YYYY-MM-DD)",
        pattern: "\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\\d|3[01])",
        testString: "2023-01-15\n2022-12-31\n01/15/2023\n2023-13-01",
        flags: "g",
        description: "Matches dates in YYYY-MM-DD format",
    },
    {
        name: "Password Strength",
        pattern:
            "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$",
        testString: "Passw0rd!\nweakpassword\nNoSpecialChar1\nShort1!",
        flags: "m",
        description:
            "Checks if password has minimum 8 characters, at least one uppercase, one lowercase, one number and one special character",
    },
    {
        name: "IP Address",
        pattern:
            "\\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\b",
        testString: "***********\n10.0.0.1\n256.256.256.256\nNot an IP",
        flags: "g",
        description: "Matches IPv4 addresses",
    },
    {
        name: "HTML Tag",
        pattern:
            "<([a-z][a-z0-9]*)(\\s[^>]*)?(?:>([\\s\\S]*?)<\\/\\1>|\\s*\\/>)",
        testString:
            '<div class="container">Content</div>\n<img src="image.jpg" />\n<p>Text</p>',
        flags: "g",
        description:
            "Matches HTML tags with capture groups for tag name, attributes, and content",
    },
    {
        name: "Credit Card Number",
        pattern:
            "(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|3[47][0-9]{13}|3(?:0[0-5]|[68][0-9])[0-9]{11}|6(?:011|5[0-9]{2})[0-9]{12}|(?:2131|1800|35\\d{3})\\d{11})",
        testString:
            "****************\n5500000000000004\n378282246310005\nNot a card number",
        flags: "g",
        description:
            "Matches major credit card numbers (Visa, Mastercard, Amex, etc.)",
    },
];

// Regex cheat sheet reference
const REGEX_REFERENCE = {
    characterClasses: [
        { pattern: ".", description: "Any character except newline" },
        { pattern: "\\w", description: "Word character [A-Za-z0-9_]" },
        { pattern: "\\d", description: "Digit [0-9]" },
        {
            pattern: "\\s",
            description: "Whitespace character (space, tab, newline)",
        },
        { pattern: "\\W", description: "NOT word character" },
        { pattern: "\\D", description: "NOT digit" },
        { pattern: "\\S", description: "NOT whitespace character" },
    ],
    anchors: [
        { pattern: "^", description: "Start of string or line" },
        { pattern: "$", description: "End of string or line" },
        { pattern: "\\b", description: "Word boundary" },
        { pattern: "\\B", description: "NOT word boundary" },
    ],
    quantifiers: [
        { pattern: "*", description: "0 or more" },
        { pattern: "+", description: "1 or more" },
        { pattern: "?", description: "0 or 1" },
        { pattern: "{n}", description: "Exactly n times" },
        { pattern: "{n,}", description: "n or more times" },
        { pattern: "{n,m}", description: "Between n and m times" },
    ],
    groups: [
        { pattern: "(abc)", description: "Capturing group" },
        { pattern: "(?:abc)", description: "Non-capturing group" },
        { pattern: "(?=abc)", description: "Positive lookahead" },
        { pattern: "(?!abc)", description: "Negative lookahead" },
        { pattern: "(?<=abc)", description: "Positive lookbehind" },
        { pattern: "(?<!abc)", description: "Negative lookbehind" },
    ],
    characterSets: [
        { pattern: "[abc]", description: "Any of the characters a, b, or c" },
        { pattern: "[^abc]", description: "Any character except a, b, or c" },
        { pattern: "[a-z]", description: "Any character from a to z" },
        { pattern: "[0-9]", description: "Any digit from 0 to 9" },
    ],
    specialCharacters: [
        { pattern: "\\", description: "Escape character" },
        { pattern: "|", description: "Alternation (OR)" },
        { pattern: "\\n", description: "Newline" },
        { pattern: "\\t", description: "Tab" },
        { pattern: "\\r", description: "Carriage return" },
    ],
};

export default function RegExTester() {
    const [toolData, setToolData] = useState(null);
    const [pattern, setPattern] = useState("");
    const [testString, setTestString] = useState("");
    const [flags, setFlags] = useState("g");
    const [matches, setMatches] = useState([]);
    const [error, setError] = useState("");
    const [copied, setCopied] = useState(false);
    const [highlightedText, setHighlightedText] = useState("");
    const [matchInfo, setMatchInfo] = useState([]);
    const [selectedSample, setSelectedSample] = useState(null);
    const [showCheatSheet, setShowCheatSheet] = useState(false);

    // Flag options
    const flagOptions = [
        {
            value: "g",
            label: "Global (g)",
            description:
                "Find all matches rather than stopping after the first match",
        },
        {
            value: "i",
            label: "Case-insensitive (i)",
            description: "Ignore case when matching",
        },
        {
            value: "m",
            label: "Multiline (m)",
            description:
                "^ and $ match start/end of line, not just start/end of string",
        },
        {
            value: "s",
            label: "Dot All (s)",
            description: "Dot (.) matches newline characters",
        },
        {
            value: "u",
            label: "Unicode (u)",
            description: "Treat pattern as a sequence of Unicode code points",
        },
        {
            value: "y",
            label: "Sticky (y)",
            description:
                "Matches only from the index indicated by lastIndex property",
        },
    ];

    useEffect(() => {
        // Get the tool data
        const data = getToolById("regextester");
        setToolData(data);
    }, []);

    useEffect(() => {
        // Test the regex whenever pattern, testString, or flags change
        testRegex();
    }, [pattern, testString, flags]);

    // Load a sample pattern
    const loadSample = (sample) => {
        setPattern(sample.pattern);
        setTestString(sample.testString);
        setFlags(sample.flags);
        setSelectedSample(sample.name);
        // The regex will be tested automatically by the useEffect
    };

    // Test the regex pattern against the test string
    const testRegex = () => {
        setError("");
        setMatches([]);
        setMatchInfo([]);
        setHighlightedText("");

        if (!pattern || !testString) {
            return;
        }

        try {
            // Create the RegExp object with the pattern and flags
            const regex = new RegExp(pattern, flags);

            // Find all matches
            const allMatches = [];

            // For global flag, we need to reset lastIndex to avoid issues with subsequent tests
            if (flags.includes("g")) {
                // Create a fresh regex to ensure lastIndex is reset
                const freshRegex = new RegExp(pattern, flags);
                let match;

                // Global flag - find all matches
                while ((match = freshRegex.exec(testString)) !== null) {
                    allMatches.push({
                        fullMatch: match[0],
                        groups: match.slice(1),
                        index: match.index,
                        input: match.input,
                    });

                    // Prevent infinite loops for zero-length matches
                    if (match.index === freshRegex.lastIndex) {
                        freshRegex.lastIndex++;
                    }
                }
            } else {
                // Non-global - just find the first match
                const match = regex.exec(testString);
                if (match) {
                    allMatches.push({
                        fullMatch: match[0],
                        groups: match.slice(1),
                        index: match.index,
                        input: match.input,
                    });
                }
            }

            setMatches(allMatches);

            // Create match info for display
            const matchInfoData = allMatches.map((match, idx) => ({
                id: idx,
                fullMatch: match.fullMatch,
                groups: match.groups,
                index: match.index,
                length: match.fullMatch.length,
            }));

            setMatchInfo(matchInfoData);

            // Create highlighted text
            highlightMatches(allMatches);
        } catch (e) {
            setError(`Invalid regular expression: ${e.message}`);
        }
    };

    // Highlight matches in the test string
    const highlightMatches = (matches) => {
        if (matches.length === 0) {
            setHighlightedText(testString);
            return;
        }

        // Sort matches by index to process them in order
        const sortedMatches = [...matches].sort((a, b) => a.index - b.index);

        let result = "";
        let lastIndex = 0;

        sortedMatches.forEach((match, idx) => {
            // Add text before this match
            result += testString.substring(lastIndex, match.index);

            // Add the match with highlighting
            result += `<mark class="bg-yellow-200 dark:bg-yellow-700 px-0.5 rounded" data-match-id="${idx}">${match.fullMatch}</mark>`;

            // Update lastIndex for next iteration
            lastIndex = match.index + match.fullMatch.length;
        });

        // Add any remaining text after the last match
        result += testString.substring(lastIndex);

        // Replace newlines with <br> for proper HTML display
        result = result.replace(/\n/g, "<br>");

        setHighlightedText(result);
    };

    // Copy regex pattern to clipboard
    const copyToClipboard = () => {
        navigator.clipboard.writeText(pattern).then(() => {
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        });
    };

    // Clear all inputs
    const clearAll = () => {
        setPattern("");
        setTestString("");
        setFlags("g");
        setMatches([]);
        setError("");
        setHighlightedText("");
        setMatchInfo([]);
        setSelectedSample(null);
    };

    // Toggle a flag
    const toggleFlag = (flag) => {
        if (flags.includes(flag)) {
            setFlags(flags.replace(flag, ""));
        } else {
            setFlags(flags + flag);
        }
    };

    return (
        <ToolContainer
            className="py-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-colors duration-200"
            containerWidth={toolData?.containerWidth || "default"}>
            <ToolHeader
                toolData={toolData}
                defaultTitle="RegEx Tester"
                defaultIcon="FileSearch"
                defaultDescription="Test and debug regular expressions with real-time matching and highlighting. Perfect for developers working with form validation, text parsing, or data extraction."
            />

            {/* Cheat Sheet Toggle Button */}
            <div className="flex justify-center mt-4 mb-6">
                <button
                    type="button"
                    className="px-4 py-2 bg-gray-100 dark:bg-gray-700 rounded-md text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center"
                    onClick={() => setShowCheatSheet(!showCheatSheet)}>
                    <LucideIcon name="BookOpen" size={16} className="mr-2" />
                    {showCheatSheet
                        ? "Hide RegEx Cheat Sheet"
                        : "Show RegEx Cheat Sheet"}
                </button>
            </div>

            {/* RegEx Cheat Sheet - Full Width */}
            {showCheatSheet && (
                <div className="mb-8 p-6 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                    <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
                        RegEx Cheat Sheet
                    </h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {/* Character Classes */}
                        <div>
                            <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider mb-2">
                                Character Classes
                            </h4>
                            <div className="space-y-1.5">
                                {REGEX_REFERENCE.characterClasses.map(
                                    (item, index) => (
                                        <div
                                            key={index}
                                            className="flex items-center">
                                            <code className="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-800 rounded text-primary dark:text-primary-dark font-mono text-sm mr-2 w-10 text-center">
                                                {item.pattern}
                                            </code>
                                            <span className="text-sm text-gray-600 dark:text-gray-400">
                                                {item.description}
                                            </span>
                                        </div>
                                    )
                                )}
                            </div>
                        </div>

                        {/* Anchors */}
                        <div>
                            <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider mb-2">
                                Anchors
                            </h4>
                            <div className="space-y-1.5">
                                {REGEX_REFERENCE.anchors.map((item, index) => (
                                    <div
                                        key={index}
                                        className="flex items-center">
                                        <code className="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-800 rounded text-primary dark:text-primary-dark font-mono text-sm mr-2 w-10 text-center">
                                            {item.pattern}
                                        </code>
                                        <span className="text-sm text-gray-600 dark:text-gray-400">
                                            {item.description}
                                        </span>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Quantifiers */}
                        <div>
                            <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider mb-2">
                                Quantifiers
                            </h4>
                            <div className="space-y-1.5">
                                {REGEX_REFERENCE.quantifiers.map(
                                    (item, index) => (
                                        <div
                                            key={index}
                                            className="flex items-center">
                                            <code className="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-800 rounded text-primary dark:text-primary-dark font-mono text-sm mr-2 w-10 text-center">
                                                {item.pattern}
                                            </code>
                                            <span className="text-sm text-gray-600 dark:text-gray-400">
                                                {item.description}
                                            </span>
                                        </div>
                                    )
                                )}
                            </div>
                        </div>

                        {/* Groups */}
                        <div>
                            <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider mb-2">
                                Groups & Lookarounds
                            </h4>
                            <div className="space-y-1.5">
                                {REGEX_REFERENCE.groups.map((item, index) => (
                                    <div
                                        key={index}
                                        className="flex items-center">
                                        <code className="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-800 rounded text-primary dark:text-primary-dark font-mono text-sm mr-2 min-w-16 text-center">
                                            {item.pattern}
                                        </code>
                                        <span className="text-sm text-gray-600 dark:text-gray-400">
                                            {item.description}
                                        </span>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Character Sets */}
                        <div>
                            <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider mb-2">
                                Character Sets
                            </h4>
                            <div className="space-y-1.5">
                                {REGEX_REFERENCE.characterSets.map(
                                    (item, index) => (
                                        <div
                                            key={index}
                                            className="flex items-center">
                                            <code className="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-800 rounded text-primary dark:text-primary-dark font-mono text-sm mr-2 min-w-16 text-center">
                                                {item.pattern}
                                            </code>
                                            <span className="text-sm text-gray-600 dark:text-gray-400">
                                                {item.description}
                                            </span>
                                        </div>
                                    )
                                )}
                            </div>
                        </div>

                        {/* Special Characters */}
                        <div>
                            <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider mb-2">
                                Special Characters
                            </h4>
                            <div className="space-y-1.5">
                                {REGEX_REFERENCE.specialCharacters.map(
                                    (item, index) => (
                                        <div
                                            key={index}
                                            className="flex items-center">
                                            <code className="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-800 rounded text-primary dark:text-primary-dark font-mono text-sm mr-2 w-10 text-center">
                                                {item.pattern}
                                            </code>
                                            <span className="text-sm text-gray-600 dark:text-gray-400">
                                                {item.description}
                                            </span>
                                        </div>
                                    )
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            )}

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Input Panel */}
                <div>
                    <div className="mb-6">
                        <div className="flex justify-between items-center mb-2">
                            <label
                                htmlFor="pattern"
                                className="block text-sm font-semibold text-gray-800 dark:text-gray-200">
                                Regular Expression Pattern
                            </label>
                            <div className="flex space-x-2">
                                <button
                                    type="button"
                                    className="px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-md text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                                    onClick={copyToClipboard}>
                                    {copied ? "Copied!" : "Copy"}
                                </button>
                                <button
                                    type="button"
                                    className="px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-md text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                                    onClick={clearAll}>
                                    Clear All
                                </button>
                            </div>
                        </div>
                        <div className="flex">
                            <span className="inline-flex items-center px-3 text-gray-500 bg-gray-100 dark:bg-gray-700 dark:text-gray-400 border border-r-0 border-gray-300 dark:border-gray-600 rounded-l-md">
                                /
                            </span>
                            <input
                                id="pattern"
                                type="text"
                                className="flex-1 p-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark transition-all"
                                value={pattern}
                                onChange={(e) => setPattern(e.target.value)}
                                placeholder="Enter regex pattern (e.g., \d{3}-\d{2}-\d{4})"
                            />
                            <span className="inline-flex items-center px-3 text-gray-500 bg-gray-100 dark:bg-gray-700 dark:text-gray-400 border border-l-0 border-gray-300 dark:border-gray-600 rounded-r-md">
                                /{flags}
                            </span>
                        </div>
                        {error && (
                            <p className="mt-2 text-red-500 text-sm">{error}</p>
                        )}
                    </div>

                    {/* Flags Section */}
                    <div className="mb-6">
                        <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200 mb-2">
                            Flags
                        </h3>
                        <div className="flex flex-wrap gap-2">
                            {flagOptions.map((flag) => (
                                <button
                                    key={flag.value}
                                    type="button"
                                    className={`px-3 py-1.5 text-sm rounded-md flex items-center gap-2 ${
                                        flags.includes(flag.value)
                                            ? "bg-primary dark:bg-primary-dark text-white"
                                            : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                                    } transition-colors`}
                                    onClick={() => toggleFlag(flag.value)}
                                    title={flag.description}>
                                    {flags.includes(flag.value) && (
                                        <LucideIcon name="Check" size={14} />
                                    )}
                                    {flag.label}
                                </button>
                            ))}
                        </div>
                    </div>

                    {/* Test String Input */}
                    <div className="mb-6">
                        <label
                            htmlFor="testString"
                            className="block text-sm font-semibold text-gray-800 dark:text-gray-200 mb-2">
                            Test String
                        </label>
                        <textarea
                            id="testString"
                            className="w-full h-48 p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-mono text-sm focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark transition-all"
                            value={testString}
                            onChange={(e) => setTestString(e.target.value)}
                            placeholder="Enter text to test against your regular expression"></textarea>
                    </div>

                    {/* Sample Patterns */}
                    <div className="mb-6">
                        <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200 mb-2">
                            Sample Patterns
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                            {SAMPLE_PATTERNS.map((sample) => (
                                <button
                                    key={sample.name}
                                    type="button"
                                    className={`p-2 text-sm rounded-md text-left ${
                                        selectedSample === sample.name
                                            ? "bg-primary/10 dark:bg-primary-dark/10 border border-primary dark:border-primary-dark"
                                            : "bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600"
                                    } transition-colors`}
                                    onClick={() => loadSample(sample)}>
                                    <div className="font-medium text-gray-800 dark:text-gray-200">
                                        {sample.name}
                                    </div>
                                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate">
                                        {sample.description}
                                    </div>
                                </button>
                            ))}
                        </div>
                    </div>
                </div>

                {/* Results Panel */}
                <div>
                    <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                        Results
                    </h2>

                    {/* Match Highlighting */}
                    <div className="mb-6">
                        <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200 mb-2">
                            Highlighted Matches{" "}
                            {matches.length > 0 && `(${matches.length} found)`}
                        </h3>
                        <div
                            className="p-4 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white min-h-[100px] font-mono text-sm overflow-auto"
                            dangerouslySetInnerHTML={{
                                __html:
                                    highlightedText ||
                                    '<span class="text-gray-400 dark:text-gray-500">No matches found</span>',
                            }}></div>
                    </div>

                    {/* Match Details */}
                    {matchInfo.length > 0 && (
                        <div>
                            <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200 mb-2">
                                Match Details
                            </h3>
                            <div className="border border-gray-300 dark:border-gray-600 rounded-md overflow-hidden">
                                <table className="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
                                    <thead className="bg-gray-50 dark:bg-gray-800">
                                        <tr>
                                            <th
                                                scope="col"
                                                className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                                Match
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                                Value
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                                Position
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600">
                                        {matchInfo.map((match, idx) => (
                                            <React.Fragment key={idx}>
                                                <tr className="bg-gray-50 dark:bg-gray-800">
                                                    <td
                                                        colSpan="3"
                                                        className="px-4 py-2 text-sm font-medium text-gray-900 dark:text-white">
                                                        Match {idx + 1}
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td className="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
                                                        Full match
                                                    </td>
                                                    <td className="px-4 py-2 text-sm font-mono text-gray-900 dark:text-white">
                                                        {match.fullMatch}
                                                    </td>
                                                    <td className="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
                                                        Index: {match.index},
                                                        Length: {match.length}
                                                    </td>
                                                </tr>
                                                {match.groups &&
                                                    match.groups.length > 0 &&
                                                    match.groups.map(
                                                        (group, groupIdx) => (
                                                            <tr
                                                                key={`${idx}-${groupIdx}`}>
                                                                <td className="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
                                                                    Group{" "}
                                                                    {groupIdx +
                                                                        1}
                                                                </td>
                                                                <td className="px-4 py-2 text-sm font-mono text-gray-900 dark:text-white">
                                                                    {group || (
                                                                        <span className="text-gray-400">
                                                                            (empty)
                                                                        </span>
                                                                    )}
                                                                </td>
                                                                <td className="px-4 py-2 text-sm text-gray-500 dark:text-gray-400"></td>
                                                            </tr>
                                                        )
                                                    )}
                                            </React.Fragment>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    )}

                    {/* No Matches Message */}
                    {!error &&
                        pattern &&
                        testString &&
                        matchInfo.length === 0 && (
                            <div className="p-4 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-center">
                                <p className="text-gray-500 dark:text-gray-400">
                                    No matches found for this pattern
                                </p>
                            </div>
                        )}
                </div>
            </div>
        </ToolContainer>
    );
}
