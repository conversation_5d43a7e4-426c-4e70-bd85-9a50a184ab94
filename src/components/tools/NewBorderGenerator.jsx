import React, { useState, useEffect } from "react";
import { getToolById } from "../../data/tools.js";
import ToolHeader from "../ui/ToolHeader.jsx";
import ToolContainer from "../ui/ToolContainer.jsx";
import LucideIcon from "../ui/LucideIcon.jsx";

// Improved version with proper container and working preview
export default function NewBorderGenerator() {
    console.log("NewBorderGenerator component is being rendered");

    const [toolData, setToolData] = useState(null);
    const [borderType, setBorderType] = useState("zigzag");
    const [borderSides, setBorderSides] = useState("bottom");
    const [borderSize, setBorderSize] = useState(20);
    const [sizeUnit, setSizeUnit] = useState("px");
    const [angle, setAngle] = useState(90);
    const [alternating, setAlternating] = useState(false);
    const [copied, setCopied] = useState(false);

    useEffect(() => {
        // Get the tool data
        const data = getToolById("bordergenerator");
        setToolData(data);
    }, []);

    // Border types
    const BORDER_TYPES = [
        { id: "zigzag", name: "Zig-Zag" },
        { id: "scooped", name: "Scooped" },
        { id: "scalloped", name: "Scalloped" },
        { id: "wavy", name: "Wavy" },
    ];

    // Border sides
    const BORDER_SIDES = [
        { id: "bottom", name: "Bottom" },
        { id: "top", name: "Top" },
        { id: "left", name: "Left" },
        { id: "right", name: "Right" },
        { id: "top-bottom", name: "Top + Bottom" },
        { id: "left-right", name: "Left + Right" },
    ];

    // Generate the CSS mask property based on selected options
    const generateMask = () => {
        let maskValue = "";

        // Different mask patterns based on border type
        switch (borderType) {
            case "zigzag":
                // For zigzag, use a repeating linear gradient for better results
                const zigzagSize = borderSize;
                const zigzagDirection =
                    borderSides === "top" || borderSides === "bottom"
                        ? "to right"
                        : "to bottom";

                if (
                    borderSides === "top-bottom" ||
                    borderSides === "left-right"
                ) {
                    if (alternating) {
                        const positions = getSidePosition().split(", ");
                        const pos1 = positions[0];
                        const pos2 = positions[1];

                        if (borderSides === "top-bottom") {
                            maskValue = `
                                linear-gradient(${zigzagDirection},
                                    #0000 calc(25% - ${
                                        zigzagSize / 4
                                    }${sizeUnit}),
                                    #000 calc(25% - ${
                                        zigzagSize / 4
                                    }${sizeUnit}) calc(25% + ${
                                zigzagSize / 4
                            }${sizeUnit}),
                                    #0000 calc(25% + ${
                                        zigzagSize / 4
                                    }${sizeUnit}) calc(75% - ${
                                zigzagSize / 4
                            }${sizeUnit}),
                                    #000 calc(75% - ${
                                        zigzagSize / 4
                                    }${sizeUnit}) calc(75% + ${
                                zigzagSize / 4
                            }${sizeUnit}),
                                    #0000 calc(75% + ${
                                        zigzagSize / 4
                                    }${sizeUnit})
                                ) 0 0/${
                                    zigzagSize * 2
                                }${sizeUnit} ${zigzagSize}${sizeUnit}`;
                        } else {
                            maskValue = `
                                linear-gradient(${zigzagDirection},
                                    #0000 calc(25% - ${
                                        zigzagSize / 4
                                    }${sizeUnit}),
                                    #000 calc(25% - ${
                                        zigzagSize / 4
                                    }${sizeUnit}) calc(25% + ${
                                zigzagSize / 4
                            }${sizeUnit}),
                                    #0000 calc(25% + ${
                                        zigzagSize / 4
                                    }${sizeUnit}) calc(75% - ${
                                zigzagSize / 4
                            }${sizeUnit}),
                                    #000 calc(75% - ${
                                        zigzagSize / 4
                                    }${sizeUnit}) calc(75% + ${
                                zigzagSize / 4
                            }${sizeUnit}),
                                    #0000 calc(75% + ${
                                        zigzagSize / 4
                                    }${sizeUnit})
                                ) 0 0/${zigzagSize}${sizeUnit} ${
                                zigzagSize * 2
                            }${sizeUnit}`;
                        }
                    } else {
                        if (borderSides === "top-bottom") {
                            maskValue = `
                                linear-gradient(${zigzagDirection},
                                    #0000 calc(50% - ${
                                        zigzagSize / 2
                                    }${sizeUnit}),
                                    #000 calc(50% - ${
                                        zigzagSize / 2
                                    }${sizeUnit}) calc(50% + ${
                                zigzagSize / 2
                            }${sizeUnit}),
                                    #0000 calc(50% + ${
                                        zigzagSize / 2
                                    }${sizeUnit})
                                ) 0 0/${
                                    zigzagSize * 2
                                }${sizeUnit} ${zigzagSize}${sizeUnit}`;
                        } else {
                            maskValue = `
                                linear-gradient(${zigzagDirection},
                                    #0000 calc(50% - ${
                                        zigzagSize / 2
                                    }${sizeUnit}),
                                    #000 calc(50% - ${
                                        zigzagSize / 2
                                    }${sizeUnit}) calc(50% + ${
                                zigzagSize / 2
                            }${sizeUnit}),
                                    #0000 calc(50% + ${
                                        zigzagSize / 2
                                    }${sizeUnit})
                                ) 0 0/${zigzagSize}${sizeUnit} ${
                                zigzagSize * 2
                            }${sizeUnit}`;
                        }
                    }
                } else {
                    // For single sides, use a simpler zigzag pattern
                    if (borderSides === "top" || borderSides === "bottom") {
                        maskValue = `
                            linear-gradient(${zigzagDirection},
                                #0000 calc(50% - ${zigzagSize / 2}${sizeUnit}),
                                #000 calc(50% - ${
                                    zigzagSize / 2
                                }${sizeUnit}) calc(50% + ${
                            zigzagSize / 2
                        }${sizeUnit}),
                                #0000 calc(50% + ${zigzagSize / 2}${sizeUnit})
                            ) 0 0/${
                                zigzagSize * 2
                            }${sizeUnit} ${zigzagSize}${sizeUnit}`;
                    } else {
                        maskValue = `
                            linear-gradient(${zigzagDirection},
                                #0000 calc(50% - ${zigzagSize / 2}${sizeUnit}),
                                #000 calc(50% - ${
                                    zigzagSize / 2
                                }${sizeUnit}) calc(50% + ${
                            zigzagSize / 2
                        }${sizeUnit}),
                                #0000 calc(50% + ${zigzagSize / 2}${sizeUnit})
                            ) 0 0/${zigzagSize}${sizeUnit} ${
                            zigzagSize * 2
                        }${sizeUnit}`;
                    }
                }
                break;
            case "scooped":
                if (
                    borderSides === "top-bottom" ||
                    borderSides === "left-right"
                ) {
                    if (alternating) {
                        const positions = getSidePosition().split(", ");
                        const pos1 = positions[0];
                        const pos2 = positions[1];
                        maskValue = `radial-gradient(${
                            borderSize / 2
                        }${sizeUnit} at ${pos1}, #0000 98%, #000),
                                     radial-gradient(${
                                         borderSize / 2
                                     }${sizeUnit} at ${pos2}, #0000 98%, #000)`;
                    } else {
                        maskValue = `radial-gradient(${
                            borderSize / 2
                        }${sizeUnit} at ${getSidePosition()}, #0000 98%, #000)`;
                    }
                } else {
                    maskValue = `radial-gradient(${
                        borderSize / 2
                    }${sizeUnit} at ${getSidePosition()}, #0000 98%, #000)`;
                }
                break;
            case "scalloped":
                if (
                    borderSides === "top-bottom" ||
                    borderSides === "left-right"
                ) {
                    if (alternating) {
                        const positions = getSidePosition().split(", ");
                        const pos1 = positions[0];
                        const pos2 = positions[1];
                        maskValue = `radial-gradient(${
                            borderSize / 2
                        }${sizeUnit} at ${pos1}, #000 98%, #0000),
                                     radial-gradient(${
                                         borderSize / 2
                                     }${sizeUnit} at ${pos2}, #000 98%, #0000)`;
                    } else {
                        maskValue = `radial-gradient(${
                            borderSize / 2
                        }${sizeUnit} at ${getSidePosition()}, #000 98%, #0000)`;
                    }
                } else {
                    maskValue = `radial-gradient(${
                        borderSize / 2
                    }${sizeUnit} at ${getSidePosition()}, #000 98%, #0000)`;
                }
                break;
            case "wavy":
                // For wavy borders, use a radial-gradient with repeating pattern
                const waveSize = borderSize;
                const waveDirection =
                    borderSides === "top" || borderSides === "bottom"
                        ? "to right"
                        : "to bottom";

                if (
                    borderSides === "top-bottom" ||
                    borderSides === "left-right"
                ) {
                    if (alternating) {
                        if (borderSides === "top-bottom") {
                            maskValue = `
                                repeating-radial-gradient(
                                    ellipse at top,
                                    #0000 0,
                                    #0000 ${waveSize / 2}${sizeUnit},
                                    #000 ${waveSize / 2}${sizeUnit},
                                    #000 ${waveSize}${sizeUnit},
                                    #0000 ${waveSize}${sizeUnit},
                                    #0000 ${waveSize * 1.5}${sizeUnit}
                                ) 0 0/${waveSize * 2}${sizeUnit} ${
                                waveSize * 2
                            }${sizeUnit},
                                repeating-radial-gradient(
                                    ellipse at bottom,
                                    #0000 0,
                                    #0000 ${waveSize / 2}${sizeUnit},
                                    #000 ${waveSize / 2}${sizeUnit},
                                    #000 ${waveSize}${sizeUnit},
                                    #0000 ${waveSize}${sizeUnit},
                                    #0000 ${waveSize * 1.5}${sizeUnit}
                                ) 0 0/${waveSize * 2}${sizeUnit} ${
                                waveSize * 2
                            }${sizeUnit}`;
                        } else {
                            maskValue = `
                                repeating-radial-gradient(
                                    ellipse at left,
                                    #0000 0,
                                    #0000 ${waveSize / 2}${sizeUnit},
                                    #000 ${waveSize / 2}${sizeUnit},
                                    #000 ${waveSize}${sizeUnit},
                                    #0000 ${waveSize}${sizeUnit},
                                    #0000 ${waveSize * 1.5}${sizeUnit}
                                ) 0 0/${waveSize * 2}${sizeUnit} ${
                                waveSize * 2
                            }${sizeUnit},
                                repeating-radial-gradient(
                                    ellipse at right,
                                    #0000 0,
                                    #0000 ${waveSize / 2}${sizeUnit},
                                    #000 ${waveSize / 2}${sizeUnit},
                                    #000 ${waveSize}${sizeUnit},
                                    #0000 ${waveSize}${sizeUnit},
                                    #0000 ${waveSize * 1.5}${sizeUnit}
                                ) 0 0/${waveSize * 2}${sizeUnit} ${
                                waveSize * 2
                            }${sizeUnit}`;
                        }
                    } else {
                        // Non-alternating pattern for multiple sides
                        if (borderSides === "top-bottom") {
                            maskValue = `
                                repeating-radial-gradient(
                                    ellipse at top,
                                    #0000 0,
                                    #0000 ${waveSize / 2}${sizeUnit},
                                    #000 ${waveSize / 2}${sizeUnit},
                                    #000 ${waveSize}${sizeUnit},
                                    #0000 ${waveSize}${sizeUnit},
                                    #0000 ${waveSize * 1.5}${sizeUnit}
                                ) 0 0/${waveSize * 2}${sizeUnit} ${
                                waveSize * 2
                            }${sizeUnit}`;
                        } else {
                            maskValue = `
                                repeating-radial-gradient(
                                    ellipse at left,
                                    #0000 0,
                                    #0000 ${waveSize / 2}${sizeUnit},
                                    #000 ${waveSize / 2}${sizeUnit},
                                    #000 ${waveSize}${sizeUnit},
                                    #0000 ${waveSize}${sizeUnit},
                                    #0000 ${waveSize * 1.5}${sizeUnit}
                                ) 0 0/${waveSize * 2}${sizeUnit} ${
                                waveSize * 2
                            }${sizeUnit}`;
                        }
                    }
                } else {
                    // Single side wavy pattern
                    if (borderSides === "top") {
                        maskValue = `
                            repeating-radial-gradient(
                                ellipse at top,
                                #0000 0,
                                #0000 ${waveSize / 2}${sizeUnit},
                                #000 ${waveSize / 2}${sizeUnit},
                                #000 ${waveSize}${sizeUnit},
                                #0000 ${waveSize}${sizeUnit},
                                #0000 ${waveSize * 1.5}${sizeUnit}
                            ) 0 0/${waveSize * 2}${sizeUnit} ${
                            waveSize * 2
                        }${sizeUnit}`;
                    } else if (borderSides === "bottom") {
                        maskValue = `
                            repeating-radial-gradient(
                                ellipse at bottom,
                                #0000 0,
                                #0000 ${waveSize / 2}${sizeUnit},
                                #000 ${waveSize / 2}${sizeUnit},
                                #000 ${waveSize}${sizeUnit},
                                #0000 ${waveSize}${sizeUnit},
                                #0000 ${waveSize * 1.5}${sizeUnit}
                            ) 0 0/${waveSize * 2}${sizeUnit} ${
                            waveSize * 2
                        }${sizeUnit}`;
                    } else if (borderSides === "left") {
                        maskValue = `
                            repeating-radial-gradient(
                                ellipse at left,
                                #0000 0,
                                #0000 ${waveSize / 2}${sizeUnit},
                                #000 ${waveSize / 2}${sizeUnit},
                                #000 ${waveSize}${sizeUnit},
                                #0000 ${waveSize}${sizeUnit},
                                #0000 ${waveSize * 1.5}${sizeUnit}
                            ) 0 0/${waveSize * 2}${sizeUnit} ${
                            waveSize * 2
                        }${sizeUnit}`;
                    } else if (borderSides === "right") {
                        maskValue = `
                            repeating-radial-gradient(
                                ellipse at right,
                                #0000 0,
                                #0000 ${waveSize / 2}${sizeUnit},
                                #000 ${waveSize / 2}${sizeUnit},
                                #000 ${waveSize}${sizeUnit},
                                #0000 ${waveSize}${sizeUnit},
                                #0000 ${waveSize * 1.5}${sizeUnit}
                            ) 0 0/${waveSize * 2}${sizeUnit} ${
                            waveSize * 2
                        }${sizeUnit}`;
                    }
                }
                break;
            default:
                maskValue = `conic-gradient(from ${angle}deg at ${getSidePosition()}, #0000, #000 1deg 89deg, #0000 90deg)`;
        }

        // Add size and position based on sides
        const sizeAndPosition = getSizeAndPosition();

        return `${maskValue} ${sizeAndPosition}`;
    };

    // Get the position for the mask based on selected sides
    const getSidePosition = () => {
        switch (borderSides) {
            case "bottom":
                return "bottom";
            case "top":
                return "top";
            case "left":
                return "left";
            case "right":
                return "right";
            case "top-bottom":
                return alternating ? "top, bottom" : "top";
            case "left-right":
                return alternating ? "left, right" : "left";
            default:
                return "bottom";
        }
    };

    // Get size and position for the mask
    const getSizeAndPosition = () => {
        switch (borderSides) {
            case "bottom":
                return `50%/${borderSize}${sizeUnit} 100%`;
            case "top":
                return `50%/${borderSize}${sizeUnit} 100%`;
            case "left":
                return `0% 50%/100% ${borderSize}${sizeUnit}`;
            case "right":
                return `100% 50%/100% ${borderSize}${sizeUnit}`;
            case "top-bottom":
                if (alternating) {
                    return `50% 0%/${borderSize}${sizeUnit} 51%, 50% 100%/${borderSize}${sizeUnit} 51%`;
                }
                return `50%/${borderSize}${sizeUnit} 100%`;
            case "left-right":
                if (alternating) {
                    return `0% 50%/51% ${borderSize}${sizeUnit}, 100% 50%/51% ${borderSize}${sizeUnit}`;
                }
                return `0% 50%/100% ${borderSize}${sizeUnit}`;
            default:
                return `50%/${borderSize}${sizeUnit} 100%`;
        }
    };

    // Generate the complete CSS code
    const generateCSS = () => {
        const mask = generateMask();

        // Create a more comprehensive CSS example
        let cssOutput = `/* ${borderType.toUpperCase()} BORDER ON ${borderSides.toUpperCase()} SIDE */
/* Size: ${borderSize}${sizeUnit}, Angle: ${angle}° */

/* Option 1: Using mask property (modern browsers) */
.element-with-${borderType}-border {
  position: relative;
  /* Add some padding to make room for the border */
  ${
      borderSides.includes("top")
          ? `padding-top: ${borderSize + 5}${sizeUnit};`
          : ""
  }
  ${
      borderSides.includes("bottom")
          ? `padding-bottom: ${borderSize + 5}${sizeUnit};`
          : ""
  }
  ${
      borderSides.includes("left")
          ? `padding-left: ${borderSize + 5}${sizeUnit};`
          : ""
  }
  ${
      borderSides.includes("right")
          ? `padding-right: ${borderSize + 5}${sizeUnit};`
          : ""
  }
}

.element-with-${borderType}-border::before {
  content: "";
  position: absolute;
  ${borderSides.includes("top") ? "top: 0;" : ""}
  ${borderSides.includes("bottom") ? "bottom: 0;" : ""}
  ${borderSides.includes("left") ? "left: 0;" : ""}
  ${borderSides.includes("right") ? "right: 0;" : ""}
  ${!borderSides.includes("-") ? "width: 100%; height: 100%;" : ""}
  background-color: #4F46E5; /* Border color */
  mask: ${mask};
  -webkit-mask: ${mask};
  z-index: 1;
}

/* Option 2: Using clip-path (better browser support) */
.element-with-${borderType}-border-clip {
  position: relative;
  /* Same padding as above */
  ${
      borderSides.includes("top")
          ? `padding-top: ${borderSize + 5}${sizeUnit};`
          : ""
  }
  ${
      borderSides.includes("bottom")
          ? `padding-bottom: ${borderSize + 5}${sizeUnit};`
          : ""
  }
  ${
      borderSides.includes("left")
          ? `padding-left: ${borderSize + 5}${sizeUnit};`
          : ""
  }
  ${
      borderSides.includes("right")
          ? `padding-right: ${borderSize + 5}${sizeUnit};`
          : ""
  }
}

/* For clip-path implementation, you would need to create specific paths for each border type */`;

        return cssOutput;
    };

    // Get preview style with the current border settings
    const getPreviewStyle = () => {
        return {
            mask: generateMask(),
            WebkitMask: generateMask(),
            backgroundColor: "#4F46E5", // Primary color
            width: "100%",
            height: "200px",
        };
    };

    // Copy CSS to clipboard
    const copyToClipboard = () => {
        navigator.clipboard.writeText(generateCSS()).then(() => {
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        });
    };

    // Sample presets for different border types
    const PRESETS = [
        {
            name: "Zigzag Bottom",
            type: "zigzag",
            sides: "bottom",
            size: 15,
            unit: "px",
            angle: 90,
            alternating: false,
        },
        {
            name: "Scalloped Top",
            type: "scalloped",
            sides: "top",
            size: 20,
            unit: "px",
            angle: 0,
            alternating: false,
        },
        {
            name: "Wavy Sides",
            type: "wavy",
            sides: "left-right",
            size: 12,
            unit: "px",
            angle: 45,
            alternating: true,
        },
        {
            name: "Scooped Edges",
            type: "scooped",
            sides: "top-bottom",
            size: 18,
            unit: "px",
            angle: 0,
            alternating: true,
        },
    ];

    // Load a preset
    const loadPreset = (preset) => {
        setBorderType(preset.type);
        setBorderSides(preset.sides);
        setBorderSize(preset.size);
        setSizeUnit(preset.unit);
        setAngle(preset.angle);
        setAlternating(preset.alternating);
    };

    return (
        <ToolContainer
            className="py-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-colors duration-200"
            containerWidth={toolData?.containerWidth || "default"}>
            <ToolHeader
                toolData={toolData}
                defaultTitle="Border Generator"
                defaultIcon="Square"
                defaultDescription="Create custom CSS borders with different shapes like zig-zag, scooped, scalloped, or wavy. Customize the size, position, and get the CSS code."
            />

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Controls Panel */}
                <div className="space-y-6">
                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                            Border Type
                        </h2>
                        <div className="grid grid-cols-2 gap-3">
                            {BORDER_TYPES.map((type) => (
                                <button
                                    key={type.id}
                                    className={`p-3 rounded-lg border ${
                                        borderType === type.id
                                            ? "border-primary dark:border-primary-dark bg-primary/10 dark:bg-primary-dark/10"
                                            : "border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600"
                                    }`}
                                    onClick={() => setBorderType(type.id)}>
                                    <span className="text-sm font-medium">
                                        {type.name}
                                    </span>
                                </button>
                            ))}
                        </div>
                    </div>

                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                            Border Side
                        </h2>
                        <div className="grid grid-cols-2 gap-3">
                            {BORDER_SIDES.map((side) => (
                                <button
                                    key={side.id}
                                    className={`p-3 rounded-lg border ${
                                        borderSides === side.id
                                            ? "border-primary dark:border-primary-dark bg-primary/10 dark:bg-primary-dark/10"
                                            : "border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600"
                                    }`}
                                    onClick={() => setBorderSides(side.id)}>
                                    <span className="text-sm font-medium">
                                        {side.name}
                                    </span>
                                </button>
                            ))}
                        </div>
                    </div>

                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                            Size & Angle
                        </h2>
                        <div className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Size
                                </label>
                                <div className="flex">
                                    <input
                                        type="number"
                                        min="1"
                                        max="100"
                                        value={borderSize}
                                        onChange={(e) =>
                                            setBorderSize(
                                                parseInt(e.target.value) || 1
                                            )
                                        }
                                        className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-l bg-white dark:bg-gray-800"
                                    />
                                    <select
                                        value={sizeUnit}
                                        onChange={(e) =>
                                            setSizeUnit(e.target.value)
                                        }
                                        className="p-2 border border-gray-300 dark:border-gray-600 border-l-0 rounded-r bg-white dark:bg-gray-800">
                                        <option value="px">px</option>
                                        <option value="em">em</option>
                                        <option value="rem">rem</option>
                                    </select>
                                </div>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Angle ({angle}°)
                                </label>
                                <input
                                    type="range"
                                    min="0"
                                    max="360"
                                    step="5"
                                    value={angle}
                                    onChange={(e) =>
                                        setAngle(parseInt(e.target.value))
                                    }
                                    className="w-full"
                                />
                            </div>

                            <div className="flex items-center">
                                <input
                                    type="checkbox"
                                    id="alternating"
                                    checked={alternating}
                                    onChange={(e) =>
                                        setAlternating(e.target.checked)
                                    }
                                    className="mr-2 h-4 w-4"
                                />
                                <label
                                    htmlFor="alternating"
                                    className="text-sm text-gray-700 dark:text-gray-300">
                                    With Alternation (for multiple sides)
                                </label>
                            </div>
                        </div>
                    </div>

                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                            Presets
                        </h2>
                        <div className="grid grid-cols-2 gap-2">
                            {PRESETS.map((preset, index) => (
                                <button
                                    key={index}
                                    className="p-2 text-left bg-white dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-600 rounded border border-gray-300 dark:border-gray-600 text-sm"
                                    onClick={() => loadPreset(preset)}>
                                    {preset.name}
                                </button>
                            ))}
                        </div>
                    </div>
                </div>

                {/* Preview and Code Panel */}
                <div className="space-y-6">
                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                            Preview
                        </h2>
                        <div
                            style={getPreviewStyle()}
                            className="border border-gray-300 dark:border-gray-600 rounded-lg"></div>
                    </div>

                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <div className="flex justify-between items-center mb-4">
                            <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
                                CSS Code
                            </h2>
                            <button
                                className="px-3 py-1 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors flex items-center"
                                onClick={copyToClipboard}>
                                <LucideIcon
                                    name={copied ? "Check" : "Copy"}
                                    size={16}
                                    className="mr-1"
                                />
                                {copied ? "Copied!" : "Copy Code"}
                            </button>
                        </div>
                        <pre className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-300 dark:border-gray-600 overflow-x-auto text-sm">
                            {generateCSS()}
                        </pre>
                    </div>
                </div>
            </div>
        </ToolContainer>
    );
}
