import { useState, useEffect } from "react";
import { getToolById } from "../../data/tools.js";
import ToolHeader from "../ui/ToolHeader.jsx";
import ToolContainer from "../ui/ToolContainer.jsx";
import LucideIcon from "../ui/LucideIcon.jsx";

// Check if we're in the browser environment
const isBrowser = typeof window !== "undefined";

export default function MetaTagPreview() {
    const [toolData, setToolData] = useState(null);
    const [url, setUrl] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [metaData, setMetaData] = useState(null);
    const [activeTab, setActiveTab] = useState("preview");
    const [copiedField, setCopiedField] = useState(null);

    // Sample URL for testing
    const sampleUrl =
        "https://github.blog/2023-11-08-universe-2023-ai-for-developers-and-organizations/";

    useEffect(() => {
        // Get the tool data
        const data = getToolById("metatagpreview");
        setToolData(data);
    }, []);

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!url) {
            setError("Please enter a URL");
            return;
        }

        // Add protocol if missing
        let processedUrl = url;
        if (!/^https?:\/\//i.test(processedUrl)) {
            processedUrl = "https://" + processedUrl;
            setUrl(processedUrl);
        }

        try {
            setIsLoading(true);
            setError(null);
            setMetaData(null);

            console.log("Fetching meta tags for URL:", processedUrl);

            // Try multiple CORS proxies in case one fails
            const corsProxies = [
                "https://api.allorigins.win/raw?url=",
                "https://corsproxy.io/?",
                "https://cors-anywhere.herokuapp.com/",
            ];

            let response = null;
            let proxyError = null;

            // Try each proxy until one works
            for (const proxy of corsProxies) {
                try {
                    console.log(`Trying proxy: ${proxy}`);
                    const targetUrl = encodeURIComponent(processedUrl);
                    response = await fetch(proxy + targetUrl);

                    if (response.ok) {
                        console.log(`Proxy ${proxy} succeeded`);
                        break;
                    }
                } catch (err) {
                    console.error(`Proxy ${proxy} failed:`, err);
                    proxyError = err;
                }
            }

            // If all proxies failed, throw an error
            if (!response || !response.ok) {
                throw new Error(
                    "Failed to fetch meta tags. All CORS proxies failed. Please try again later."
                );
            }

            console.log("Response status:", response.status);

            // Get the HTML content
            const html = await response.text();
            console.log("Received HTML content");

            // Create a DOM parser
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, "text/html");

            // Extract meta tags
            const metaTags = {};
            const allMetaTags = doc.querySelectorAll("meta");
            allMetaTags.forEach((tag) => {
                const name =
                    tag.getAttribute("name") || tag.getAttribute("property");
                const content = tag.getAttribute("content");
                if (name && content) {
                    metaTags[name] = content;
                }
            });

            // Extract title
            const titleTag = doc.querySelector("title");
            const title = titleTag ? titleTag.textContent : "";

            // Extract domain and base URL for resolving relative URLs
            let domain;
            let baseUrl;
            try {
                const urlObj = new URL(processedUrl);
                domain = urlObj.hostname;
                baseUrl = `${urlObj.protocol}//${urlObj.hostname}`;
            } catch (e) {
                domain = processedUrl;
                baseUrl = `https://${processedUrl}`;
            }

            // Function to resolve relative URLs
            const resolveUrl = (url) => {
                if (!url) return url;
                if (url.startsWith("http://") || url.startsWith("https://")) {
                    return url;
                } else if (url.startsWith("//")) {
                    return `https:${url}`;
                } else if (url.startsWith("/")) {
                    return `${baseUrl}${url}`;
                } else {
                    return `${baseUrl}/${url}`;
                }
            };

            // Extract Open Graph image
            let ogImage;
            const ogImageTag = doc.querySelector('meta[property="og:image"]');
            if (ogImageTag) {
                ogImage = resolveUrl(ogImageTag.getAttribute("content"));
            }

            // Extract Twitter image
            let twitterImage;
            const twitterImageTag = doc.querySelector(
                'meta[name="twitter:image"]'
            );
            if (twitterImageTag) {
                twitterImage = resolveUrl(
                    twitterImageTag.getAttribute("content")
                );
            }

            // We already have domain and baseUrl from above

            console.log("Extracted meta tags:", metaTags);

            // Transform the extracted data to match our expected format
            const transformedData = {
                ogTitle: metaTags["og:title"] || title || "",
                ogDescription:
                    metaTags["og:description"] || metaTags["description"] || "",
                ogImage: ogImage ? [{ url: ogImage }] : undefined,
                ogUrl: metaTags["og:url"] || processedUrl,
                ogSiteName: metaTags["og:site_name"] || domain,
                title: title || "",
                description: metaTags["description"] || "",
                twitterTitle:
                    metaTags["twitter:title"] ||
                    metaTags["og:title"] ||
                    title ||
                    "",
                twitterDescription:
                    metaTags["twitter:description"] ||
                    metaTags["og:description"] ||
                    metaTags["description"] ||
                    "",
                twitterImage: twitterImage
                    ? [{ url: twitterImage }]
                    : ogImage
                    ? [{ url: ogImage }]
                    : undefined,
                twitterCard: metaTags["twitter:card"] || "summary_large_image",
                requestUrl: processedUrl,
                charset: "UTF-8",
            };

            setMetaData(transformedData);
        } catch (err) {
            console.error("Error in handleSubmit:", err);
            setError(err.message || "Failed to fetch meta tags");
        } finally {
            setIsLoading(false);
        }
    };

    const handleCopy = (field, value) => {
        if (!isBrowser) return;

        navigator.clipboard.writeText(value).then(() => {
            setCopiedField(field);
            setTimeout(() => setCopiedField(null), 2000);
        });
    };

    const renderPreview = () => {
        if (!metaData) return null;

        const title =
            metaData.ogTitle || metaData.twitterTitle || metaData.title || url;
        const description =
            metaData.ogDescription ||
            metaData.twitterDescription ||
            metaData.description ||
            "";
        const image =
            metaData.ogImage?.[0]?.url || metaData.twitterImage?.[0]?.url || "";

        // Safely extract hostname from URL
        let siteName = metaData.ogSiteName;
        if (!siteName) {
            try {
                siteName = new URL(metaData.requestUrl || url).hostname;
            } catch (e) {
                // If URL parsing fails, use a fallback
                siteName = metaData.requestUrl || url;
            }
        }

        return (
            <div className="space-y-8">
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
                    <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                        Facebook / LinkedIn Preview
                    </h3>
                    <div className="border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
                        <div className="bg-[#f2f3f5] dark:bg-gray-700 p-3">
                            {image && (
                                <div className="aspect-[1.91/1] bg-gray-200 dark:bg-gray-600 rounded-lg overflow-hidden mb-2">
                                    <img
                                        src={image}
                                        alt={title}
                                        className="w-full h-full object-cover"
                                        onError={(e) => {
                                            e.target.onerror = null;
                                            e.target.src =
                                                "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Crect width='100' height='100' fill='%23cccccc'/%3E%3Cpath d='M30,50 L70,50 M50,30 L50,70' stroke='%23999999' stroke-width='4'/%3E%3C/svg%3E";
                                        }}
                                    />
                                </div>
                            )}
                            <div className="bg-white dark:bg-gray-800 rounded-lg p-3">
                                <div className="text-[#385898] dark:text-blue-400 text-xs uppercase font-semibold mb-1">
                                    {siteName}
                                </div>
                                <h4 className="font-bold text-gray-900 dark:text-white text-base mb-1 line-clamp-2">
                                    {title}
                                </h4>
                                <p className="text-gray-500 dark:text-gray-400 text-sm line-clamp-2">
                                    {description}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
                    <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                        Twitter Preview
                    </h3>
                    <div className="border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
                        <div className="bg-white dark:bg-gray-700 p-3">
                            {image && (
                                <div className="aspect-[2/1] bg-gray-200 dark:bg-gray-600 rounded-lg overflow-hidden mb-2">
                                    <img
                                        src={image}
                                        alt={title}
                                        className="w-full h-full object-cover"
                                        onError={(e) => {
                                            e.target.onerror = null;
                                            e.target.src =
                                                "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Crect width='100' height='100' fill='%23cccccc'/%3E%3Cpath d='M30,50 L70,50 M50,30 L50,70' stroke='%23999999' stroke-width='4'/%3E%3C/svg%3E";
                                        }}
                                    />
                                </div>
                            )}
                            <div className="p-3">
                                <h4 className="font-bold text-gray-900 dark:text-white text-base mb-1 line-clamp-2">
                                    {title}
                                </h4>
                                <p className="text-gray-500 dark:text-gray-400 text-sm mb-1 line-clamp-3">
                                    {description}
                                </p>
                                <div className="text-gray-500 dark:text-gray-400 text-xs">
                                    {siteName}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    };

    const renderMetaTags = () => {
        if (!metaData) return null;

        const metaTagGroups = [
            {
                title: "Basic Meta Tags",
                tags: [
                    { name: "Title", value: metaData.title },
                    { name: "Description", value: metaData.description },
                    { name: "Charset", value: metaData.charset },
                    { name: "URL", value: metaData.requestUrl || url },
                ],
            },
            {
                title: "Open Graph Tags",
                tags: [
                    { name: "og:title", value: metaData.ogTitle },
                    { name: "og:description", value: metaData.ogDescription },
                    { name: "og:url", value: metaData.ogUrl },
                    { name: "og:site_name", value: metaData.ogSiteName },
                    { name: "og:type", value: metaData.ogType },
                    { name: "og:image", value: metaData.ogImage?.[0]?.url },
                    {
                        name: "og:image:width",
                        value: metaData.ogImage?.[0]?.width,
                    },
                    {
                        name: "og:image:height",
                        value: metaData.ogImage?.[0]?.height,
                    },
                    {
                        name: "og:image:type",
                        value: metaData.ogImage?.[0]?.type,
                    },
                ],
            },
            {
                title: "Twitter Card Tags",
                tags: [
                    { name: "twitter:card", value: metaData.twitterCard },
                    { name: "twitter:title", value: metaData.twitterTitle },
                    {
                        name: "twitter:description",
                        value: metaData.twitterDescription,
                    },
                    {
                        name: "twitter:image",
                        value: metaData.twitterImage?.[0]?.url,
                    },
                    { name: "twitter:site", value: metaData.twitterSite },
                    { name: "twitter:creator", value: metaData.twitterCreator },
                ],
            },
        ];

        return (
            <div className="space-y-8">
                {metaTagGroups.map((group, groupIndex) => (
                    <div
                        key={groupIndex}
                        className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
                        <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                            {group.title}
                        </h3>
                        <div className="overflow-x-auto">
                            <table className="w-full text-left">
                                <thead>
                                    <tr className="border-b border-gray-200 dark:border-gray-700">
                                        <th className="py-3 px-4 text-gray-600 dark:text-gray-400 font-medium">
                                            Tag
                                        </th>
                                        <th className="py-3 px-4 text-gray-600 dark:text-gray-400 font-medium">
                                            Value
                                        </th>
                                        <th className="py-3 px-4 text-gray-600 dark:text-gray-400 font-medium w-24">
                                            Action
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {group.tags.map((tag, tagIndex) =>
                                        tag.value ? (
                                            <tr
                                                key={tagIndex}
                                                className="border-b border-gray-200 dark:border-gray-700">
                                                <td className="py-3 px-4 text-gray-800 dark:text-gray-200 font-mono text-sm">
                                                    {tag.name}
                                                </td>
                                                <td className="py-3 px-4 text-gray-800 dark:text-gray-200 font-mono text-sm break-all">
                                                    {tag.value.length > 100
                                                        ? `${tag.value.substring(
                                                              0,
                                                              100
                                                          )}...`
                                                        : tag.value}
                                                </td>
                                                <td className="py-3 px-4">
                                                    <button
                                                        onClick={() =>
                                                            handleCopy(
                                                                `${group.title}-${tag.name}`,
                                                                tag.value
                                                            )
                                                        }
                                                        className="text-primary dark:text-primary-dark hover:text-primary/80 dark:hover:text-primary-dark/80 transition-colors"
                                                        title="Copy to clipboard">
                                                        {copiedField ===
                                                        `${group.title}-${tag.name}` ? (
                                                            <LucideIcon
                                                                name="Check"
                                                                size={18}
                                                            />
                                                        ) : (
                                                            <LucideIcon
                                                                name="Copy"
                                                                size={18}
                                                            />
                                                        )}
                                                    </button>
                                                </td>
                                            </tr>
                                        ) : null
                                    )}
                                </tbody>
                            </table>
                        </div>
                    </div>
                ))}
            </div>
        );
    };

    return (
        <ToolContainer
            className="py-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-colors duration-200"
            containerWidth={toolData?.containerWidth || "default"}>
            <ToolHeader
                toolData={toolData}
                defaultTitle="Meta Tag Preview"
                defaultIcon="Share2"
                defaultDescription="See how your website appears when shared on social media platforms like Facebook, Twitter, and LinkedIn. This tool fetches and displays meta tags, Open Graph tags, Twitter Card tags, and other relevant metadata from any URL."
            />

            <form onSubmit={handleSubmit} className="mb-8">
                <div className="flex flex-col md:flex-row gap-4">
                    <div className="flex-grow">
                        <div className="relative">
                            <label htmlFor="url" className="sr-only">
                                URL
                            </label>
                            <input
                                id="url"
                                type="url"
                                value={url}
                                onChange={(e) => setUrl(e.target.value)}
                                placeholder="Enter a URL (e.g., https://example.com)"
                                className="w-full p-4 border border-gray-300 dark:border-gray-600 rounded-xl text-lg font-mono bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark transition-all"
                                required
                            />
                            <button
                                type="button"
                                onClick={() => setUrl(sampleUrl)}
                                className="absolute right-3 top-1/2 -translate-y-1/2 text-primary dark:text-primary-dark hover:text-primary/80 dark:hover:text-primary-dark/80 text-sm font-medium">
                                Load Sample
                            </button>
                        </div>
                    </div>
                    <button
                        type="submit"
                        disabled={isLoading}
                        className="bg-primary dark:bg-primary-dark text-white px-6 py-4 rounded-xl hover:bg-primary/90 dark:hover:bg-primary-dark/90 focus:outline-none focus:ring-4 focus:ring-primary/50 dark:focus:ring-primary-dark/50 transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center min-w-[120px]">
                        {isLoading ? (
                            <div className="animate-spin w-5 h-5 border-2 border-white border-t-transparent rounded-full"></div>
                        ) : (
                            "Preview"
                        )}
                    </button>
                </div>
            </form>

            {error && (
                <div className="bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 p-4 rounded-lg mb-8">
                    <div className="flex items-center">
                        <LucideIcon
                            name="AlertTriangle"
                            size={20}
                            className="mr-2 flex-shrink-0"
                        />
                        <p>{error}</p>
                    </div>
                </div>
            )}

            {metaData && (
                <div>
                    <div className="bg-blue-50 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 p-4 rounded-lg mb-8">
                        <div className="flex items-center">
                            <LucideIcon
                                name="Info"
                                size={20}
                                className="mr-2 flex-shrink-0"
                            />
                            <p>
                                This is a <strong>simulation</strong> of how
                                your URL might appear when shared on social
                                media. For demonstration purposes, we're
                                generating placeholder content based on the
                                domain name.
                            </p>
                        </div>
                    </div>

                    <div className="border-b border-gray-200 dark:border-gray-700 mb-8">
                        <nav className="flex space-x-8">
                            <button
                                onClick={() => setActiveTab("preview")}
                                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                                    activeTab === "preview"
                                        ? "border-primary dark:border-primary-dark text-primary dark:text-primary-dark"
                                        : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"
                                }`}>
                                Preview
                            </button>
                            <button
                                onClick={() => setActiveTab("metatags")}
                                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                                    activeTab === "metatags"
                                        ? "border-primary dark:border-primary-dark text-primary dark:text-primary-dark"
                                        : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"
                                }`}>
                                Meta Tags
                            </button>
                        </nav>
                    </div>

                    <div>
                        {activeTab === "preview"
                            ? renderPreview()
                            : renderMetaTags()}
                    </div>
                </div>
            )}
        </ToolContainer>
    );
}
