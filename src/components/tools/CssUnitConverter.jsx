import React, { useState, useEffect } from "react";
import { getToolById } from "../../data/tools.js";
import ToolContainer from "../ui/ToolContainer.jsx";
import ToolHeader from "../ui/ToolHeader.jsx";

export default function CssUnitConverter() {
    const [toolData, setToolData] = useState(null);
    const [baseFontSize, setBaseFontSize] = useState(16);
    const [pixelInput, setPixelInput] = useState("");
    const [batchInput, setBatchInput] = useState("");
    const [singleResult, setSingleResult] = useState(null);
    const [batchResults, setBatchResults] = useState([]);
    const [copied, setCopied] = useState("");
    const [activeTab, setActiveTab] = useState("single");

    useEffect(() => {
        const data = getToolById("cssunitconverter");
        setToolData(data);
    }, []);

    // Convert pixels to rem and em
    const convertUnits = (pixels, baseSize = baseFontSize) => {
        const px = parseFloat(pixels);
        if (isNaN(px)) return null;

        return {
            px: px,
            rem: Math.round((px / baseSize) * 1000) / 1000,
            em: Math.round((px / baseSize) * 1000) / 1000,
        };
    };

    // Handle single conversion
    const handleSingleConversion = () => {
        const result = convertUnits(pixelInput);
        setSingleResult(result);
    };

    // Handle batch conversion
    const handleBatchConversion = () => {
        const lines = batchInput.split('\n').filter(line => line.trim());
        const results = [];

        lines.forEach((line, index) => {
            // Extract numbers from the line (supports formats like "16px", "24", "margin: 16px", etc.)
            const matches = line.match(/(\d+(?:\.\d+)?)/g);
            if (matches) {
                matches.forEach(match => {
                    const result = convertUnits(match);
                    if (result) {
                        results.push({
                            id: `${index}-${match}`,
                            original: line.trim(),
                            ...result
                        });
                    }
                });
            }
        });

        setBatchResults(results);
    };

    // Copy to clipboard
    const copyToClipboard = (text, id = "") => {
        navigator.clipboard.writeText(text).then(() => {
            setCopied(id);
            setTimeout(() => setCopied(""), 2000);
        });
    };

    // Load sample data
    const loadSample = () => {
        if (activeTab === "single") {
            setPixelInput("24");
            setTimeout(() => handleSingleConversion(), 100);
        } else {
            setBatchInput(`font-size: 16px
margin: 24px
padding: 12px
width: 320px
height: 48px
border-radius: 8px`);
            setTimeout(() => handleBatchConversion(), 100);
        }
    };

    // Common conversion table
    const commonConversions = [
        { px: 8, label: "Small spacing" },
        { px: 12, label: "Text small" },
        { px: 14, label: "Text body" },
        { px: 16, label: "Text base" },
        { px: 18, label: "Text large" },
        { px: 24, label: "Heading small" },
        { px: 32, label: "Heading medium" },
        { px: 48, label: "Heading large" },
    ];

    return (
        <ToolContainer
            className="py-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-colors duration-200"
            containerWidth={toolData?.containerWidth || "default"}
        >
            <ToolHeader
                toolData={toolData}
                defaultTitle="CSS Unit Converter"
                defaultIcon="Ruler"
                defaultDescription="Convert pixels to rem/em units for responsive CSS. Perfect for converting Figma designs to scalable web layouts."
            />

            {/* Base Font Size Configuration */}
            <div className="mb-8 p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
                <label className="block text-sm font-semibold text-gray-800 dark:text-gray-200 mb-2">
                    Base Font Size (px)
                </label>
                <div className="flex items-center space-x-4">
                    <input
                        type="number"
                        value={baseFontSize}
                        onChange={(e) => setBaseFontSize(parseFloat(e.target.value) || 16)}
                        className="w-24 p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                        min="1"
                        max="32"
                    />
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                        This is your root font size (typically 16px)
                    </span>
                </div>
            </div>

            {/* Tab Navigation */}
            <div className="flex mb-6 border-b border-gray-200 dark:border-gray-600">
                <button
                    onClick={() => setActiveTab("single")}
                    className={`px-4 py-2 font-medium text-sm border-b-2 transition-colors ${
                        activeTab === "single"
                            ? "border-primary dark:border-primary-dark text-primary dark:text-primary-dark"
                            : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                    }`}
                >
                    Single Conversion
                </button>
                <button
                    onClick={() => setActiveTab("batch")}
                    className={`px-4 py-2 font-medium text-sm border-b-2 transition-colors ${
                        activeTab === "batch"
                            ? "border-primary dark:border-primary-dark text-primary dark:text-primary-dark"
                            : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                    }`}
                >
                    Batch Conversion
                </button>
                <button
                    onClick={() => setActiveTab("table")}
                    className={`px-4 py-2 font-medium text-sm border-b-2 transition-colors ${
                        activeTab === "table"
                            ? "border-primary dark:border-primary-dark text-primary dark:text-primary-dark"
                            : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                    }`}
                >
                    Conversion Table
                </button>
            </div>

            {/* Load Sample Button */}
            <div className="mb-6">
                <button
                    onClick={loadSample}
                    className="px-4 py-2 bg-gray-100 dark:bg-gray-700 rounded-md text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                    Load Sample
                </button>
            </div>

            {/* Single Conversion Tab */}
            {activeTab === "single" && (
                <div className="space-y-6">
                    <div>
                        <label className="block text-sm font-semibold text-gray-800 dark:text-gray-200 mb-2">
                            Pixel Value
                        </label>
                        <div className="flex space-x-4">
                            <input
                                type="number"
                                value={pixelInput}
                                onChange={(e) => setPixelInput(e.target.value)}
                                placeholder="Enter pixel value (e.g., 24)"
                                className="flex-1 p-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                            />
                            <button
                                onClick={handleSingleConversion}
                                className="px-6 py-3 bg-primary dark:bg-primary-dark text-white rounded-xl hover:bg-primary/90 dark:hover:bg-primary-dark/90 focus:outline-none focus:ring-4 focus:ring-primary/50 dark:focus:ring-primary-dark/50 transition-all"
                            >
                                Convert
                            </button>
                        </div>
                    </div>

                    {singleResult && (
                        <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
                            <h3 className="font-semibold text-gray-800 dark:text-gray-200 mb-3">
                                Conversion Result
                            </h3>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div className="flex justify-between items-center p-3 bg-white dark:bg-gray-800 rounded-lg">
                                    <span className="font-mono text-lg">{singleResult.px}px</span>
                                    <button
                                        onClick={() => copyToClipboard(`${singleResult.px}px`, "px")}
                                        className="text-xs text-primary dark:text-primary-dark hover:underline"
                                    >
                                        {copied === "px" ? "Copied!" : "Copy"}
                                    </button>
                                </div>
                                <div className="flex justify-between items-center p-3 bg-white dark:bg-gray-800 rounded-lg">
                                    <span className="font-mono text-lg">{singleResult.rem}rem</span>
                                    <button
                                        onClick={() => copyToClipboard(`${singleResult.rem}rem`, "rem")}
                                        className="text-xs text-primary dark:text-primary-dark hover:underline"
                                    >
                                        {copied === "rem" ? "Copied!" : "Copy"}
                                    </button>
                                </div>
                                <div className="flex justify-between items-center p-3 bg-white dark:bg-gray-800 rounded-lg">
                                    <span className="font-mono text-lg">{singleResult.em}em</span>
                                    <button
                                        onClick={() => copyToClipboard(`${singleResult.em}em`, "em")}
                                        className="text-xs text-primary dark:text-primary-dark hover:underline"
                                    >
                                        {copied === "em" ? "Copied!" : "Copy"}
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            )}

            {/* Batch Conversion Tab */}
            {activeTab === "batch" && (
                <div className="space-y-6">
                    <div>
                        <label className="block text-sm font-semibold text-gray-800 dark:text-gray-200 mb-2">
                            CSS Properties (one per line)
                        </label>
                        <textarea
                            value={batchInput}
                            onChange={(e) => setBatchInput(e.target.value)}
                            placeholder="Paste your CSS or enter values like:&#10;font-size: 16px&#10;margin: 24px&#10;padding: 12px"
                            rows={8}
                            className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark font-mono text-sm"
                        />
                        <button
                            onClick={handleBatchConversion}
                            className="mt-3 px-6 py-3 bg-primary dark:bg-primary-dark text-white rounded-xl hover:bg-primary/90 dark:hover:bg-primary-dark/90 focus:outline-none focus:ring-4 focus:ring-primary/50 dark:focus:ring-primary-dark/50 transition-all"
                        >
                            Convert All
                        </button>
                    </div>

                    {batchResults.length > 0 && (
                        <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
                            <h3 className="font-semibold text-gray-800 dark:text-gray-200 mb-3">
                                Batch Results ({batchResults.length} conversions)
                            </h3>
                            <div className="space-y-2 max-h-64 overflow-y-auto">
                                {batchResults.map((result) => (
                                    <div key={result.id} className="flex justify-between items-center p-2 bg-white dark:bg-gray-800 rounded text-sm">
                                        <span className="font-mono text-gray-600 dark:text-gray-400 truncate mr-4">
                                            {result.original}
                                        </span>
                                        <div className="flex space-x-2 flex-shrink-0">
                                            <span className="font-mono">{result.px}px</span>
                                            <span className="text-gray-400">→</span>
                                            <button
                                                onClick={() => copyToClipboard(`${result.rem}rem`, `batch-rem-${result.id}`)}
                                                className="font-mono text-primary dark:text-primary-dark hover:underline"
                                            >
                                                {result.rem}rem
                                            </button>
                                            <span className="text-gray-400">/</span>
                                            <button
                                                onClick={() => copyToClipboard(`${result.em}em`, `batch-em-${result.id}`)}
                                                className="font-mono text-primary dark:text-primary-dark hover:underline"
                                            >
                                                {result.em}em
                                            </button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            )}

            {/* Conversion Table Tab */}
            {activeTab === "table" && (
                <div className="space-y-6">
                    <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
                        <h3 className="font-semibold text-gray-800 dark:text-gray-200 mb-4">
                            Common Conversions (Base: {baseFontSize}px)
                        </h3>
                        <div className="overflow-x-auto">
                            <table className="w-full text-sm">
                                <thead>
                                    <tr className="border-b border-gray-200 dark:border-gray-600">
                                        <th className="text-left py-2 text-gray-600 dark:text-gray-400">Use Case</th>
                                        <th className="text-left py-2 text-gray-600 dark:text-gray-400">Pixels</th>
                                        <th className="text-left py-2 text-gray-600 dark:text-gray-400">REM</th>
                                        <th className="text-left py-2 text-gray-600 dark:text-gray-400">EM</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {commonConversions.map((item) => {
                                        const conversion = convertUnits(item.px);
                                        return (
                                            <tr key={item.px} className="border-b border-gray-100 dark:border-gray-600">
                                                <td className="py-2 text-gray-700 dark:text-gray-300">{item.label}</td>
                                                <td className="py-2 font-mono">{item.px}px</td>
                                                <td className="py-2">
                                                    <button
                                                        onClick={() => copyToClipboard(`${conversion.rem}rem`, `table-rem-${item.px}`)}
                                                        className="font-mono text-primary dark:text-primary-dark hover:underline"
                                                    >
                                                        {conversion.rem}rem
                                                    </button>
                                                </td>
                                                <td className="py-2">
                                                    <button
                                                        onClick={() => copyToClipboard(`${conversion.em}em`, `table-em-${item.px}`)}
                                                        className="font-mono text-primary dark:text-primary-dark hover:underline"
                                                    >
                                                        {conversion.em}em
                                                    </button>
                                                </td>
                                            </tr>
                                        );
                                    })}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            )}
        </ToolContainer>
    );
}
