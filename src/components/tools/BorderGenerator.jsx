import React, { useState, useEffect } from "react";
import { getToolById } from "../../data/tools.js";
import ToolHeader from "../ui/ToolHeader.jsx";
import ToolContainer from "../ui/ToolContainer.jsx";
import LucideIcon from "../ui/LucideIcon.jsx";

export default function ExactBorderGenerator() {
    const [toolData, setToolData] = useState(null);
    const [borderType, setBorderType] = useState("zigzag");
    const [borderSize, setBorderSize] = useState(30);
    const [copied, setCopied] = useState(false);

    useEffect(() => {
        // Get the tool data
        const data = getToolById("bordergenerator");
        setToolData(data);
    }, []);

    // Border types based on the provided examples
    const BORDER_TYPES = [
        { id: "zigzag", name: "Zig-Zag" },
        { id: "scooped", name: "Scooped" },
        { id: "scalloped", name: "Scalloped" },
        { id: "wavy", name: "Wavy" },
    ];

    // Generate the exact CSS mask based on the examples
    const generateExactCSS = () => {
        // Use the size parameter to adjust the values in the examples
        const size = borderSize;

        switch (borderType) {
            case "zigzag":
                return `.box {
  mask: conic-gradient(from -45deg at bottom,#0000,#000 1deg 89deg,#0000 90deg) 50%/${
      size * 2
  }px 100%
}`;

            case "scooped":
                return `.box {
  mask: radial-gradient(${size}px at bottom,#0000 calc(100% - 1px),#000) 50%/${
                    size * 1.85
                }px 100%
}`;

            case "scalloped":
                return `.box {
  mask:
    linear-gradient(0,#0000 ${size}px,#000 0),
    radial-gradient(${size}px,#000 calc(100% - 1px),#0000) bottom/${
                    size * 1.85
                }px ${size * 2}px
}`;

            case "wavy":
                return `.box {
  mask:
    radial-gradient(${size}px at 75% 100%,#0000 98%,#000) 50% calc(100% - ${size}px)/${
                    size * 4
                }px 100% repeat-x,
    radial-gradient(${size}px at 25% 50%,#000 99%,#0000 101%) bottom/${
                    size * 4
                }px ${size * 2}px repeat-x
}`;

            default:
                return "";
        }
    };

    // Copy CSS to clipboard
    const copyToClipboard = () => {
        navigator.clipboard.writeText(generateExactCSS()).then(() => {
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        });
    };

    // Sample presets
    const PRESETS = [
        {
            name: "Zig-Zag (30px)",
            type: "zigzag",
            size: 30,
        },
        {
            name: "Scooped (30px)",
            type: "scooped",
            size: 30,
        },
        {
            name: "Scalloped (30px)",
            type: "scalloped",
            size: 30,
        },
        {
            name: "Wavy (30px)",
            type: "wavy",
            size: 30,
        },
    ];

    // Load a preset
    const loadPreset = (preset) => {
        setBorderType(preset.type);
        setBorderSize(preset.size);
    };

    return (
        <ToolContainer
            className="py-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-colors duration-200"
            containerWidth={toolData?.containerWidth || "default"}>
            <ToolHeader
                toolData={toolData}
                defaultTitle="Border Generator"
                defaultIcon="Frame"
                defaultDescription="Create custom CSS borders with different shapes like zig-zag, scooped, scalloped, or wavy using CSS masks."
            />

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Controls Panel */}
                <div className="space-y-6">
                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                            Border Type
                        </h2>
                        <div className="grid grid-cols-2 gap-3">
                            {BORDER_TYPES.map((type) => (
                                <button
                                    key={type.id}
                                    className={`p-3 rounded-lg border ${
                                        borderType === type.id
                                            ? "border-primary dark:border-primary-dark bg-primary/10 dark:bg-primary-dark/10"
                                            : "border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600"
                                    }`}
                                    onClick={() => setBorderType(type.id)}>
                                    <span className="text-sm font-medium">
                                        {type.name}
                                    </span>
                                </button>
                            ))}
                        </div>
                    </div>

                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                            Border Size
                        </h2>
                        <div className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Size (px)
                                </label>
                                <input
                                    type="range"
                                    min="10"
                                    max="60"
                                    value={borderSize}
                                    onChange={(e) =>
                                        setBorderSize(parseInt(e.target.value))
                                    }
                                    className="w-full"
                                />
                                <div className="flex justify-between text-sm text-gray-500 mt-1">
                                    <span>10px</span>
                                    <span>{borderSize}px</span>
                                    <span>60px</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                            Presets
                        </h2>
                        <div className="grid grid-cols-2 gap-2">
                            {PRESETS.map((preset, index) => (
                                <button
                                    key={index}
                                    className="p-2 text-left bg-white dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-600 rounded border border-gray-300 dark:border-gray-600 text-sm"
                                    onClick={() => loadPreset(preset)}>
                                    {preset.name}
                                </button>
                            ))}
                        </div>
                    </div>
                </div>

                {/* Preview and Code Panel */}
                <div className="space-y-6">
                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                            Preview
                        </h2>
                        <div className="w-full h-48 flex items-center justify-center bg-gray-100 dark:bg-gray-600 rounded-lg p-4">
                            <div
                                className="w-3/4 h-3/4 rounded-lg flex items-center justify-center relative overflow-hidden"
                                style={{
                                    backgroundColor: "#FFFFFF",
                                }}>
                                {/* Content */}
                                <div className="text-center font-medium z-10 text-gray-700">
                                    <div className="flex flex-col items-center">
                                        <span className="text-sm mb-1">
                                            {
                                                BORDER_TYPES.find(
                                                    (t) => t.id === borderType
                                                )?.name
                                            }{" "}
                                            Border
                                        </span>
                                        <span className="text-sm">
                                            Size: {borderSize}px
                                        </span>
                                    </div>
                                </div>

                                {/* Border mask element */}
                                <div
                                    className="absolute inset-0"
                                    style={{
                                        backgroundColor: "#4F46E5",
                                        ...(borderType === "zigzag" && {
                                            mask: `conic-gradient(from -45deg at bottom,#0000,#000 1deg 89deg,#0000 90deg) 50%/${
                                                borderSize * 2
                                            }px 100%`,
                                            WebkitMask: `conic-gradient(from -45deg at bottom,#0000,#000 1deg 89deg,#0000 90deg) 50%/${
                                                borderSize * 2
                                            }px 100%`,
                                        }),
                                        ...(borderType === "scooped" && {
                                            mask: `radial-gradient(${borderSize}px at bottom,#0000 calc(100% - 1px),#000) 50%/${
                                                borderSize * 1.85
                                            }px 100%`,
                                            WebkitMask: `radial-gradient(${borderSize}px at bottom,#0000 calc(100% - 1px),#000) 50%/${
                                                borderSize * 1.85
                                            }px 100%`,
                                        }),
                                        ...(borderType === "scalloped" && {
                                            mask: `linear-gradient(0,#0000 ${borderSize}px,#000 0), radial-gradient(${borderSize}px,#000 calc(100% - 1px),#0000) bottom/${
                                                borderSize * 1.85
                                            }px ${borderSize * 2}px`,
                                            WebkitMask: `linear-gradient(0,#0000 ${borderSize}px,#000 0), radial-gradient(${borderSize}px,#000 calc(100% - 1px),#0000) bottom/${
                                                borderSize * 1.85
                                            }px ${borderSize * 2}px`,
                                        }),
                                        ...(borderType === "wavy" && {
                                            mask: `radial-gradient(${borderSize}px at 75% 100%,#0000 98%,#000) 50% calc(100% - ${borderSize}px)/${
                                                borderSize * 4
                                            }px 100% repeat-x, radial-gradient(${borderSize}px at 25% 50%,#000 99%,#0000 101%) bottom/${
                                                borderSize * 4
                                            }px ${borderSize * 2}px repeat-x`,
                                            WebkitMask: `radial-gradient(${borderSize}px at 75% 100%,#0000 98%,#000) 50% calc(100% - ${borderSize}px)/${
                                                borderSize * 4
                                            }px 100% repeat-x, radial-gradient(${borderSize}px at 25% 50%,#000 99%,#0000 101%) bottom/${
                                                borderSize * 4
                                            }px ${borderSize * 2}px repeat-x`,
                                        }),
                                        pointerEvents: "none",
                                    }}></div>
                            </div>
                        </div>
                    </div>

                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <div className="flex justify-between items-center mb-4">
                            <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
                                CSS Code
                            </h2>
                            <button
                                className="px-3 py-1 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors flex items-center"
                                onClick={copyToClipboard}>
                                <LucideIcon
                                    name={copied ? "Check" : "Copy"}
                                    size={16}
                                    className="mr-1"
                                />
                                {copied ? "Copied!" : "Copy Code"}
                            </button>
                        </div>
                        <pre className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-300 dark:border-gray-600 overflow-x-auto text-sm">
                            {generateExactCSS()}
                        </pre>
                    </div>
                </div>
            </div>
        </ToolContainer>
    );
}
