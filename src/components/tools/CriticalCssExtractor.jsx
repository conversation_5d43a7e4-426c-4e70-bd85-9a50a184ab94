import { useState, useEffect } from "react";
import { getToolById } from "../../data/tools.js";
import ToolHeader from "../ui/ToolHeader.jsx";
import ToolContainer from "../ui/ToolContainer.jsx";

// Simple beautify functions as fallbacks
const simpleBeautifyHtml = (html) => {
    return html
        .replace(/></g, ">\n<")
        .replace(/<\/([^>]+)>/g, "</$1>\n")
        .replace(/<([^\/>]+)>/g, "\n<$1>");
};

const simpleBeautifyCss = (css) => {
    // First, normalize whitespace
    let normalized = css
        .replace(/\s+/g, " ")
        .replace(/\s*{\s*/g, " {")
        .replace(/\s*;\s*/g, ";")
        .replace(/\s*}\s*/g, "}")
        .trim();

    // Then add proper formatting
    return (
        normalized
            .replace(/{/g, " {\n  ")
            .replace(/;\s*/g, ";\n  ")
            .replace(/}/g, "\n}\n")
            // Clean up any empty lines within rule blocks
            .replace(/\n\s*\n/g, "\n")
    );
};

export default function CriticalCssExtractor() {
    const [toolData, setToolData] = useState(null);
    const [htmlInput, setHtmlInput] = useState("");
    const [cssInput, setCssInput] = useState("");
    const [criticalCssOutput, setCriticalCssOutput] = useState("");
    const [nonCriticalCssOutput, setNonCriticalCssOutput] = useState("");
    const [cleanedHtmlOutput, setCleanedHtmlOutput] = useState("");
    const [activeTab, setActiveTab] = useState("critical");
    const [processing, setProcessing] = useState(false);
    const [error, setError] = useState("");
    const [copied, setCopied] = useState(false);
    const [showDiff, setShowDiff] = useState(false);

    // Use our simple beautify functions
    const beautifyHtml = simpleBeautifyHtml;
    const beautifyCss = simpleBeautifyCss;

    useEffect(() => {
        // Get the tool data
        const data = getToolById("criticalcss");
        setToolData(data);
    }, []);

    // Sample HTML and CSS for demonstration
    const sampleHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Sample Page</title>
</head>
<body>
  <header class="site-header">
    <nav class="main-nav">
      <ul>
        <li><a href="#" class="nav-link">Home</a></li>
        <li><a href="#" class="nav-link">About</a></li>
        <li><a href="#" class="nav-link">Contact</a></li>
      </ul>
    </nav>
  </header>

  <main>
    <section class="hero">
      <h1 class="hero-title">Welcome to our website</h1>
      <p class="hero-text">This is a sample page to demonstrate critical CSS extraction.</p>
      <button class="cta-button">Get Started</button>
    </section>

    <section class="features">
      <div class="feature-card">
        <h2 class="feature-title">Feature 1</h2>
        <p class="feature-description">Description of feature 1</p>
      </div>
      <div class="feature-card">
        <h2 class="feature-title">Feature 2</h2>
        <p class="feature-description">Description of feature 2</p>
      </div>
    </section>
  </main>

  <footer class="site-footer">
    <p class="copyright">© 2023 Sample Company</p>
  </footer>
</body>
</html>`;

    const sampleCss = `/* Global styles */
body {
  font-family: 'Arial', sans-serif;
  line-height: 1.6;
  color: #333;
  margin: 0;
  padding: 0;
}

/* Header styles */
.site-header {
  background-color: #2c3e50;
  padding: 1rem 2rem;
  position: sticky;
  top: 0;
  z-index: 100;
}

.main-nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.main-nav li {
  margin-right: 1.5rem;
}

.nav-link {
  color: white;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s;
}

.nav-link:hover {
  color: #3498db;
}

/* Hero section */
.hero {
  background-color: #f8f9fa;
  padding: 4rem 2rem;
  text-align: center;
}

.hero-title {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.hero-text {
  font-size: 1.2rem;
  max-width: 600px;
  margin: 0 auto 2rem;
}

.cta-button {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.cta-button:hover {
  background-color: #2980b9;
}

/* Features section */
.features {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
  padding: 4rem 2rem;
}

.feature-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  width: 300px;
}

.feature-title {
  color: #2c3e50;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.feature-description {
  color: #666;
}

/* Footer styles */
.site-footer {
  background-color: #2c3e50;
  color: white;
  text-align: center;
  padding: 2rem;
  margin-top: 2rem;
}

.copyright {
  margin: 0;
  font-size: 0.9rem;
}

/* Media queries for responsive design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
  }

  .features {
    flex-direction: column;
    align-items: center;
  }
}`;

    // Load sample data
    const loadSampleData = () => {
        setHtmlInput(sampleHtml);
        setCssInput(sampleCss);
    };

    // Process the HTML and CSS to extract critical CSS
    const processCriticalCss = () => {
        if (!htmlInput || !cssInput) {
            setError("Please provide both HTML and CSS input");
            return;
        }

        setProcessing(true);
        setError("");

        try {
            // For this browser-based implementation, we'll use a simplified approach
            // to extract critical CSS based on the HTML content

            // Parse the HTML to find used classes
            const parser = new DOMParser();
            const doc = parser.parseFromString(htmlInput, "text/html");

            // Extract all class names from the HTML
            const allElements = doc.querySelectorAll("*");
            const usedClasses = new Set();

            allElements.forEach((element) => {
                if (element.classList) {
                    element.classList.forEach((className) => {
                        usedClasses.add(className);
                    });
                }
            });

            // Simple CSS parser to extract rules
            const cssRules = cssInput.match(/[^{}]+\{[^{}]+\}/g) || [];

            // Filter CSS rules to find critical ones (containing classes used in HTML)
            const criticalRules = [];
            const nonCriticalRules = [];

            cssRules.forEach((rule) => {
                const selector = rule.substring(0, rule.indexOf("{")).trim();

                // Check if this rule contains any of the used classes
                const isCritical = Array.from(usedClasses).some(
                    (className) =>
                        selector.includes(`.${className}`) ||
                        selector === className ||
                        // Also include basic element selectors and global styles
                        /^(body|html|h[1-6]|p|div|span|a|ul|ol|li|button|input|form)([^\w-]|$)/.test(
                            selector
                        )
                );

                if (isCritical) {
                    criticalRules.push(rule);
                } else {
                    nonCriticalRules.push(rule);
                }
            });

            // Join the rules back into CSS
            const criticalOutput = criticalRules.join("\n");
            const nonCriticalOutput = nonCriticalRules.join("\n");

            // Format the outputs
            setCriticalCssOutput(
                beautifyCss(criticalOutput, {
                    indent_size: 2,
                    space_in_empty_paren: true,
                })
            );

            setNonCriticalCssOutput(
                beautifyCss(nonCriticalOutput, {
                    indent_size: 2,
                    space_in_empty_paren: true,
                })
            );

            // Create cleaned HTML with inlined critical CSS
            const cleanedHtml = htmlInput.replace(
                "</head>",
                `<style>\n${criticalOutput}\n</style>\n</head>`
            );
            setCleanedHtmlOutput(
                beautifyHtml(cleanedHtml, {
                    indent_size: 2,
                    space_in_empty_paren: true,
                })
            );

            setShowDiff(true);
        } catch (err) {
            console.error("Error processing critical CSS:", err);
            setError(`Error: ${err.message}`);
        } finally {
            setProcessing(false);
        }
    };

    // Copy output to clipboard
    const copyToClipboard = (text) => {
        navigator.clipboard.writeText(text).then(() => {
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        });
    };

    // Clear all inputs and outputs
    const clearAll = () => {
        setHtmlInput("");
        setCssInput("");
        setCriticalCssOutput("");
        setNonCriticalCssOutput("");
        setCleanedHtmlOutput("");
        setShowDiff(false);
        setError("");
    };

    // Get the active output based on the selected tab
    const getActiveOutput = () => {
        switch (activeTab) {
            case "critical":
                return criticalCssOutput;
            case "nonCritical":
                return nonCriticalCssOutput;
            case "html":
                return cleanedHtmlOutput;
            default:
                return criticalCssOutput;
        }
    };

    return (
        <ToolContainer
            className="py-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-colors duration-200"
            containerWidth={toolData?.containerWidth || "default"}>
            <ToolHeader
                toolData={toolData}
                defaultTitle="Critical CSS Extractor"
                defaultIcon="Scissors"
                defaultDescription="Extract critical CSS from your HTML and CSS to improve page load times and performance. This tool helps you identify and separate the CSS needed for above-the-fold content."
            />

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                {/* HTML Input */}
                <div>
                    <div className="flex justify-between items-center mb-2">
                        <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
                            HTML Input
                        </h2>
                        <button
                            className="px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-md text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                            onClick={loadSampleData}>
                            Load Sample
                        </button>
                    </div>
                    <div className="relative">
                        <textarea
                            className="w-full h-80 p-4 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200 font-mono text-sm resize-none focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                            value={htmlInput}
                            onChange={(e) => setHtmlInput(e.target.value)}
                            placeholder="Paste your HTML here..."></textarea>
                    </div>
                </div>

                {/* CSS Input */}
                <div>
                    <div className="flex justify-between items-center mb-2">
                        <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
                            CSS Input
                        </h2>
                    </div>
                    <div className="relative">
                        <textarea
                            className="w-full h-80 p-4 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200 font-mono text-sm resize-none focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                            value={cssInput}
                            onChange={(e) => setCssInput(e.target.value)}
                            placeholder="Paste your CSS here..."></textarea>
                    </div>
                </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap justify-center gap-4 mb-8">
                <button
                    className="px-6 py-3 bg-primary dark:bg-primary-dark text-white rounded-lg hover:bg-primary-dark dark:hover:bg-primary transition-colors flex items-center"
                    onClick={processCriticalCss}
                    disabled={Boolean(processing || !htmlInput || !cssInput)}>
                    {processing ? (
                        <>
                            <svg
                                className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24">
                                <circle
                                    className="opacity-25"
                                    cx="12"
                                    cy="12"
                                    r="10"
                                    stroke="currentColor"
                                    strokeWidth="4"></circle>
                                <path
                                    className="opacity-75"
                                    fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Processing...
                        </>
                    ) : (
                        <>Extract Critical CSS</>
                    )}
                </button>
                <button
                    className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    onClick={clearAll}>
                    Clear All
                </button>
            </div>

            {/* Error Message */}
            {error && (
                <div className="mb-8 p-4 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-lg">
                    {error}
                </div>
            )}

            {/* Results Section */}
            {criticalCssOutput && (
                <div className="mb-8">
                    <h2 className="text-2xl font-semibold text-gray-800 dark:text-white mb-4">
                        Results
                    </h2>

                    {/* Tabs */}
                    <div className="flex border-b border-gray-200 dark:border-gray-700 mb-4">
                        <button
                            className={`py-2 px-4 font-medium ${
                                activeTab === "critical"
                                    ? "text-primary dark:text-primary-dark border-b-2 border-primary dark:border-primary-dark"
                                    : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                            }`}
                            onClick={() => setActiveTab("critical")}>
                            Critical CSS
                        </button>
                        <button
                            className={`py-2 px-4 font-medium ${
                                activeTab === "nonCritical"
                                    ? "text-primary dark:text-primary-dark border-b-2 border-primary dark:border-primary-dark"
                                    : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                            }`}
                            onClick={() => setActiveTab("nonCritical")}>
                            Non-Critical CSS
                        </button>
                        <button
                            className={`py-2 px-4 font-medium ${
                                activeTab === "html"
                                    ? "text-primary dark:text-primary-dark border-b-2 border-primary dark:border-primary-dark"
                                    : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                            }`}
                            onClick={() => setActiveTab("html")}>
                            Optimized HTML
                        </button>
                        <button
                            className={`py-2 px-4 font-medium ${
                                activeTab === "diff"
                                    ? "text-primary dark:text-primary-dark border-b-2 border-primary dark:border-primary-dark"
                                    : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                            }`}
                            onClick={() => setActiveTab("diff")}>
                            CSS Diff
                        </button>
                    </div>

                    {/* Tab Content */}
                    <div className="relative">
                        {activeTab === "diff" ? (
                            <div className="border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
                                    <div>
                                        <h3 className="text-lg font-medium mb-2 text-gray-800 dark:text-white">
                                            Original CSS
                                        </h3>
                                        <pre className="w-full h-80 p-4 overflow-auto border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-200 font-mono text-sm">
                                            {beautifyCss(cssInput)}
                                        </pre>
                                    </div>
                                    <div>
                                        <h3 className="text-lg font-medium mb-2 text-gray-800 dark:text-white">
                                            Critical CSS
                                        </h3>
                                        <pre className="w-full h-80 p-4 overflow-auto border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-200 font-mono text-sm">
                                            {criticalCssOutput}
                                        </pre>
                                    </div>
                                </div>
                            </div>
                        ) : (
                            <div className="relative">
                                <pre className="w-full h-80 p-4 overflow-auto border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-200 font-mono text-sm">
                                    {getActiveOutput()}
                                </pre>
                                <button
                                    className="absolute top-2 right-2 px-2 py-1 bg-primary dark:bg-primary-dark text-white text-xs rounded hover:bg-primary-dark dark:hover:bg-primary transition-colors"
                                    onClick={() =>
                                        copyToClipboard(getActiveOutput())
                                    }>
                                    {copied ? "Copied!" : "Copy"}
                                </button>
                            </div>
                        )}
                    </div>
                </div>
            )}

            {/* Information Section */}
            <div className="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg">
                <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                    What is Critical CSS?
                </h2>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                    Critical CSS is the minimal CSS required to render the
                    above-the-fold content of a webpage. Extracting and inlining
                    this CSS can significantly improve page load times by:
                </p>
                <ul className="list-disc pl-6 text-gray-600 dark:text-gray-400 mb-4 space-y-2">
                    <li>Reducing render-blocking resources</li>
                    <li>Displaying content to users faster</li>
                    <li>
                        Improving Core Web Vitals metrics like First Contentful
                        Paint (FCP)
                    </li>
                    <li>Enhancing the perceived performance of your website</li>
                </ul>
                <p className="text-gray-600 dark:text-gray-400">
                    The non-critical CSS can be loaded asynchronously after the
                    page has rendered, further optimizing the user experience.
                </p>
            </div>
        </ToolContainer>
    );
}
