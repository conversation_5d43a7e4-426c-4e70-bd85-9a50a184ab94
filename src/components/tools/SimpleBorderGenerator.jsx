import { useState, useEffect } from "react";
import { getToolById } from "../../data/tools.js";
import ToolHeader from "../ui/ToolHeader.jsx";
import ToolContainer from "../ui/ToolContainer.jsx";

export default function SimpleBorderGenerator() {
    console.log("SimpleBorderGenerator component is being rendered");

    const [toolData, setToolData] = useState(null);

    useEffect(() => {
        console.log("SimpleBorderGenerator useEffect is running");
        // Get the tool data
        const data = getToolById("bordergenerator");
        console.log("Tool data:", data);
        setToolData(data);
    }, []);

    console.log("SimpleBorderGenerator rendering return statement");

    return (
        <ToolContainer
            className="py-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-colors duration-200"
            containerWidth={toolData?.containerWidth || "default"}>
            <ToolHeader
                toolData={toolData}
                defaultTitle="Border Generator"
                defaultIcon="Frame"
                defaultDescription="Create custom CSS borders with different shapes like zig-zag, scooped, scalloped, or wavy. Customize the size, position, and get the CSS code."
            />

            <div className="p-6 bg-gray-50 dark:bg-gray-700 rounded-lg mt-6">
                <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                    Simple Border Generator
                </h2>
                <p className="text-gray-600 dark:text-gray-300">
                    This is a simplified version of the Border Generator tool to
                    debug rendering issues.
                </p>
            </div>
        </ToolContainer>
    );
}
