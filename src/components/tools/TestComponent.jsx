import React from "react";

export default function TestComponent() {
  console.log("TestComponent is rendering");
  
  return (
    <div className="p-6 bg-white dark:bg-gray-800 rounded-lg">
      <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">
        Test Component
      </h1>
      <p className="text-gray-600 dark:text-gray-300">
        This is a test component to debug rendering issues.
      </p>
      <button 
        className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        onClick={() => alert("Button clicked!")}>
        Click Me
      </button>
    </div>
  );
}
