import { useState, useEffect } from "react";
import { getToolById } from "../../data/tools.js";
import ToolHeader from "../ui/ToolHeader.jsx";
import LucideIcon from "../ui/LucideIcon.jsx";
import ToolContainer from "../ui/ToolContainer.jsx";

// HTTP methods
const HTTP_METHODS = [
    "GET",
    "POST",
    "PUT",
    "DELETE",
    "PATCH",
    "HEAD",
    "OPTIONS",
];

// Content types
const CONTENT_TYPES = {
    "application/json": "JSON",
    "application/x-www-form-urlencoded": "Form URL Encoded",
    "multipart/form-data": "Form Data",
    "text/plain": "Plain Text",
    "application/xml": "XML",
    "text/html": "HTML",
};

// Sample APIs for testing
const SAMPLE_APIS = [
    {
        name: "Random User API",
        url: "https://randomuser.me/api/",
        method: "GET",
        description: "Generates random user data",
    },
    {
        name: "JSONPlaceholder Posts",
        url: "https://jsonplaceholder.typicode.com/posts",
        method: "GET",
        description: "Fetch sample posts",
    },
    {
        name: "JSONPlaceholder Create Post",
        url: "https://jsonplaceholder.typicode.com/posts",
        method: "POST",
        description: "Create a new post",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(
            {
                title: "DevBottle API Test",
                body: "This is a test post from DevBottle API Tester",
                userId: 1,
            },
            null,
            2
        ),
    },
    {
        name: "Public APIs List",
        url: "https://api.publicapis.org/entries",
        method: "GET",
        description: "List of public APIs",
    },
];

export default function ApiTester() {
    const [toolData, setToolData] = useState(null);
    const [url, setUrl] = useState("");
    const [method, setMethod] = useState("GET");
    const [headers, setHeaders] = useState([{ key: "", value: "" }]);
    const [contentType, setContentType] = useState("application/json");
    const [requestBody, setRequestBody] = useState("");
    const [response, setResponse] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [responseTime, setResponseTime] = useState(null);
    const [copied, setCopied] = useState(false);
    const [activeTab, setActiveTab] = useState("body"); // body, headers, raw

    useEffect(() => {
        // Get the tool data
        const data = getToolById("apitester");
        setToolData(data);
    }, []);

    // Add a new header field
    const addHeader = () => {
        setHeaders([...headers, { key: "", value: "" }]);
    };

    // Remove a header field
    const removeHeader = (index) => {
        const newHeaders = [...headers];
        newHeaders.splice(index, 1);
        setHeaders(newHeaders);
    };

    // Update a header field
    const updateHeader = (index, field, value) => {
        const newHeaders = [...headers];
        newHeaders[index][field] = value;
        setHeaders(newHeaders);
    };

    // Load a sample API
    const loadSample = (sample) => {
        setUrl(sample.url);
        setMethod(sample.method);

        if (sample.headers) {
            const sampleHeaders = Object.entries(sample.headers).map(
                ([key, value]) => ({ key, value })
            );
            setHeaders(
                sampleHeaders.length ? sampleHeaders : [{ key: "", value: "" }]
            );

            // Set content type if present in headers
            const contentTypeHeader = sampleHeaders.find(
                (h) => h.key.toLowerCase() === "content-type"
            );
            if (contentTypeHeader) {
                setContentType(contentTypeHeader.value);
            }
        } else {
            setHeaders([{ key: "", value: "" }]);
        }

        if (sample.body) {
            setRequestBody(sample.body);
        } else {
            setRequestBody("");
        }

        // Clear previous response
        setResponse(null);
        setError(null);
        setResponseTime(null);
    };

    // Format JSON for display
    const formatJSON = (json) => {
        try {
            return JSON.stringify(json, null, 2);
        } catch (e) {
            return json;
        }
    };

    // Determine if the response is JSON
    const isJsonResponse = (response) => {
        const contentType = response.headers.get("content-type");
        return contentType && contentType.includes("application/json");
    };

    // Determine if the response is XML
    const isXmlResponse = (response) => {
        const contentType = response.headers.get("content-type");
        return (
            contentType &&
            (contentType.includes("application/xml") ||
                contentType.includes("text/xml"))
        );
    };

    // Format the response body based on content type
    const formatResponseBody = async (response) => {
        const contentType = response.headers.get("content-type");
        const text = await response.text();

        if (contentType && contentType.includes("application/json")) {
            try {
                return formatJSON(JSON.parse(text));
            } catch (e) {
                return text;
            }
        } else if (
            contentType &&
            (contentType.includes("application/xml") ||
                contentType.includes("text/xml"))
        ) {
            // Simple XML formatting - in a real app, you might want to use a proper XML formatter
            return text.replace(/></g, ">\n<").replace(/><\//g, ">\n</");
        } else {
            return text;
        }
    };

    // Send the API request
    const sendRequest = async (e) => {
        e.preventDefault();

        if (!url) {
            setError("Please enter a URL");
            return;
        }

        setIsLoading(true);
        setError(null);
        setResponse(null);
        setResponseTime(null);

        try {
            const startTime = performance.now();

            // Prepare headers
            const headerObj = {};
            headers.forEach((header) => {
                if (header.key && header.value) {
                    headerObj[header.key] = header.value;
                }
            });

            // Add content-type header if not already present and we have a body
            if (
                requestBody &&
                method !== "GET" &&
                method !== "HEAD" &&
                !headerObj["Content-Type"]
            ) {
                headerObj["Content-Type"] = contentType;
            }

            // Prepare request options
            const options = {
                method,
                headers: headerObj,
                // Don't add body for GET and HEAD requests
                ...(method !== "GET" && method !== "HEAD" && requestBody
                    ? { body: requestBody }
                    : {}),
            };

            const response = await fetch(url, options);
            const endTime = performance.now();
            setResponseTime(Math.round(endTime - startTime));

            // Format the response body
            const formattedBody = await formatResponseBody(response);

            // Convert headers to an object
            const responseHeaders = {};
            response.headers.forEach((value, key) => {
                responseHeaders[key] = value;
            });

            setResponse({
                status: response.status,
                statusText: response.statusText,
                headers: responseHeaders,
                body: formattedBody,
                ok: response.ok,
            });
        } catch (err) {
            setError(err.message);
        } finally {
            setIsLoading(false);
        }
    };

    // Clear the form
    const clearForm = () => {
        setUrl("");
        setMethod("GET");
        setHeaders([{ key: "", value: "" }]);
        setContentType("application/json");
        setRequestBody("");
        setResponse(null);
        setError(null);
        setResponseTime(null);
    };

    // Copy response to clipboard
    const copyToClipboard = () => {
        if (!response) return;

        let textToCopy;
        if (activeTab === "body") {
            textToCopy = response.body;
        } else if (activeTab === "headers") {
            textToCopy = formatJSON(response.headers);
        } else {
            textToCopy = formatJSON(response);
        }

        navigator.clipboard.writeText(textToCopy).then(() => {
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        });
    };

    return (
        <ToolContainer
            className="py-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-colors duration-200"
            containerWidth={toolData?.containerWidth || "default"}>
            <ToolHeader
                toolData={toolData}
                defaultTitle="API Tester"
                defaultIcon="ServerCog"
                defaultDescription="Test and debug API endpoints with our intuitive interface. Send GET, POST, PUT, DELETE and other HTTP requests with custom headers and request bodies. View formatted JSON or XML responses, check status codes, and measure response times."
            />

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Request Panel */}
                <div>
                    <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                        Request
                    </h2>

                    <form onSubmit={sendRequest} className="space-y-6">
                        {/* URL and Method */}
                        <div className="space-y-2">
                            <label
                                htmlFor="url"
                                className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                URL
                            </label>
                            <div className="flex space-x-2">
                                <select
                                    value={method}
                                    onChange={(e) => setMethod(e.target.value)}
                                    className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200 focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark">
                                    {HTTP_METHODS.map((m) => (
                                        <option key={m} value={m}>
                                            {m}
                                        </option>
                                    ))}
                                </select>
                                <input
                                    id="url"
                                    type="text"
                                    value={url}
                                    onChange={(e) => setUrl(e.target.value)}
                                    placeholder="https://api.example.com/endpoint"
                                    className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200 focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                />
                            </div>
                        </div>

                        {/* Headers */}
                        <div className="space-y-2">
                            <div className="flex justify-between items-center">
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Headers
                                </label>
                                <button
                                    type="button"
                                    onClick={addHeader}
                                    className="text-primary dark:text-primary-dark text-sm flex items-center">
                                    <LucideIcon
                                        name="Plus"
                                        size={16}
                                        className="mr-1"
                                    />
                                    Add Header
                                </button>
                            </div>

                            <div className="space-y-2">
                                {headers.map((header, index) => (
                                    <div key={index} className="flex space-x-2">
                                        <input
                                            type="text"
                                            value={header.key}
                                            onChange={(e) =>
                                                updateHeader(
                                                    index,
                                                    "key",
                                                    e.target.value
                                                )
                                            }
                                            placeholder="Header name"
                                            className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200 focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                        />
                                        <input
                                            type="text"
                                            value={header.value}
                                            onChange={(e) =>
                                                updateHeader(
                                                    index,
                                                    "value",
                                                    e.target.value
                                                )
                                            }
                                            placeholder="Value"
                                            className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200 focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                        />
                                        {headers.length > 1 && (
                                            <button
                                                type="button"
                                                onClick={() =>
                                                    removeHeader(index)
                                                }
                                                className="px-2 py-2 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300">
                                                <LucideIcon
                                                    name="X"
                                                    size={16}
                                                />
                                            </button>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Request Body (for non-GET methods) */}
                        {method !== "GET" && method !== "HEAD" && (
                            <div className="space-y-2">
                                <div className="flex justify-between items-center">
                                    <label
                                        htmlFor="requestBody"
                                        className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Request Body
                                    </label>
                                    <select
                                        value={contentType}
                                        onChange={(e) =>
                                            setContentType(e.target.value)
                                        }
                                        className="text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 px-2 py-1">
                                        {Object.entries(CONTENT_TYPES).map(
                                            ([value, label]) => (
                                                <option
                                                    key={value}
                                                    value={value}>
                                                    {label}
                                                </option>
                                            )
                                        )}
                                    </select>
                                </div>
                                <textarea
                                    id="requestBody"
                                    value={requestBody}
                                    onChange={(e) =>
                                        setRequestBody(e.target.value)
                                    }
                                    placeholder={
                                        contentType.includes("json")
                                            ? '{\n  "key": "value"\n}'
                                            : "Request body..."
                                    }
                                    className="w-full h-40 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200 font-mono text-sm focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                />
                            </div>
                        )}

                        {/* Action Buttons */}
                        <div className="flex flex-wrap gap-3">
                            <button
                                type="submit"
                                disabled={isLoading}
                                className="px-4 py-2 bg-primary dark:bg-primary-dark text-white rounded-lg hover:bg-primary-dark dark:hover:bg-primary transition-colors flex items-center">
                                {isLoading ? (
                                    <>
                                        <svg
                                            className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24">
                                            <circle
                                                className="opacity-25"
                                                cx="12"
                                                cy="12"
                                                r="10"
                                                stroke="currentColor"
                                                strokeWidth="4"></circle>
                                            <path
                                                className="opacity-75"
                                                fill="currentColor"
                                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Sending...
                                    </>
                                ) : (
                                    <>
                                        <LucideIcon
                                            name="Send"
                                            size={16}
                                            className="mr-2"
                                        />
                                        Send Request
                                    </>
                                )}
                            </button>

                            <button
                                type="button"
                                onClick={clearForm}
                                className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                                Clear
                            </button>
                        </div>
                    </form>

                    {/* Sample APIs */}
                    <div className="mt-8">
                        <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-3">
                            Sample APIs
                        </h3>
                        <div className="space-y-2">
                            {SAMPLE_APIS.map((sample, index) => (
                                <div
                                    key={index}
                                    onClick={() => loadSample(sample)}
                                    className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    <div className="flex justify-between items-center">
                                        <div>
                                            <h4 className="font-medium text-gray-800 dark:text-white">
                                                {sample.name}
                                            </h4>
                                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                                {sample.description}
                                            </p>
                                        </div>
                                        <span
                                            className={`px-2 py-1 text-xs rounded ${
                                                sample.method === "GET"
                                                    ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                                                    : sample.method === "POST"
                                                    ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                                    : sample.method === "PUT"
                                                    ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                                                    : sample.method === "DELETE"
                                                    ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                                                    : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"
                                            }`}>
                                            {sample.method}
                                        </span>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>

                {/* Response Panel */}
                <div>
                    <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                        Response
                    </h2>

                    {error && (
                        <div className="p-4 mb-4 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-lg">
                            <p className="font-medium">Error</p>
                            <p>{error}</p>
                        </div>
                    )}

                    {response && (
                        <div className="space-y-4">
                            {/* Status and Time */}
                            <div className="flex flex-wrap gap-4">
                                <div
                                    className={`px-3 py-2 rounded-lg ${
                                        response.status < 300
                                            ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                            : response.status < 400
                                            ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                                            : response.status < 500
                                            ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                                            : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                                    }`}>
                                    Status: {response.status}{" "}
                                    {response.statusText}
                                </div>

                                {responseTime && (
                                    <div className="px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg">
                                        Time: {responseTime} ms
                                    </div>
                                )}
                            </div>

                            {/* Response Tabs */}
                            <div className="border-b border-gray-200 dark:border-gray-700">
                                <nav className="flex space-x-4">
                                    <button
                                        onClick={() => setActiveTab("body")}
                                        className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                            activeTab === "body"
                                                ? "border-primary dark:border-primary-dark text-primary dark:text-primary-dark"
                                                : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                                        }`}>
                                        Body
                                    </button>
                                    <button
                                        onClick={() => setActiveTab("headers")}
                                        className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                            activeTab === "headers"
                                                ? "border-primary dark:border-primary-dark text-primary dark:text-primary-dark"
                                                : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                                        }`}>
                                        Headers
                                    </button>
                                    <button
                                        onClick={() => setActiveTab("raw")}
                                        className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                            activeTab === "raw"
                                                ? "border-primary dark:border-primary-dark text-primary dark:text-primary-dark"
                                                : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                                        }`}>
                                        Raw
                                    </button>
                                </nav>
                            </div>

                            {/* Response Content */}
                            <div className="relative">
                                <pre className="w-full h-96 p-4 overflow-auto border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-900 text-gray-800 dark:text-gray-200 font-mono text-sm">
                                    {activeTab === "body" && response.body}
                                    {activeTab === "headers" &&
                                        formatJSON(response.headers)}
                                    {activeTab === "raw" &&
                                        formatJSON(response)}
                                </pre>

                                <button
                                    onClick={copyToClipboard}
                                    className="absolute top-2 right-2 px-3 py-1 bg-primary dark:bg-primary-dark text-white rounded-md text-sm hover:bg-primary-dark dark:hover:bg-primary transition-colors">
                                    {copied ? "Copied!" : "Copy"}
                                </button>
                            </div>
                        </div>
                    )}

                    {!response && !error && (
                        <div className="flex flex-col items-center justify-center h-96 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-900 text-gray-500 dark:text-gray-400">
                            <LucideIcon
                                name="ServerCog"
                                size={48}
                                className="mb-4 opacity-50"
                            />
                            <p className="text-lg">
                                Send a request to see the response
                            </p>
                            <p className="text-sm mt-2">
                                Try one of the sample APIs to get started
                            </p>
                        </div>
                    )}
                </div>
            </div>
        </ToolContainer>
    );
}
