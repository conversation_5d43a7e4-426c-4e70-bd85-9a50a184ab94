import React, { useState, useEffect } from "react";
import { getToolById } from "../../data/tools.js";
import ToolHeader from "../ui/ToolHeader.jsx";
import ToolContainer from "../ui/ToolContainer.jsx";
import LucideIcon from "../ui/LucideIcon.jsx";

export default function MaskBorderGenerator() {
  const [toolData, setToolData] = useState(null);
  const [borderType, setBorderType] = useState("wavy");
  const [borderPosition, setBorderPosition] = useState("bottom");
  const [borderSize, setBorderSize] = useState(30);
  const [borderColor, setBorderColor] = useState("#4F46E5");
  const [backgroundColor, setBackgroundColor] = useState("#FFFFFF");
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    // Get the tool data
    const data = getToolById("bordergenerator");
    setToolData(data);
  }, []);

  // Border types based on the provided examples
  const BORDER_TYPES = [
    { id: "zigzag", name: "Zig-Zag" },
    { id: "scooped", name: "Scooped" },
    { id: "scalloped", name: "Scalloped" },
    { id: "wavy", name: "Wavy" }
  ];

  // Border positions
  const BORDER_POSITIONS = [
    { id: "bottom", name: "Bottom" },
    { id: "top", name: "Top" },
    { id: "left", name: "Left" },
    { id: "right", name: "Right" }
  ];

  // Generate CSS mask based on border type and position
  const generateMask = () => {
    const isHorizontal = borderPosition === 'top' || borderPosition === 'bottom';
    const isReversed = borderPosition === 'top' || borderPosition === 'left';
    
    // Adjust size for different border types
    const size = borderSize;
    const halfSize = size / 2;
    
    switch (borderType) {
      case 'zigzag':
        if (isHorizontal) {
          const angle = isReversed ? '135deg' : '-45deg';
          const position = isReversed ? 'top' : 'bottom';
          return `conic-gradient(from ${angle} at ${position},#0000,#000 1deg 89deg,#0000 90deg) 50%/${size * 2}px 100%`;
        } else {
          const angle = isReversed ? '-135deg' : '45deg';
          const position = isReversed ? 'left' : 'right';
          return `conic-gradient(from ${angle} at ${position},#0000,#000 1deg 89deg,#0000 90deg) 50%/100% ${size * 2}px`;
        }
        
      case 'scooped':
        if (isHorizontal) {
          const position = isReversed ? 'top' : 'bottom';
          return `radial-gradient(${size}px at ${position},#0000 calc(100% - 1px),#000) 50%/${size * 1.85}px 100%`;
        } else {
          const position = isReversed ? 'left' : 'right';
          return `radial-gradient(${size}px at ${position},#0000 calc(100% - 1px),#000) 50%/100% ${size * 1.85}px`;
        }
        
      case 'scalloped':
        if (isHorizontal) {
          const direction = isReversed ? '180deg' : '0';
          const position = isReversed ? 'top' : 'bottom';
          return `linear-gradient(${direction},#0000 ${size}px,#000 0), radial-gradient(${size}px,#000 calc(100% - 1px),#0000) ${position}/${size * 1.85}px ${size * 2}px`;
        } else {
          const direction = isReversed ? '270deg' : '90deg';
          const position = isReversed ? 'left' : 'right';
          return `linear-gradient(${direction},#0000 ${size}px,#000 0), radial-gradient(${size}px,#000 calc(100% - 1px),#0000) ${position}/${size * 2}px ${size * 1.85}px`;
        }
        
      case 'wavy':
        if (isHorizontal) {
          const position1 = isReversed ? '25% 0%' : '75% 100%';
          const position2 = isReversed ? '75% 50%' : '25% 50%';
          const yPos = isReversed ? `${size}px` : `calc(100% - ${size}px)`;
          const verticalPosition = isReversed ? 'top' : 'bottom';
          
          return `radial-gradient(${size}px at ${position1},#0000 98%,#000) 50% ${yPos}/${size * 4}px 100% repeat-x, radial-gradient(${size}px at ${position2},#000 99%,#0000 101%) ${verticalPosition}/${size * 4}px ${size * 2}px repeat-x`;
        } else {
          const position1 = isReversed ? '0% 25%' : '100% 75%';
          const position2 = isReversed ? '50% 75%' : '50% 25%';
          const xPos = isReversed ? `${size}px` : `calc(100% - ${size}px)`;
          const horizontalPosition = isReversed ? 'left' : 'right';
          
          return `radial-gradient(${size}px at ${position1},#0000 98%,#000) ${xPos} 50%/100% ${size * 4}px repeat-y, radial-gradient(${size}px at ${position2},#000 99%,#0000 101%) ${horizontalPosition}/${size * 2}px ${size * 4}px repeat-y`;
        }
        
      default:
        return 'none';
    }
  };

  // Generate CSS for the selected border type and position
  const generateCSS = () => {
    const mask = generateMask();
    const isHorizontal = borderPosition === 'top' || borderPosition === 'bottom';
    const padding = Math.round(borderSize * 1.5);
    
    return `.border-${borderType}-${borderPosition} {
  position: relative;
  background-color: ${backgroundColor};
  padding-${borderPosition}: ${padding}px;
}

.border-${borderType}-${borderPosition}::before {
  content: "";
  position: absolute;
  ${borderPosition === 'top' ? 'top: 0;' : ''}
  ${borderPosition === 'bottom' ? 'bottom: 0;' : ''}
  ${borderPosition === 'left' ? 'left: 0;' : ''}
  ${borderPosition === 'right' ? 'right: 0;' : ''}
  ${isHorizontal ? 'left: 0; width: 100%;' : 'top: 0; height: 100%;'}
  ${isHorizontal ? `height: ${borderSize * 2}px;` : `width: ${borderSize * 2}px;`}
  background-color: ${borderColor};
  mask: ${mask};
  -webkit-mask: ${mask};
}

/* Example usage */
.my-element {
  /* Apply the border */
  ${borderType === 'zigzag' ? '/* Zig-zag border */' : ''}
  ${borderType === 'scooped' ? '/* Scooped border */' : ''}
  ${borderType === 'scalloped' ? '/* Scalloped border */' : ''}
  ${borderType === 'wavy' ? '/* Wavy border */' : ''}
  ${borderPosition === 'top' ? '/* Top position */' : ''}
  ${borderPosition === 'bottom' ? '/* Bottom position */' : ''}
  ${borderPosition === 'left' ? '/* Left position */' : ''}
  ${borderPosition === 'right' ? '/* Right position */' : ''}
  
  /* Add the border class */
  @extend .border-${borderType}-${borderPosition};
  
  /* Your other styles */
}`;
  };

  // Copy CSS to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(generateCSS()).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };

  // Sample presets
  const PRESETS = [
    {
      name: "Wavy Bottom",
      type: "wavy",
      position: "bottom",
      size: 30,
      color: "#4F46E5",
      bgColor: "#FFFFFF"
    },
    {
      name: "Zig-Zag Top",
      type: "zigzag",
      position: "top",
      size: 20,
      color: "#EF4444",
      bgColor: "#FFFFFF"
    },
    {
      name: "Scooped Left",
      type: "scooped",
      position: "left",
      size: 25,
      color: "#10B981",
      bgColor: "#FFFFFF"
    },
    {
      name: "Scalloped Right",
      type: "scalloped",
      position: "right",
      size: 30,
      color: "#F59E0B",
      bgColor: "#FFFFFF"
    }
  ];

  // Load a preset
  const loadPreset = (preset) => {
    setBorderType(preset.type);
    setBorderPosition(preset.position);
    setBorderSize(preset.size);
    setBorderColor(preset.color);
    setBackgroundColor(preset.bgColor);
  };

  // Get preview style
  const getPreviewStyle = () => {
    const mask = generateMask();
    const isHorizontal = borderPosition === 'top' || borderPosition === 'bottom';
    
    return {
      position: "relative",
      backgroundColor: backgroundColor,
      [`padding${borderPosition.charAt(0).toUpperCase() + borderPosition.slice(1)}`]: `${Math.round(borderSize * 1.5)}px`,
      overflow: "hidden"
    };
  };

  // Get border element style
  const getBorderStyle = () => {
    const mask = generateMask();
    const isHorizontal = borderPosition === 'top' || borderPosition === 'bottom';
    
    return {
      content: '""',
      position: "absolute",
      [borderPosition]: 0,
      ...(isHorizontal ? { left: 0, width: '100%' } : { top: 0, height: '100%' }),
      ...(isHorizontal ? { height: `${borderSize * 2}px` } : { width: `${borderSize * 2}px` }),
      backgroundColor: borderColor,
      mask: mask,
      WebkitMask: mask
    };
  };

  return (
    <ToolContainer
      className="py-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-colors duration-200"
      containerWidth={toolData?.containerWidth || "default"}>
      <ToolHeader
        toolData={toolData}
        defaultTitle="Border Generator"
        defaultIcon="Square"
        defaultDescription="Create custom CSS borders with different shapes like zig-zag, scooped, scalloped, or wavy. Customize the size, position, and get the CSS code."
      />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Controls Panel */}
        <div className="space-y-6">
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
              Border Type
            </h2>
            <div className="grid grid-cols-2 gap-3">
              {BORDER_TYPES.map((type) => (
                <button
                  key={type.id}
                  className={`p-3 rounded-lg border ${
                    borderType === type.id
                      ? "border-primary dark:border-primary-dark bg-primary/10 dark:bg-primary-dark/10"
                      : "border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600"
                  }`}
                  onClick={() => setBorderType(type.id)}>
                  <span className="text-sm font-medium">
                    {type.name}
                  </span>
                </button>
              ))}
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
              Border Position
            </h2>
            <div className="grid grid-cols-2 gap-3">
              {BORDER_POSITIONS.map((position) => (
                <button
                  key={position.id}
                  className={`p-3 rounded-lg border ${
                    borderPosition === position.id
                      ? "border-primary dark:border-primary-dark bg-primary/10 dark:bg-primary-dark/10"
                      : "border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600"
                  }`}
                  onClick={() => setBorderPosition(position.id)}>
                  <span className="text-sm font-medium">
                    {position.name}
                  </span>
                </button>
              ))}
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
              Border Properties
            </h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Size (px)
                </label>
                <input
                  type="range"
                  min="10"
                  max="50"
                  value={borderSize}
                  onChange={(e) => setBorderSize(parseInt(e.target.value))}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-gray-500 mt-1">
                  <span>10px</span>
                  <span>{borderSize}px</span>
                  <span>50px</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Border Color
                </label>
                <input
                  type="color"
                  value={borderColor}
                  onChange={(e) => setBorderColor(e.target.value)}
                  className="w-full h-10 rounded border border-gray-300 dark:border-gray-600"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Background Color
                </label>
                <input
                  type="color"
                  value={backgroundColor}
                  onChange={(e) => setBackgroundColor(e.target.value)}
                  className="w-full h-10 rounded border border-gray-300 dark:border-gray-600"
                />
              </div>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
              Presets
            </h2>
            <div className="grid grid-cols-2 gap-2">
              {PRESETS.map((preset, index) => (
                <button
                  key={index}
                  className="p-2 text-left bg-white dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-600 rounded border border-gray-300 dark:border-gray-600 text-sm"
                  onClick={() => loadPreset(preset)}>
                  {preset.name}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Preview and Code Panel */}
        <div className="space-y-6">
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
              Preview
            </h2>
            <div className="w-full h-40 flex items-center justify-center">
              <div 
                className="w-3/4 h-3/4 rounded-lg flex items-center justify-center"
                style={getPreviewStyle()}
              >
                <div className="text-center font-medium" style={{ color: getContrastColor(backgroundColor) }}>
                  <div className="flex flex-col items-center">
                    <span className="text-sm mb-1">
                      {BORDER_TYPES.find(t => t.id === borderType)?.name} Border
                    </span>
                    <span className="text-sm">
                      {BORDER_POSITIONS.find(p => p.id === borderPosition)?.name} Position
                    </span>
                  </div>
                </div>
                
                {/* The actual border element */}
                <div
                  className="absolute"
                  style={getBorderStyle()}
                ></div>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
                CSS Code
              </h2>
              <button
                className="px-3 py-1 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors flex items-center"
                onClick={copyToClipboard}>
                <LucideIcon
                  name={copied ? "Check" : "Copy"}
                  size={16}
                  className="mr-1"
                />
                {copied ? "Copied!" : "Copy Code"}
              </button>
            </div>
            <pre className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-300 dark:border-gray-600 overflow-x-auto text-sm">
              {generateCSS()}
            </pre>
          </div>
        </div>
      </div>
    </ToolContainer>
  );
  
  // Helper function to determine text color based on background color
  function getContrastColor(hexColor) {
    // Convert hex to RGB
    const r = parseInt(hexColor.substr(1, 2), 16);
    const g = parseInt(hexColor.substr(3, 2), 16);
    const b = parseInt(hexColor.substr(5, 2), 16);
    
    // Calculate luminance
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
    
    // Return black or white based on luminance
    return luminance > 0.5 ? '#000000' : '#FFFFFF';
  }
}
