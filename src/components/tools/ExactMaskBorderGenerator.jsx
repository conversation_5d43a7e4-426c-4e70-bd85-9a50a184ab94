import React, { useState, useEffect } from "react";
import { getToolById } from "../../data/tools.js";
import ToolHeader from "../ui/ToolHeader.jsx";
import ToolContainer from "../ui/ToolContainer.jsx";
import LucideIcon from "../ui/LucideIcon.jsx";

export default function ExactMaskBorderGenerator() {
    const [toolData, setToolData] = useState(null);
    const [borderType, setBorderType] = useState("zigzag");
    const [borderPosition, setBorderPosition] = useState("bottom");
    const [borderSize, setBorderSize] = useState(30);
    const [borderColor, setBorderColor] = useState("#4F46E5");
    const [backgroundColor, setBackgroundColor] = useState("#FFFFFF");
    const [copied, setCopied] = useState(false);

    useEffect(() => {
        // Get the tool data
        const data = getToolById("bordergenerator");
        setToolData(data);
    }, []);

    // Border types based on the provided examples
    const BORDER_TYPES = [
        { id: "zigzag", name: "Zig-Zag" },
        { id: "scooped", name: "Scooped" },
        { id: "scalloped", name: "Scalloped" },
        { id: "wavy", name: "Wavy" },
    ];

    // Border positions
    const BORDER_POSITIONS = [
        { id: "bottom", name: "Bottom" },
        { id: "top", name: "Top" },
        { id: "left", name: "Left" },
        { id: "right", name: "Right" },
    ];

    // Generate the exact CSS mask based on the examples
    const generateExactMask = () => {
        // Adjust size based on the border size input
        const size = borderSize;
        const halfSize = Math.round(size / 2);
        const doubleSize = size * 2;
        const quadSize = size * 4;

        // Position mapping
        const positionMap = {
            bottom: "bottom",
            top: "top",
            left: "left",
            right: "right",
        };

        const position = positionMap[borderPosition];
        const isHorizontal =
            borderPosition === "top" || borderPosition === "bottom";

        // Generate mask based on border type and position
        switch (borderType) {
            case "zigzag":
                if (isHorizontal) {
                    const angle =
                        borderPosition === "top" ? "135deg" : "-45deg";
                    return `conic-gradient(from ${angle} at ${position},#0000,#000 1deg 89deg,#0000 90deg) 50%/${doubleSize}px 100%`;
                } else {
                    const angle =
                        borderPosition === "left" ? "45deg" : "-135deg";
                    return `conic-gradient(from ${angle} at ${position},#0000,#000 1deg 89deg,#0000 90deg) 50%/100% ${doubleSize}px`;
                }

            case "scooped":
                if (isHorizontal) {
                    return `radial-gradient(${size}px at ${position},#0000 calc(100% - 1px),#000) 50%/${
                        size * 1.85
                    }px 100%`;
                } else {
                    return `radial-gradient(${size}px at ${position},#0000 calc(100% - 1px),#000) 50%/100% ${
                        size * 1.85
                    }px`;
                }

            case "scalloped":
                if (isHorizontal) {
                    return `linear-gradient(${
                        borderPosition === "top" ? "180deg" : "0"
                    },#0000 ${size}px,#000 0),
    radial-gradient(${size}px,#000 calc(100% - 1px),#0000) ${position}/${
                        size * 1.85
                    }px ${doubleSize}px`;
                } else {
                    return `linear-gradient(${
                        borderPosition === "left" ? "270deg" : "90deg"
                    },#0000 ${size}px,#000 0),
    radial-gradient(${size}px,#000 calc(100% - 1px),#0000) ${position}/${doubleSize}px ${
                        size * 1.85
                    }px`;
                }

            case "wavy":
                if (isHorizontal) {
                    return `radial-gradient(${size}px at ${
                        borderPosition === "top" ? "25% 0%" : "75% 100%"
                    },#0000 98%,#000) 50% ${
                        borderPosition === "top" ? size : `calc(100% - ${size})`
                    }px/${quadSize}px 100% repeat-x,
    radial-gradient(${size}px at ${
                        borderPosition === "top" ? "75% 50%" : "25% 50%"
                    },#000 99%,#0000 101%) ${position}/${quadSize}px ${doubleSize}px repeat-x`;
                } else {
                    return `radial-gradient(${size}px at ${
                        borderPosition === "left" ? "0% 25%" : "100% 75%"
                    },#0000 98%,#000) ${
                        borderPosition === "left"
                            ? size
                            : `calc(100% - ${size})`
                    }px 50%/100% ${quadSize}px repeat-y,
    radial-gradient(${size}px at ${
                        borderPosition === "left" ? "50% 75%" : "50% 25%"
                    },#000 99%,#0000 101%) ${position}/${doubleSize}px ${quadSize}px repeat-y`;
                }

            default:
                return "";
        }
    };

    // Generate the complete CSS
    const generateCSS = () => {
        const mask = generateExactMask();

        return `.${borderType}-${borderPosition}-border {
  position: relative;
  background-color: ${backgroundColor};
}

.${borderType}-${borderPosition}-border::before {
  content: "";
  position: absolute;
  inset: 0;
  background-color: ${borderColor};
  mask: ${mask};
  -webkit-mask: ${mask};
  pointer-events: none;
}`;
    };

    // Copy CSS to clipboard
    const copyToClipboard = () => {
        navigator.clipboard.writeText(generateCSS()).then(() => {
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        });
    };

    // Sample presets
    const PRESETS = [
        {
            name: "Zig-Zag Bottom",
            type: "zigzag",
            position: "bottom",
            size: 30,
            color: "#4F46E5",
            bgColor: "#FFFFFF",
        },
        {
            name: "Scooped Top",
            type: "scooped",
            position: "top",
            size: 30,
            color: "#EF4444",
            bgColor: "#FFFFFF",
        },
        {
            name: "Scalloped Left",
            type: "scalloped",
            position: "left",
            size: 30,
            color: "#10B981",
            bgColor: "#FFFFFF",
        },
        {
            name: "Wavy Right",
            type: "wavy",
            position: "right",
            size: 30,
            color: "#F59E0B",
            bgColor: "#FFFFFF",
        },
    ];

    // Load a preset
    const loadPreset = (preset) => {
        setBorderType(preset.type);
        setBorderPosition(preset.position);
        setBorderSize(preset.size);
        setBorderColor(preset.color);
        setBackgroundColor(preset.bgColor);
    };

    return (
        <ToolContainer
            className="py-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-colors duration-200"
            containerWidth={toolData?.containerWidth || "default"}>
            <ToolHeader
                toolData={toolData}
                defaultTitle="Border Generator"
                defaultIcon="Frame"
                defaultDescription="Create custom CSS borders with different shapes like zig-zag, scooped, scalloped, or wavy using CSS masks."
            />

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Controls Panel */}
                <div className="space-y-6">
                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                            Border Type
                        </h2>
                        <div className="grid grid-cols-2 gap-3">
                            {BORDER_TYPES.map((type) => (
                                <button
                                    key={type.id}
                                    className={`p-3 rounded-lg border ${
                                        borderType === type.id
                                            ? "border-primary dark:border-primary-dark bg-primary/10 dark:bg-primary-dark/10"
                                            : "border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600"
                                    }`}
                                    onClick={() => setBorderType(type.id)}>
                                    <span className="text-sm font-medium">
                                        {type.name}
                                    </span>
                                </button>
                            ))}
                        </div>
                    </div>

                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                            Border Position
                        </h2>
                        <div className="grid grid-cols-2 gap-3">
                            {BORDER_POSITIONS.map((position) => (
                                <button
                                    key={position.id}
                                    className={`p-3 rounded-lg border ${
                                        borderPosition === position.id
                                            ? "border-primary dark:border-primary-dark bg-primary/10 dark:bg-primary-dark/10"
                                            : "border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600"
                                    }`}
                                    onClick={() =>
                                        setBorderPosition(position.id)
                                    }>
                                    <span className="text-sm font-medium">
                                        {position.name}
                                    </span>
                                </button>
                            ))}
                        </div>
                    </div>

                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                            Border Properties
                        </h2>
                        <div className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Size (px)
                                </label>
                                <input
                                    type="range"
                                    min="10"
                                    max="60"
                                    value={borderSize}
                                    onChange={(e) =>
                                        setBorderSize(parseInt(e.target.value))
                                    }
                                    className="w-full"
                                />
                                <div className="flex justify-between text-sm text-gray-500 mt-1">
                                    <span>10px</span>
                                    <span>{borderSize}px</span>
                                    <span>60px</span>
                                </div>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Border Color
                                </label>
                                <input
                                    type="color"
                                    value={borderColor}
                                    onChange={(e) =>
                                        setBorderColor(e.target.value)
                                    }
                                    className="w-full h-10 rounded border border-gray-300 dark:border-gray-600"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Background Color
                                </label>
                                <input
                                    type="color"
                                    value={backgroundColor}
                                    onChange={(e) =>
                                        setBackgroundColor(e.target.value)
                                    }
                                    className="w-full h-10 rounded border border-gray-300 dark:border-gray-600"
                                />
                            </div>
                        </div>
                    </div>

                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                            Presets
                        </h2>
                        <div className="grid grid-cols-2 gap-2">
                            {PRESETS.map((preset, index) => (
                                <button
                                    key={index}
                                    className="p-2 text-left bg-white dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-600 rounded border border-gray-300 dark:border-gray-600 text-sm"
                                    onClick={() => loadPreset(preset)}>
                                    {preset.name}
                                </button>
                            ))}
                        </div>
                    </div>
                </div>

                {/* Preview and Code Panel */}
                <div className="space-y-6">
                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                            Preview
                        </h2>
                        <div className="w-full h-48 flex items-center justify-center bg-gray-100 dark:bg-gray-600 rounded-lg p-4">
                            <div
                                className="w-3/4 h-3/4 rounded-lg flex items-center justify-center relative overflow-hidden"
                                style={{
                                    backgroundColor: backgroundColor,
                                }}>
                                {/* Content */}
                                <div
                                    className="text-center font-medium z-10"
                                    style={{
                                        color: getContrastColor(
                                            backgroundColor
                                        ),
                                    }}>
                                    <div className="flex flex-col items-center">
                                        <span className="text-sm mb-1">
                                            {
                                                BORDER_TYPES.find(
                                                    (t) => t.id === borderType
                                                )?.name
                                            }{" "}
                                            Border
                                        </span>
                                        <span className="text-sm">
                                            {
                                                BORDER_POSITIONS.find(
                                                    (p) =>
                                                        p.id === borderPosition
                                                )?.name
                                            }{" "}
                                            Position
                                        </span>
                                    </div>
                                </div>

                                {/* Border mask element */}
                                <div
                                    className="absolute inset-0"
                                    style={{
                                        backgroundColor: borderColor,
                                        mask: generateExactMask(),
                                        WebkitMask: generateExactMask(),
                                        pointerEvents: "none",
                                    }}></div>
                            </div>
                        </div>
                    </div>

                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <div className="flex justify-between items-center mb-4">
                            <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
                                CSS Code
                            </h2>
                            <button
                                className="px-3 py-1 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors flex items-center"
                                onClick={copyToClipboard}>
                                <LucideIcon
                                    name={copied ? "Check" : "Copy"}
                                    size={16}
                                    className="mr-1"
                                />
                                {copied ? "Copied!" : "Copy Code"}
                            </button>
                        </div>
                        <pre className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-300 dark:border-gray-600 overflow-x-auto text-sm">
                            {generateCSS()}
                        </pre>
                    </div>
                </div>
            </div>
        </ToolContainer>
    );

    // Helper function to determine text color based on background color
    function getContrastColor(hexColor) {
        // Convert hex to RGB
        const r = parseInt(hexColor.substr(1, 2), 16);
        const g = parseInt(hexColor.substr(3, 2), 16);
        const b = parseInt(hexColor.substr(5, 2), 16);

        // Calculate luminance
        const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

        // Return black or white based on luminance
        return luminance > 0.5 ? "#000000" : "#FFFFFF";
    }
}
