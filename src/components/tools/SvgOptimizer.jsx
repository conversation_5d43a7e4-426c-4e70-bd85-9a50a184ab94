import { useState, useEffect } from "react";
import { getToolById } from "../../data/tools.js";
import ToolHeader from "../ui/ToolHeader.jsx";
import ToolContainer from "../ui/ToolContainer.jsx";

export default function SvgOptimizer() {
    const [toolData, setToolData] = useState(null);
    const [svgInput, setSvgInput] = useState("");
    const [optimizedSvg, setOptimizedSvg] = useState("");
    const [originalSize, setOriginalSize] = useState(0);
    const [optimizedSize, setOptimizedSize] = useState(0);
    const [savings, setSavings] = useState(0);
    const [error, setError] = useState("");
    const [copied, setCopied] = useState(false);

    // Options
    const [cleanViewBox, setCleanViewBox] = useState(true);
    const [removeMetadata, setRemoveMetadata] = useState(true);
    const [removeComments, setRemoveComments] = useState(true);
    const [removeEmptyAttrs, setRemoveEmptyAttrs] = useState(true);
    const [removeHiddenElems, setRemoveHiddenElems] = useState(true);
    const [removeEmptyText, setRemoveEmptyText] = useState(true);
    const [removeEmptyContainers, setRemoveEmptyContainers] = useState(true);
    const [removeUnusedDefs, setRemoveUnusedDefs] = useState(true);
    const [convertColors, setConvertColors] = useState(true);
    const [inlineStyles, setInlineStyles] = useState(false);
    const [minifyStyles, setMinifyStyles] = useState(true);
    const [roundPrecision, setRoundPrecision] = useState(3);

    useEffect(() => {
        // Get the tool data
        const data = getToolById("svgoptimizer");
        setToolData(data);
    }, []);

    // Sample SVG for demonstration
    const sampleSvg = `<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="210mm"
   height="297mm"
   viewBox="0 0 210.00000 297.00000"
   version="1.1"
   id="svg8"
   inkscape:version="0.92.3 (2405546, 2018-03-11)"
   sodipodi:docname="sample.svg">
  <defs
     id="defs2" />
  <sodipodi:namedview
     id="base"
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1.0"
     inkscape:pageopacity="0.0"
     inkscape:pageshadow="2"
     inkscape:zoom="0.35"
     inkscape:cx="400"
     inkscape:cy="560"
     inkscape:document-units="mm"
     inkscape:current-layer="layer1"
     showgrid="false"
     inkscape:window-width="1920"
     inkscape:window-height="1017"
     inkscape:window-x="-8"
     inkscape:window-y="-8"
     inkscape:window-maximized="1" />
  <metadata
     id="metadata5">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title></dc:title>
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <g
     inkscape:label="Layer 1"
     inkscape:groupmode="layer"
     id="layer1">
    <circle
       style="fill:#ff0000;fill-opacity:1;stroke:#000000;stroke-width:0.26458332"
       id="path10"
       cx="100"
       cy="100"
       r="50" />
    <rect
       style="fill:#0000ff;fill-opacity:1;stroke:#000000;stroke-width:0.26458332"
       id="rect12"
       width="80"
       height="40"
       x="60"
       y="170" />
    <path
       style="fill:none;stroke:#00ff00;stroke-width:2;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1;stroke-miterlimit:4;stroke-dasharray:none"
       d="M 30,30 H 180 L 105,80 Z"
       id="path14"
       inkscape:connector-curvature="0" />
    <!-- This is a comment that can be removed -->
    <text
       xml:space="preserve"
       style="font-style:normal;font-weight:normal;font-size:10.58333302px;line-height:1.25;font-family:sans-serif;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;stroke-width:0.26458332"
       x="70"
       y="150"
       id="text18"><tspan
         sodipodi:role="line"
         id="tspan16"
         x="70"
         y="150"
         style="stroke-width:0.26458332">Sample SVG</tspan></text>
    <g
       id="g20"
       style="opacity:0">
      <rect
         style="fill:#ff00ff;fill-opacity:1;stroke:none;stroke-width:0.26458332"
         id="rect22"
         width="10"
         height="10"
         x="10"
         y="10" />
    </g>
    <g
       id="g24">
      <!-- Empty group that can be removed -->
    </g>
  </g>
</svg>`;

    // Load sample SVG
    const loadSampleSvg = () => {
        setSvgInput(sampleSvg);
        setError("");
        calculateOriginalSize(sampleSvg);
    };

    // Calculate the size of the SVG string in bytes
    const calculateSize = (svgString) => {
        return new Blob([svgString]).size;
    };

    // Calculate the original size
    const calculateOriginalSize = (svg) => {
        const size = calculateSize(svg);
        setOriginalSize(size);
        return size;
    };

    // Calculate the optimized size and savings
    const calculateOptimizedSize = (optimizedSvg, originalSize) => {
        const size = calculateSize(optimizedSvg);
        setOptimizedSize(size);

        // Calculate savings percentage
        if (originalSize > 0) {
            const savingsPercent = ((originalSize - size) / originalSize) * 100;
            setSavings(Math.round(savingsPercent * 100) / 100);
        } else {
            setSavings(0);
        }

        return size;
    };

    // Copy to clipboard
    const copyToClipboard = () => {
        navigator.clipboard.writeText(optimizedSvg).then(() => {
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        });
    };

    // Clear all inputs and outputs
    const clearAll = () => {
        setSvgInput("");
        setOptimizedSvg("");
        setOriginalSize(0);
        setOptimizedSize(0);
        setSavings(0);
        setError("");
    };

    // Optimize SVG
    const optimizeSvg = () => {
        if (!svgInput) {
            setError("Please provide SVG input");
            return;
        }

        try {
            // Store original size
            const origSize = calculateOriginalSize(svgInput);

            // Create a DOM parser to work with the SVG
            const parser = new DOMParser();
            let svgDoc = parser.parseFromString(svgInput, "image/svg+xml");

            // Check for parsing errors
            const parserError = svgDoc.querySelector("parsererror");
            if (parserError) {
                setError("Invalid SVG: " + parserError.textContent);
                return;
            }

            // Get the root SVG element
            const svgElement = svgDoc.querySelector("svg");
            if (!svgElement) {
                setError("No SVG element found");
                return;
            }

            // 1. Clean ViewBox if enabled
            if (cleanViewBox) {
                cleanSvgViewBox(svgElement);
            }

            // 2. Remove metadata if enabled
            if (removeMetadata) {
                removeMetadataElements(svgDoc);
            }

            // 3. Remove comments if enabled
            if (removeComments) {
                removeCommentNodes(svgDoc);
            }

            // 4. Remove empty attributes if enabled
            if (removeEmptyAttrs) {
                removeEmptyAttributes(svgDoc);
            }

            // 5. Remove hidden elements if enabled
            if (removeHiddenElems) {
                removeHiddenElements(svgDoc);
            }

            // 6. Remove empty text nodes if enabled
            if (removeEmptyText) {
                removeEmptyTextNodes(svgDoc);
            }

            // 7. Remove empty containers if enabled
            if (removeEmptyContainers) {
                removeEmptyContainerElements(svgDoc);
            }

            // 8. Remove unused defs if enabled
            if (removeUnusedDefs) {
                removeUnusedDefinitions(svgDoc);
            }

            // 9. Convert colors if enabled
            if (convertColors) {
                convertColorValues(svgDoc);
            }

            // 10. Inline styles if enabled
            if (inlineStyles) {
                inlineStyleElements(svgDoc);
            }

            // 11. Minify styles if enabled
            if (minifyStyles) {
                minifyStyleAttributes(svgDoc);
            }

            // 12. Round numeric values to specified precision
            roundNumericValues(svgDoc, roundPrecision);

            // Convert the optimized SVG back to a string
            const serializer = new XMLSerializer();
            let optimizedSvgString = serializer.serializeToString(svgDoc);

            // Clean up the XML declaration and doctype
            optimizedSvgString = optimizedSvgString.replace(
                /^<\?xml.*?\?>\s*/,
                ""
            );

            // Set the optimized SVG
            setOptimizedSvg(optimizedSvgString);

            // Calculate the optimized size and savings
            calculateOptimizedSize(optimizedSvgString, origSize);

            // Clear any previous errors
            setError("");
        } catch (err) {
            console.error("Error optimizing SVG:", err);
            setError(`Error: ${err.message}`);
        }
    };

    // Clean SVG ViewBox
    const cleanSvgViewBox = (svgElement) => {
        // Get width, height, and viewBox attributes
        const width = svgElement.getAttribute("width");
        const height = svgElement.getAttribute("height");
        let viewBox = svgElement.getAttribute("viewBox");

        // If no viewBox but width and height exist, create one
        if (!viewBox && width && height) {
            viewBox = `0 0 ${width} ${height}`;
            svgElement.setAttribute("viewBox", viewBox);
        }

        // If viewBox exists, ensure it's properly formatted
        if (viewBox) {
            // Split the viewBox values
            const values = viewBox.split(/[\s,]+/).map(parseFloat);

            // If we have 4 valid numbers, format them properly
            if (values.length === 4 && !values.some(isNaN)) {
                // Round to the specified precision
                const formattedValues = values.map((val) => {
                    return Number(val.toFixed(roundPrecision));
                });

                // Set the cleaned viewBox
                svgElement.setAttribute("viewBox", formattedValues.join(" "));
            }
        }
    };

    // Remove metadata elements
    const removeMetadataElements = (svgDoc) => {
        // Remove metadata elements
        const metadataElements = svgDoc.querySelectorAll("metadata");
        metadataElements.forEach((el) => el.parentNode.removeChild(el));

        // Remove XML namespace declarations
        const svgElement = svgDoc.querySelector("svg");
        if (svgElement) {
            const attrs = svgElement.attributes;
            const attrsToRemove = [];

            for (let i = 0; i < attrs.length; i++) {
                const attr = attrs[i];
                if (
                    attr.name.startsWith("xmlns:") &&
                    !attr.name.startsWith("xmlns:xlink")
                ) {
                    attrsToRemove.push(attr.name);
                }
            }

            attrsToRemove.forEach((attr) => svgElement.removeAttribute(attr));
        }

        // Remove editor-specific elements - handle each namespace separately
        // since querySelectorAll doesn't support namespace selectors directly
        const namespaces = ["sodipodi", "inkscape", "rdf", "cc", "dc"];

        // Get all elements
        const allElements = svgDoc.querySelectorAll("*");
        const editorElements = [];

        // Filter elements that belong to editor namespaces
        allElements.forEach((el) => {
            const nodeName = el.nodeName.toLowerCase();

            // Check if the element belongs to any of the editor namespaces
            for (const ns of namespaces) {
                if (
                    nodeName.startsWith(ns + ":") ||
                    nodeName === "namedview" ||
                    nodeName === "rdf:rdf"
                ) {
                    editorElements.push(el);
                    break;
                }
            }
        });

        // Remove the identified editor elements
        editorElements.forEach((el) => {
            if (el.parentNode) el.parentNode.removeChild(el);
        });
    };

    // Remove comment nodes
    const removeCommentNodes = (svgDoc) => {
        const nodeIterator = document.createNodeIterator(
            svgDoc,
            NodeFilter.SHOW_COMMENT,
            null,
            false
        );

        const comments = [];
        let currentNode;

        while ((currentNode = nodeIterator.nextNode())) {
            comments.push(currentNode);
        }

        comments.forEach((comment) => {
            if (comment.parentNode) {
                comment.parentNode.removeChild(comment);
            }
        });
    };

    // Remove empty attributes
    const removeEmptyAttributes = (svgDoc) => {
        const allElements = svgDoc.querySelectorAll("*");

        allElements.forEach((el) => {
            const attrs = el.attributes;
            const attrsToRemove = [];

            for (let i = 0; i < attrs.length; i++) {
                const attr = attrs[i];
                if (
                    attr.value === "" ||
                    attr.value === "null" ||
                    attr.value === "undefined"
                ) {
                    attrsToRemove.push(attr.name);
                }
            }

            attrsToRemove.forEach((attr) => el.removeAttribute(attr));
        });
    };

    // Remove hidden elements
    const removeHiddenElements = (svgDoc) => {
        // Find elements with display:none, visibility:hidden, or opacity:0
        const hiddenElements = svgDoc.querySelectorAll(
            '[style*="display: none"], [style*="display:none"], ' +
                '[style*="visibility: hidden"], [style*="visibility:hidden"], ' +
                '[style*="opacity: 0"], [style*="opacity:0"], ' +
                '[display="none"], [visibility="hidden"], [opacity="0"]'
        );

        hiddenElements.forEach((el) => {
            if (el.parentNode) {
                el.parentNode.removeChild(el);
            }
        });
    };

    // Remove empty text nodes
    const removeEmptyTextNodes = (svgDoc) => {
        const walker = document.createTreeWalker(
            svgDoc,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );

        const emptyTextNodes = [];
        let node;

        while ((node = walker.nextNode())) {
            if (node.nodeValue.trim() === "") {
                emptyTextNodes.push(node);
            }
        }

        emptyTextNodes.forEach((node) => {
            if (node.parentNode) {
                node.parentNode.removeChild(node);
            }
        });
    };

    // Remove empty container elements
    const removeEmptyContainerElements = (svgDoc) => {
        // Get all group elements
        const containers = svgDoc.querySelectorAll(
            "g, defs, symbol, marker, mask, pattern, clipPath"
        );

        // Convert NodeList to Array and reverse it to start from the deepest elements
        Array.from(containers)
            .reverse()
            .forEach((container) => {
                // Check if the container has no children or only has empty text nodes
                let hasContent = false;

                for (let i = 0; i < container.childNodes.length; i++) {
                    const child = container.childNodes[i];

                    if (
                        child.nodeType === Node.ELEMENT_NODE ||
                        (child.nodeType === Node.TEXT_NODE &&
                            child.nodeValue.trim() !== "")
                    ) {
                        hasContent = true;
                        break;
                    }
                }

                // If the container is empty, remove it
                if (!hasContent && container.parentNode) {
                    container.parentNode.removeChild(container);
                }
            });
    };

    // Remove unused definitions
    const removeUnusedDefinitions = (svgDoc) => {
        // Get all elements in defs
        const defElements = svgDoc.querySelectorAll("defs > *");

        defElements.forEach((defEl) => {
            // Get the ID of the definition element
            const id = defEl.id || defEl.getAttribute("id");

            if (id) {
                // Check if this ID is referenced anywhere in the SVG
                const isReferenced = svgDoc.querySelector(
                    `[fill="url(#${id})"], [stroke="url(#${id})"], [filter="url(#${id})"], [mask="url(#${id})"], [clip-path="url(#${id})"], [marker-start="url(#${id})"], [marker-mid="url(#${id})"], [marker-end="url(#${id})"], use[*|href="#${id}"]`
                );

                // If not referenced, remove it
                if (!isReferenced && defEl.parentNode) {
                    defEl.parentNode.removeChild(defEl);
                }
            }
        });
    };

    // Convert color values
    const convertColorValues = (svgDoc) => {
        // Color name to hex mapping for common colors
        const colorMap = {
            red: "#ff0000",
            green: "#008000",
            blue: "#0000ff",
            yellow: "#ffff00",
            purple: "#800080",
            cyan: "#00ffff",
            magenta: "#ff00ff",
            black: "#000000",
            white: "#ffffff",
            gray: "#808080",
            grey: "#808080",
            orange: "#ffa500",
            pink: "#ffc0cb",
            brown: "#a52a2a",
            violet: "#ee82ee",
            indigo: "#4b0082",
            maroon: "#800000",
            navy: "#000080",
            olive: "#808000",
            silver: "#c0c0c0",
            teal: "#008080",
            transparent: "none",
        };

        // Get all elements with fill or stroke attributes
        const elements = svgDoc.querySelectorAll("[fill], [stroke]");

        elements.forEach((el) => {
            // Convert fill attribute
            if (el.hasAttribute("fill")) {
                const fill = el.getAttribute("fill").toLowerCase();

                // Convert named colors to hex
                if (colorMap[fill]) {
                    el.setAttribute("fill", colorMap[fill]);
                }
                // Convert rgb() to hex
                else if (fill.startsWith("rgb")) {
                    const rgb = fill.match(/\d+/g);
                    if (rgb && rgb.length >= 3) {
                        const hex = `#${parseInt(rgb[0])
                            .toString(16)
                            .padStart(2, "0")}${parseInt(rgb[1])
                            .toString(16)
                            .padStart(2, "0")}${parseInt(rgb[2])
                            .toString(16)
                            .padStart(2, "0")}`;
                        el.setAttribute("fill", hex);
                    }
                }
            }

            // Convert stroke attribute
            if (el.hasAttribute("stroke")) {
                const stroke = el.getAttribute("stroke").toLowerCase();

                // Convert named colors to hex
                if (colorMap[stroke]) {
                    el.setAttribute("stroke", colorMap[stroke]);
                }
                // Convert rgb() to hex
                else if (stroke.startsWith("rgb")) {
                    const rgb = stroke.match(/\d+/g);
                    if (rgb && rgb.length >= 3) {
                        const hex = `#${parseInt(rgb[0])
                            .toString(16)
                            .padStart(2, "0")}${parseInt(rgb[1])
                            .toString(16)
                            .padStart(2, "0")}${parseInt(rgb[2])
                            .toString(16)
                            .padStart(2, "0")}`;
                        el.setAttribute("stroke", hex);
                    }
                }
            }
        });
    };

    // Inline styles
    const inlineStyleElements = (svgDoc) => {
        // Get all style elements
        const styleElements = svgDoc.querySelectorAll("style");

        styleElements.forEach((styleEl) => {
            const cssText = styleEl.textContent;

            // Simple CSS parser (this is a basic implementation)
            const cssRules = cssText.match(/[^{}]+\{[^{}]+\}/g) || [];

            cssRules.forEach((rule) => {
                const selectorPart = rule
                    .substring(0, rule.indexOf("{"))
                    .trim();
                const stylePart = rule
                    .substring(rule.indexOf("{") + 1, rule.indexOf("}"))
                    .trim();

                // Get all elements matching the selector
                try {
                    const matchingElements =
                        svgDoc.querySelectorAll(selectorPart);

                    matchingElements.forEach((el) => {
                        // Get current style attribute or create a new one
                        let currentStyle = el.getAttribute("style") || "";

                        // Append new styles
                        if (currentStyle) {
                            currentStyle += "; " + stylePart;
                        } else {
                            currentStyle = stylePart;
                        }

                        // Set the updated style attribute
                        el.setAttribute("style", currentStyle);
                    });
                } catch (e) {
                    // Some complex selectors might not be supported by querySelectorAll
                    console.warn("Could not process selector:", selectorPart);
                }
            });

            // Remove the style element if inlining was successful
            if (cssRules.length > 0 && styleEl.parentNode) {
                styleEl.parentNode.removeChild(styleEl);
            }
        });
    };

    // Minify style attributes
    const minifyStyleAttributes = (svgDoc) => {
        // Get all elements with style attributes
        const elements = svgDoc.querySelectorAll("[style]");

        elements.forEach((el) => {
            const style = el.getAttribute("style");

            if (style) {
                // Remove whitespace around semicolons and colons
                const minifiedStyle = style
                    .replace(/\s*;\s*/g, ";")
                    .replace(/\s*:\s*/g, ":")
                    .replace(/;$/g, "")
                    .trim();

                el.setAttribute("style", minifiedStyle);
            }
        });
    };

    // Round numeric values to specified precision
    const roundNumericValues = (svgDoc, precision) => {
        // Get all elements with numeric attributes
        const elements = svgDoc.querySelectorAll("*");

        // Attributes that typically contain numeric values
        const numericAttrs = [
            "x",
            "y",
            "width",
            "height",
            "cx",
            "cy",
            "r",
            "rx",
            "ry",
            "x1",
            "y1",
            "x2",
            "y2",
            "points",
            "d",
            "transform",
        ];

        elements.forEach((el) => {
            numericAttrs.forEach((attr) => {
                if (el.hasAttribute(attr)) {
                    const value = el.getAttribute(attr);

                    // Round numbers in the attribute value
                    const roundedValue = value.replace(
                        /[-+]?[0-9]*\.?[0-9]+([eE][-+]?[0-9]+)?/g,
                        (match) => {
                            return Number(parseFloat(match).toFixed(precision));
                        }
                    );

                    el.setAttribute(attr, roundedValue);
                }
            });
        });
    };

    return (
        <ToolContainer
            className="py-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-colors duration-200"
            containerWidth={toolData?.containerWidth || "default"}>
            <ToolHeader
                toolData={toolData}
                defaultTitle="SVG Optimizer"
                defaultIcon="FileImage"
                defaultDescription="Optimize SVG files by cleaning viewbox, stripping metadata, and reducing file size for web use."
            />

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                {/* Input Section */}
                <div>
                    <div className="flex justify-between items-center mb-2">
                        <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
                            SVG Input
                        </h2>
                        <div className="flex space-x-2">
                            <button
                                className="px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-md text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                                onClick={loadSampleSvg}>
                                Load Sample
                            </button>
                            <button
                                className="px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-md text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                                onClick={clearAll}>
                                Clear
                            </button>
                        </div>
                    </div>
                    <div className="relative">
                        <textarea
                            className="w-full h-80 p-4 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200 font-mono text-sm resize-none focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                            value={svgInput}
                            onChange={(e) => {
                                setSvgInput(e.target.value);
                                calculateOriginalSize(e.target.value);
                            }}
                            placeholder="Paste your SVG code here..."></textarea>
                    </div>

                    {/* File Size Info */}
                    {originalSize > 0 && (
                        <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                            Original Size:{" "}
                            <span className="font-medium">
                                {originalSize} bytes
                            </span>
                        </div>
                    )}
                </div>

                {/* Options Section */}
                <div>
                    <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                        Optimization Options
                    </h2>

                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-4">
                        <h3 className="text-lg font-medium mb-2 text-gray-800 dark:text-white">
                            Basic Options
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <label className="flex items-center">
                                <input
                                    type="checkbox"
                                    checked={cleanViewBox}
                                    onChange={(e) =>
                                        setCleanViewBox(e.target.checked)
                                    }
                                    className="mr-2"
                                />
                                <span className="text-gray-700 dark:text-gray-300">
                                    Clean ViewBox
                                </span>
                            </label>
                            <label className="flex items-center">
                                <input
                                    type="checkbox"
                                    checked={removeMetadata}
                                    onChange={(e) =>
                                        setRemoveMetadata(e.target.checked)
                                    }
                                    className="mr-2"
                                />
                                <span className="text-gray-700 dark:text-gray-300">
                                    Remove Metadata
                                </span>
                            </label>
                            <label className="flex items-center">
                                <input
                                    type="checkbox"
                                    checked={removeComments}
                                    onChange={(e) =>
                                        setRemoveComments(e.target.checked)
                                    }
                                    className="mr-2"
                                />
                                <span className="text-gray-700 dark:text-gray-300">
                                    Remove Comments
                                </span>
                            </label>
                            <label className="flex items-center">
                                <input
                                    type="checkbox"
                                    checked={removeEmptyAttrs}
                                    onChange={(e) =>
                                        setRemoveEmptyAttrs(e.target.checked)
                                    }
                                    className="mr-2"
                                />
                                <span className="text-gray-700 dark:text-gray-300">
                                    Remove Empty Attributes
                                </span>
                            </label>
                        </div>
                    </div>

                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-4">
                        <h3 className="text-lg font-medium mb-2 text-gray-800 dark:text-white">
                            Advanced Options
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <label className="flex items-center">
                                <input
                                    type="checkbox"
                                    checked={removeHiddenElems}
                                    onChange={(e) =>
                                        setRemoveHiddenElems(e.target.checked)
                                    }
                                    className="mr-2"
                                />
                                <span className="text-gray-700 dark:text-gray-300">
                                    Remove Hidden Elements
                                </span>
                            </label>
                            <label className="flex items-center">
                                <input
                                    type="checkbox"
                                    checked={removeEmptyText}
                                    onChange={(e) =>
                                        setRemoveEmptyText(e.target.checked)
                                    }
                                    className="mr-2"
                                />
                                <span className="text-gray-700 dark:text-gray-300">
                                    Remove Empty Text
                                </span>
                            </label>
                            <label className="flex items-center">
                                <input
                                    type="checkbox"
                                    checked={removeEmptyContainers}
                                    onChange={(e) =>
                                        setRemoveEmptyContainers(
                                            e.target.checked
                                        )
                                    }
                                    className="mr-2"
                                />
                                <span className="text-gray-700 dark:text-gray-300">
                                    Remove Empty Containers
                                </span>
                            </label>
                            <label className="flex items-center">
                                <input
                                    type="checkbox"
                                    checked={removeUnusedDefs}
                                    onChange={(e) =>
                                        setRemoveUnusedDefs(e.target.checked)
                                    }
                                    className="mr-2"
                                />
                                <span className="text-gray-700 dark:text-gray-300">
                                    Remove Unused Defs
                                </span>
                            </label>
                        </div>
                    </div>

                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h3 className="text-lg font-medium mb-2 text-gray-800 dark:text-white">
                            Style Options
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-3">
                            <label className="flex items-center">
                                <input
                                    type="checkbox"
                                    checked={convertColors}
                                    onChange={(e) =>
                                        setConvertColors(e.target.checked)
                                    }
                                    className="mr-2"
                                />
                                <span className="text-gray-700 dark:text-gray-300">
                                    Convert Colors
                                </span>
                            </label>
                            <label className="flex items-center">
                                <input
                                    type="checkbox"
                                    checked={inlineStyles}
                                    onChange={(e) =>
                                        setInlineStyles(e.target.checked)
                                    }
                                    className="mr-2"
                                />
                                <span className="text-gray-700 dark:text-gray-300">
                                    Inline Styles
                                </span>
                            </label>
                            <label className="flex items-center">
                                <input
                                    type="checkbox"
                                    checked={minifyStyles}
                                    onChange={(e) =>
                                        setMinifyStyles(e.target.checked)
                                    }
                                    className="mr-2"
                                />
                                <span className="text-gray-700 dark:text-gray-300">
                                    Minify Styles
                                </span>
                            </label>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Precision (decimal places): {roundPrecision}
                            </label>
                            <input
                                type="range"
                                min="0"
                                max="5"
                                step="1"
                                value={roundPrecision}
                                onChange={(e) =>
                                    setRoundPrecision(parseInt(e.target.value))
                                }
                                className="w-full"
                            />
                        </div>
                    </div>
                </div>
            </div>

            {/* Action Button */}
            <div className="flex justify-center mb-8">
                <button
                    className="px-6 py-3 bg-primary dark:bg-primary-dark text-white rounded-lg hover:bg-primary-dark dark:hover:bg-primary transition-colors"
                    onClick={optimizeSvg}
                    disabled={!svgInput}>
                    Optimize SVG
                </button>
            </div>

            {/* Error Message */}
            {error && (
                <div className="mb-8 p-4 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-lg">
                    {error}
                </div>
            )}

            {/* Results Section - Will be implemented in Part 2 */}
            {optimizedSvg && (
                <div className="mb-8">
                    <h2 className="text-2xl font-semibold text-gray-800 dark:text-white mb-4">
                        Optimized SVG
                    </h2>

                    {/* File Size Comparison */}
                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-4">
                        <div className="flex flex-wrap justify-between items-center">
                            <div className="mb-2 md:mb-0">
                                <span className="text-gray-700 dark:text-gray-300">
                                    Original Size:{" "}
                                </span>
                                <span className="font-medium">
                                    {originalSize} bytes
                                </span>
                            </div>
                            <div className="mb-2 md:mb-0">
                                <span className="text-gray-700 dark:text-gray-300">
                                    Optimized Size:{" "}
                                </span>
                                <span className="font-medium">
                                    {optimizedSize} bytes
                                </span>
                            </div>
                            <div className="mb-2 md:mb-0">
                                <span className="text-gray-700 dark:text-gray-300">
                                    Savings:{" "}
                                </span>
                                <span className="font-medium text-green-600 dark:text-green-400">
                                    {savings}%
                                </span>
                            </div>
                        </div>
                    </div>

                    {/* SVG Output */}
                    <div className="relative">
                        <pre className="w-full h-80 p-4 overflow-auto border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-200 font-mono text-sm">
                            {optimizedSvg}
                        </pre>
                        <button
                            className="absolute top-2 right-2 px-2 py-1 bg-primary dark:bg-primary-dark text-white text-xs rounded hover:bg-primary-dark dark:hover:bg-primary transition-colors"
                            onClick={copyToClipboard}>
                            {copied ? "Copied!" : "Copy"}
                        </button>
                    </div>
                </div>
            )}

            {/* Information Section */}
            <div className="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg">
                <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                    About SVG Optimization
                </h2>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                    SVG optimization is the process of removing unnecessary data
                    from SVG files to reduce their file size while maintaining
                    visual quality. This can significantly improve page load
                    times and performance, especially for websites with multiple
                    SVG graphics.
                </p>
                <ul className="list-disc pl-6 text-gray-600 dark:text-gray-400 mb-4 space-y-2">
                    <li>
                        <strong>Clean ViewBox</strong> - Normalizes and
                        simplifies the viewBox attribute for better browser
                        compatibility.
                    </li>
                    <li>
                        <strong>Metadata Stripping</strong> - Removes
                        non-essential metadata like editor information,
                        comments, and XML declarations.
                    </li>
                    <li>
                        <strong>File Size Reduction</strong> - Reduces file size
                        by removing unnecessary attributes, whitespace, and
                        precision in decimal values.
                    </li>
                </ul>
                <p className="text-gray-600 dark:text-gray-400">
                    For best results, use this tool on SVGs that you have the
                    rights to modify, and always keep a backup of your original
                    files.
                </p>
            </div>
        </ToolContainer>
    );
}
