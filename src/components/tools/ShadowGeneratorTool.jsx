import { useState, useEffect } from "react";
import { getToolById } from "../../data/tools.js";
// Use a simpler color picker approach
import { HexColorPicker } from "react-colorful";
import ToolHeader from "../ui/ToolHeader.jsx";
import LucideIcon from "../ui/LucideIcon.jsx";
import ToolContainer from "../ui/ToolContainer.jsx";

// Preset shadows
const PRESETS = {
    material: [
        {
            name: "Material 1",
            x: 0,
            y: 2,
            blur: 1,
            spread: -1,
            color: "rgba(0,0,0,0.2)",
            inset: false,
        },
        {
            name: "Material 2",
            x: 0,
            y: 3,
            blur: 3,
            spread: -2,
            color: "rgba(0,0,0,0.3)",
            inset: false,
        },
        {
            name: "Material 3",
            x: 0,
            y: 3,
            blur: 4,
            spread: -2,
            color: "rgba(0,0,0,0.4)",
            inset: false,
        },
        {
            name: "Material 4",
            x: 0,
            y: 2,
            blur: 4,
            spread: -1,
            color: "rgba(0,0,0,0.2)",
            inset: false,
        },
    ],
    ios: [
        {
            name: "iOS",
            x: 0,
            y: 3,
            blur: 10,
            spread: 0,
            color: "rgba(0,0,0,0.1)",
            inset: false,
        },
    ],
    neumorphism: [
        {
            name: "Neumorphism Light",
            x: -5,
            y: -5,
            blur: 10,
            spread: 0,
            color: "rgba(255,255,255,0.5)",
            inset: false,
        },
        {
            name: "Neumorphism Dark",
            x: 5,
            y: 5,
            blur: 10,
            spread: 0,
            color: "rgba(0,0,0,0.3)",
            inset: false,
        },
    ],
};

export default function ShadowGeneratorTool() {
    const [toolData, setToolData] = useState(null);
    const [shadowLayers, setShadowLayers] = useState([
        {
            id: 1,
            x: 0,
            y: 4,
            blur: 8,
            spread: 0,
            color: "rgba(0,0,0,0.2)",
            inset: false,
        },
    ]);
    const [activeLayer, setActiveLayer] = useState(1);
    const [previewBgColor, setPreviewBgColor] = useState("#ffffff");
    const [previewElementColor, setPreviewElementColor] = useState("#ffffff");
    const [outputFormat, setOutputFormat] = useState("css");
    const [activeColorPicker, setActiveColorPicker] = useState(null);
    const [copied, setCopied] = useState(false);

    useEffect(() => {
        // Get the tool data
        const data = getToolById("shadowgenerator");
        setToolData(data);
    }, []);

    // Get the active layer
    const getActiveLayer = () => {
        return (
            shadowLayers.find((layer) => layer.id === activeLayer) ||
            shadowLayers[0]
        );
    };

    // Update a property of the active layer
    const updateActiveLayer = (property, value) => {
        setShadowLayers(
            shadowLayers.map((layer) =>
                layer.id === activeLayer
                    ? { ...layer, [property]: value }
                    : layer
            )
        );
    };

    // Add a new layer
    const addLayer = () => {
        const newId = Math.max(...shadowLayers.map((layer) => layer.id), 0) + 1;
        const newLayer = {
            id: newId,
            x: 0,
            y: 4,
            blur: 8,
            spread: 0,
            color: "rgba(0,0,0,0.2)",
            inset: false,
        };
        setShadowLayers([...shadowLayers, newLayer]);
        setActiveLayer(newId);
    };

    // Remove the active layer
    const removeLayer = (id) => {
        if (shadowLayers.length <= 1) return; // Don't remove the last layer

        const newLayers = shadowLayers.filter((layer) => layer.id !== id);
        setShadowLayers(newLayers);
        setActiveLayer(newLayers[0].id);
    };

    // Apply a preset
    const applyPreset = (presetName) => {
        const preset = PRESETS[presetName];
        if (!preset) return;

        setShadowLayers(
            preset.map((layer, index) => ({
                ...layer,
                id: index + 1,
            }))
        );
        setActiveLayer(1);
    };

    // Generate CSS box-shadow
    const generateBoxShadow = () => {
        return shadowLayers
            .map((layer) => {
                const { x, y, blur, spread, color, inset } = layer;
                return `${
                    inset ? "inset " : ""
                }${x}px ${y}px ${blur}px ${spread}px ${color}`;
            })
            .join(", ");
    };

    // Generate Tailwind CSS
    const generateTailwind = () => {
        // This is a simplified version - Tailwind doesn't directly support multiple shadows
        // or all the customizations we offer
        const layer = getActiveLayer();
        const { x, y, blur, spread, color } = layer;

        // Use string concatenation instead of template literals to avoid CSS minification warnings
        return (
            "shadow-[" +
            x +
            "px_" +
            y +
            "px_" +
            blur +
            "px_" +
            spread +
            "px_" +
            color +
            "]"
        );
    };

    // Generate JavaScript object
    const generateJS = () => {
        return `boxShadow: "${generateBoxShadow()}"`;
    };

    // Get the output code based on selected format
    const getOutputCode = () => {
        switch (outputFormat) {
            case "css":
                return `box-shadow: ${generateBoxShadow()};`;
            case "tailwind":
                return generateTailwind();
            case "js":
                return generateJS();
            default:
                return generateBoxShadow();
        }
    };

    // Copy to clipboard
    const copyToClipboard = () => {
        navigator.clipboard.writeText(getOutputCode()).then(() => {
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        });
    };

    // Preview style
    const previewStyle = {
        backgroundColor: previewElementColor,
        boxShadow: generateBoxShadow(),
    };

    return (
        <ToolContainer
            className="py-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-colors duration-200"
            containerWidth={toolData?.containerWidth || "default"}>
            <ToolHeader
                toolData={toolData}
                defaultTitle="CSS Shadow Generator"
                defaultIcon="Square"
                defaultDescription="Create beautiful CSS shadows with our visual editor. Adjust X/Y offset, blur, spread, and color with intuitive controls."
            />

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Preview Section */}
                <div>
                    <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                        Preview
                    </h2>
                    <div
                        className="w-full h-64 rounded-lg flex items-center justify-center p-4 mb-6 transition-all"
                        style={{ backgroundColor: previewBgColor }}>
                        <div
                            className="w-32 h-32 rounded-lg transition-all"
                            style={previewStyle}></div>
                    </div>

                    <div className="flex flex-wrap gap-4 mb-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Background Color
                            </label>
                            <div
                                className="w-10 h-10 rounded cursor-pointer border border-gray-300 dark:border-gray-600"
                                style={{ backgroundColor: previewBgColor }}
                                onClick={() =>
                                    setActiveColorPicker(
                                        activeColorPicker === "bg" ? null : "bg"
                                    )
                                }></div>
                            {activeColorPicker === "bg" && (
                                <div className="mt-2 p-2 bg-white dark:bg-gray-800 rounded shadow-lg">
                                    <HexColorPicker
                                        color={previewBgColor}
                                        onChange={setPreviewBgColor}
                                    />
                                    <div className="mt-2 flex justify-between">
                                        <span className="text-xs">
                                            {previewBgColor}
                                        </span>
                                        <button
                                            className="text-xs text-primary dark:text-primary-dark"
                                            onClick={() =>
                                                setActiveColorPicker(null)
                                            }>
                                            Close
                                        </button>
                                    </div>
                                </div>
                            )}
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Element Color
                            </label>
                            <div
                                className="w-10 h-10 rounded cursor-pointer border border-gray-300 dark:border-gray-600"
                                style={{ backgroundColor: previewElementColor }}
                                onClick={() =>
                                    setActiveColorPicker(
                                        activeColorPicker === "element"
                                            ? null
                                            : "element"
                                    )
                                }></div>
                            {activeColorPicker === "element" && (
                                <div className="mt-2 p-2 bg-white dark:bg-gray-800 rounded shadow-lg">
                                    <HexColorPicker
                                        color={previewElementColor}
                                        onChange={setPreviewElementColor}
                                    />
                                    <div className="mt-2 flex justify-between">
                                        <span className="text-xs">
                                            {previewElementColor}
                                        </span>
                                        <button
                                            className="text-xs text-primary dark:text-primary-dark"
                                            onClick={() =>
                                                setActiveColorPicker(null)
                                            }>
                                            Close
                                        </button>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>

                    <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-2">
                            Presets
                        </h3>
                        <div className="flex flex-wrap gap-2">
                            <button
                                className="px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-md text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                                onClick={() => applyPreset("material")}>
                                Material Design
                            </button>
                            <button
                                className="px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-md text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                                onClick={() => applyPreset("ios")}>
                                iOS
                            </button>
                            <button
                                className="px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-md text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                                onClick={() => applyPreset("neumorphism")}>
                                Neumorphism
                            </button>
                        </div>
                    </div>
                </div>

                {/* Controls Section */}
                <div>
                    <div className="flex justify-between items-center mb-4">
                        <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
                            Shadow Layers
                        </h2>
                        <button
                            className="px-3 py-1 bg-primary dark:bg-primary-dark text-white rounded-md text-sm hover:bg-primary-dark dark:hover:bg-primary transition-colors"
                            onClick={addLayer}>
                            Add Layer
                        </button>
                    </div>

                    {/* Layer Tabs */}
                    <div className="flex flex-wrap gap-2 mb-4">
                        {shadowLayers.map((layer) => (
                            <div
                                key={layer.id}
                                className={`flex items-center px-3 py-1 rounded-md text-sm cursor-pointer ${
                                    layer.id === activeLayer
                                        ? "bg-primary dark:bg-primary-dark text-white"
                                        : "bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600"
                                }`}
                                onClick={() => setActiveLayer(layer.id)}>
                                Layer {layer.id}
                                {shadowLayers.length > 1 && (
                                    <button
                                        className="ml-2 text-xs hover:text-red-500 dark:hover:text-red-400"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            removeLayer(layer.id);
                                        }}>
                                        ×
                                    </button>
                                )}
                            </div>
                        ))}
                    </div>

                    {/* Active Layer Controls */}
                    {getActiveLayer() && (
                        <div className="space-y-4 mb-6">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    X Offset: {getActiveLayer().x}px
                                </label>
                                <input
                                    type="range"
                                    min="-50"
                                    max="50"
                                    value={getActiveLayer().x}
                                    onChange={(e) =>
                                        updateActiveLayer(
                                            "x",
                                            parseInt(e.target.value)
                                        )
                                    }
                                    className="w-full"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Y Offset: {getActiveLayer().y}px
                                </label>
                                <input
                                    type="range"
                                    min="-50"
                                    max="50"
                                    value={getActiveLayer().y}
                                    onChange={(e) =>
                                        updateActiveLayer(
                                            "y",
                                            parseInt(e.target.value)
                                        )
                                    }
                                    className="w-full"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Blur: {getActiveLayer().blur}px
                                </label>
                                <input
                                    type="range"
                                    min="0"
                                    max="100"
                                    value={getActiveLayer().blur}
                                    onChange={(e) =>
                                        updateActiveLayer(
                                            "blur",
                                            parseInt(e.target.value)
                                        )
                                    }
                                    className="w-full"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Spread: {getActiveLayer().spread}px
                                </label>
                                <input
                                    type="range"
                                    min="-50"
                                    max="50"
                                    value={getActiveLayer().spread}
                                    onChange={(e) =>
                                        updateActiveLayer(
                                            "spread",
                                            parseInt(e.target.value)
                                        )
                                    }
                                    className="w-full"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Shadow Color
                                </label>
                                <div
                                    className="w-full h-10 rounded cursor-pointer border border-gray-300 dark:border-gray-600 flex items-center px-3"
                                    onClick={() =>
                                        setActiveColorPicker(
                                            activeColorPicker === "shadow"
                                                ? null
                                                : "shadow"
                                        )
                                    }>
                                    <div
                                        className="w-6 h-6 rounded mr-2"
                                        style={{
                                            backgroundColor:
                                                getActiveLayer().color,
                                        }}></div>
                                    <span className="text-sm">
                                        {getActiveLayer().color}
                                    </span>
                                </div>
                                {activeColorPicker === "shadow" && (
                                    <div className="mt-2 p-2 bg-white dark:bg-gray-800 rounded shadow-lg">
                                        <HexColorPicker
                                            color={getActiveLayer().color}
                                            onChange={(color) => {
                                                // Convert hex to rgba with 0.5 opacity as default
                                                const hexColor = color.replace(
                                                    "#",
                                                    ""
                                                );
                                                const r = parseInt(
                                                    hexColor.substring(0, 2),
                                                    16
                                                );
                                                const g = parseInt(
                                                    hexColor.substring(2, 4),
                                                    16
                                                );
                                                const b = parseInt(
                                                    hexColor.substring(4, 6),
                                                    16
                                                );
                                                const rgba = `rgba(${r}, ${g}, ${b}, 0.5)`;
                                                updateActiveLayer(
                                                    "color",
                                                    rgba
                                                );
                                            }}
                                        />
                                        <div className="mt-2 flex justify-between">
                                            <span className="text-xs">
                                                {getActiveLayer().color}
                                            </span>
                                            <button
                                                className="text-xs text-primary dark:text-primary-dark"
                                                onClick={() =>
                                                    setActiveColorPicker(null)
                                                }>
                                                Close
                                            </button>
                                        </div>
                                    </div>
                                )}
                            </div>
                            <div className="flex items-center">
                                <input
                                    type="checkbox"
                                    id="inset"
                                    checked={getActiveLayer().inset}
                                    onChange={(e) =>
                                        updateActiveLayer(
                                            "inset",
                                            e.target.checked
                                        )
                                    }
                                    className="mr-2"
                                />
                                <label
                                    htmlFor="inset"
                                    className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Inset Shadow
                                </label>
                            </div>
                        </div>
                    )}

                    {/* Output Section */}
                    <div>
                        <div className="flex justify-between items-center mb-2">
                            <h3 className="text-lg font-medium text-gray-800 dark:text-white">
                                Generated Code
                            </h3>
                            <div className="flex gap-2">
                                <select
                                    value={outputFormat}
                                    onChange={(e) =>
                                        setOutputFormat(e.target.value)
                                    }
                                    className="text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 px-2 py-1">
                                    <option value="css">CSS</option>
                                    <option value="tailwind">Tailwind</option>
                                    <option value="js">JavaScript</option>
                                </select>
                                <button
                                    className="px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-md text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                                    onClick={copyToClipboard}>
                                    {copied ? "Copied!" : "Copy"}
                                </button>
                            </div>
                        </div>
                        <div className="bg-gray-100 dark:bg-gray-700 rounded-md p-4 overflow-x-auto">
                            <pre className="text-sm text-gray-800 dark:text-gray-200 font-mono">
                                {getOutputCode()}
                            </pre>
                        </div>
                    </div>
                </div>
            </div>
        </ToolContainer>
    );
}
