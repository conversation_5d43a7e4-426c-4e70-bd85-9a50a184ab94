// This is a temporary file to test the wavy border pattern
const wavyBorderCase = `
case "wavy":
    if (borderSides === "top" || borderSides === "bottom") {
        // Horizontal wavy - completely redesigned for smooth continuous waves
        const waveWidth = size * 8; // Wider to show more waves
        const waveHeight = size;
        viewBox = \`0 0 \${waveWidth} \${waveHeight}\`;
        preserveAspectRatio = "none";
        
        // Create a continuous wave pattern
        let wavePath = "";
        const numWaves = 4; // Number of complete waves
        const waveSegmentWidth = waveWidth / numWaves;
        
        if (borderSides === "top") {
            // Start at bottom left for top border
            wavePath = \`M0,\${waveHeight}\`;
            
            // Add each wave segment
            for (let i = 0; i < numWaves; i++) {
                const startX = i * waveSegmentWidth;
                const quarterX = startX + (waveSegmentWidth / 4);
                const halfX = startX + (waveSegmentWidth / 2);
                const threeQuarterX = startX + (waveSegmentWidth * 3/4);
                const endX = startX + waveSegmentWidth;
                
                // Create a smooth sine-like wave using cubic bezier curves
                wavePath += \` C\${quarterX},\${waveHeight} \${quarterX},0 \${halfX},0\`;
                wavePath += \` C\${threeQuarterX},0 \${threeQuarterX},\${waveHeight} \${endX},\${waveHeight}\`;
            }
            
            // Close the path for filling
            wavePath += \` L0,\${waveHeight} Z\`;
        } else {
            // Start at top left for bottom border
            wavePath = \`M0,0\`;
            
            // Add each wave segment
            for (let i = 0; i < numWaves; i++) {
                const startX = i * waveSegmentWidth;
                const quarterX = startX + (waveSegmentWidth / 4);
                const halfX = startX + (waveSegmentWidth / 2);
                const threeQuarterX = startX + (waveSegmentWidth * 3/4);
                const endX = startX + waveSegmentWidth;
                
                // Create a smooth sine-like wave using cubic bezier curves
                wavePath += \` C\${quarterX},0 \${quarterX},\${waveHeight} \${halfX},\${waveHeight}\`;
                wavePath += \` C\${threeQuarterX},\${waveHeight} \${threeQuarterX},0 \${endX},0\`;
            }
            
            // Close the path for filling
            wavePath += \` L0,0 Z\`;
        }
        
        path = wavePath;
    } else {
        // Vertical wavy - completely redesigned for smooth continuous waves
        const waveWidth = size;
        const waveHeight = size * 8; // Taller to show more waves
        viewBox = \`0 0 \${waveWidth} \${waveHeight}\`;
        preserveAspectRatio = "none";
        
        // Create a continuous wave pattern
        let wavePath = "";
        const numWaves = 4; // Number of complete waves
        const waveSegmentHeight = waveHeight / numWaves;
        
        if (borderSides === "left") {
            // Start at top right for left border
            wavePath = \`M\${waveWidth},0\`;
            
            // Add each wave segment
            for (let i = 0; i < numWaves; i++) {
                const startY = i * waveSegmentHeight;
                const quarterY = startY + (waveSegmentHeight / 4);
                const halfY = startY + (waveSegmentHeight / 2);
                const threeQuarterY = startY + (waveSegmentHeight * 3/4);
                const endY = startY + waveSegmentHeight;
                
                // Create a smooth sine-like wave using cubic bezier curves
                wavePath += \` C\${waveWidth},\${quarterY} 0,\${quarterY} 0,\${halfY}\`;
                wavePath += \` C0,\${threeQuarterY} \${waveWidth},\${threeQuarterY} \${waveWidth},\${endY}\`;
            }
            
            // Close the path for filling
            wavePath += \` L\${waveWidth},0 Z\`;
        } else {
            // Start at top left for right border
            wavePath = \`M0,0\`;
            
            // Add each wave segment
            for (let i = 0; i < numWaves; i++) {
                const startY = i * waveSegmentHeight;
                const quarterY = startY + (waveSegmentHeight / 4);
                const halfY = startY + (waveSegmentHeight / 2);
                const threeQuarterY = startY + (waveSegmentHeight * 3/4);
                const endY = startY + waveSegmentHeight;
                
                // Create a smooth sine-like wave using cubic bezier curves
                wavePath += \` C0,\${quarterY} \${waveWidth},\${quarterY} \${waveWidth},\${halfY}\`;
                wavePath += \` C\${waveWidth},\${threeQuarterY} 0,\${threeQuarterY} 0,\${endY}\`;
            }
            
            // Close the path for filling
            wavePath += \` L0,0 Z\`;
        }
        
        path = wavePath;
    }
    break;
`;
