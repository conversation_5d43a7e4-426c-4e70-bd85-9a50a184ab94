import React, { useState, useEffect } from "react";
import { getToolById } from "../../data/tools.js";
import ToolHeader from "../ui/ToolHeader.jsx";
import ToolContainer from "../ui/ToolContainer.jsx";
import LucideIcon from "../ui/LucideIcon.jsx";

export default function BorderGeneratorNew() {
  const [toolData, setToolData] = useState(null);
  const [borderType, setBorderType] = useState("wavy");
  const [borderPosition, setBorderPosition] = useState("bottom");
  const [borderSize, setBorderSize] = useState(15);
  const [borderColor, setBorderColor] = useState("#4F46E5");
  const [backgroundColor, setBackgroundColor] = useState("#FFFFFF");
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    // Get the tool data
    const data = getToolById("bordergenerator");
    setToolData(data);
  }, []);

  // Border types based on the reference site
  const BORDER_TYPES = [
    { id: "wavy", name: "Wavy" },
    { id: "zigzag", name: "Zig Zag" },
    { id: "rounded", name: "Rounded" },
    { id: "diagonal", name: "Diagonal" }
  ];

  // Border positions
  const BORDER_POSITIONS = [
    { id: "bottom", name: "Bottom" },
    { id: "top", name: "Top" },
    { id: "left", name: "Left" },
    { id: "right", name: "Right" }
  ];

  // Generate CSS for the selected border type and position
  const generateCSS = () => {
    let css = '';
    const isHorizontal = borderPosition === 'top' || borderPosition === 'bottom';
    
    switch (borderType) {
      case 'wavy':
        if (isHorizontal) {
          css = `
.element {
  position: relative;
  background-color: ${backgroundColor};
  padding-${borderPosition}: ${borderSize + 10}px;
}

.element::before {
  content: "";
  position: absolute;
  left: 0;
  ${borderPosition}: 0;
  height: ${borderSize}px;
  width: 100%;
  background-image: 
    repeating-radial-gradient(
      ellipse at ${borderPosition === 'top' ? 'top' : 'bottom'},
      transparent 0,
      transparent ${borderSize * 0.5}px,
      ${borderColor} ${borderSize * 0.5}px,
      ${borderColor} ${borderSize}px
    );
  background-size: ${borderSize * 2}px ${borderSize}px;
  background-repeat: repeat-x;
}`;
        } else {
          css = `
.element {
  position: relative;
  background-color: ${backgroundColor};
  padding-${borderPosition}: ${borderSize + 10}px;
}

.element::before {
  content: "";
  position: absolute;
  top: 0;
  ${borderPosition}: 0;
  width: ${borderSize}px;
  height: 100%;
  background-image: 
    repeating-radial-gradient(
      ellipse at ${borderPosition === 'left' ? 'left' : 'right'},
      transparent 0,
      transparent ${borderSize * 0.5}px,
      ${borderColor} ${borderSize * 0.5}px,
      ${borderColor} ${borderSize}px
    );
  background-size: ${borderSize}px ${borderSize * 2}px;
  background-repeat: repeat-y;
}`;
        }
        break;
        
      case 'zigzag':
        if (isHorizontal) {
          css = `
.element {
  position: relative;
  background-color: ${backgroundColor};
  padding-${borderPosition}: ${borderSize + 10}px;
}

.element::before {
  content: "";
  position: absolute;
  left: 0;
  ${borderPosition}: 0;
  height: ${borderSize}px;
  width: 100%;
  background-image: 
    linear-gradient(
      ${borderPosition === 'top' ? '135deg' : '45deg'},
      ${borderColor} 25%,
      transparent 25%
    ),
    linear-gradient(
      ${borderPosition === 'top' ? '45deg' : '135deg'},
      ${borderColor} 25%,
      transparent 25%
    );
  background-size: ${borderSize * 2}px ${borderSize}px;
  background-position: 0 0, ${borderSize}px 0;
  background-repeat: repeat-x;
}`;
        } else {
          css = `
.element {
  position: relative;
  background-color: ${backgroundColor};
  padding-${borderPosition}: ${borderSize + 10}px;
}

.element::before {
  content: "";
  position: absolute;
  top: 0;
  ${borderPosition}: 0;
  width: ${borderSize}px;
  height: 100%;
  background-image: 
    linear-gradient(
      ${borderPosition === 'left' ? '45deg' : '135deg'},
      ${borderColor} 25%,
      transparent 25%
    ),
    linear-gradient(
      ${borderPosition === 'left' ? '135deg' : '45deg'},
      ${borderColor} 25%,
      transparent 25%
    );
  background-size: ${borderSize}px ${borderSize * 2}px;
  background-position: 0 0, 0 ${borderSize}px;
  background-repeat: repeat-y;
}`;
        }
        break;
        
      case 'rounded':
        if (isHorizontal) {
          css = `
.element {
  position: relative;
  background-color: ${backgroundColor};
  padding-${borderPosition}: ${borderSize + 10}px;
}

.element::before {
  content: "";
  position: absolute;
  left: 0;
  ${borderPosition}: 0;
  height: ${borderSize}px;
  width: 100%;
  background-image: 
    radial-gradient(
      circle at ${borderSize / 2}px ${borderPosition === 'top' ? '0' : borderSize}px,
      transparent ${borderSize / 2}px,
      ${borderColor} ${borderSize / 2}px
    );
  background-size: ${borderSize}px ${borderSize}px;
  background-repeat: repeat-x;
}`;
        } else {
          css = `
.element {
  position: relative;
  background-color: ${backgroundColor};
  padding-${borderPosition}: ${borderSize + 10}px;
}

.element::before {
  content: "";
  position: absolute;
  top: 0;
  ${borderPosition}: 0;
  width: ${borderSize}px;
  height: 100%;
  background-image: 
    radial-gradient(
      circle at ${borderPosition === 'left' ? '0' : borderSize}px ${borderSize / 2}px,
      transparent ${borderSize / 2}px,
      ${borderColor} ${borderSize / 2}px
    );
  background-size: ${borderSize}px ${borderSize}px;
  background-repeat: repeat-y;
}`;
        }
        break;
        
      case 'diagonal':
        if (isHorizontal) {
          css = `
.element {
  position: relative;
  background-color: ${backgroundColor};
  padding-${borderPosition}: ${borderSize + 10}px;
}

.element::before {
  content: "";
  position: absolute;
  left: 0;
  ${borderPosition}: 0;
  height: ${borderSize}px;
  width: 100%;
  background-image: 
    linear-gradient(
      ${borderPosition === 'top' ? '135deg' : '45deg'},
      transparent 50%,
      ${borderColor} 50%
    );
  background-size: ${borderSize}px ${borderSize}px;
  background-repeat: repeat-x;
}`;
        } else {
          css = `
.element {
  position: relative;
  background-color: ${backgroundColor};
  padding-${borderPosition}: ${borderSize + 10}px;
}

.element::before {
  content: "";
  position: absolute;
  top: 0;
  ${borderPosition}: 0;
  width: ${borderSize}px;
  height: 100%;
  background-image: 
    linear-gradient(
      ${borderPosition === 'left' ? '45deg' : '135deg'},
      transparent 50%,
      ${borderColor} 50%
    );
  background-size: ${borderSize}px ${borderSize}px;
  background-repeat: repeat-y;
}`;
        }
        break;
        
      default:
        css = `/* Select a border type */`;
    }
    
    return css;
  };

  // Copy CSS to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(generateCSS()).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };

  // Sample presets
  const PRESETS = [
    {
      name: "Wavy Bottom",
      type: "wavy",
      position: "bottom",
      size: 15,
      color: "#4F46E5",
      bgColor: "#FFFFFF"
    },
    {
      name: "Zigzag Top",
      type: "zigzag",
      position: "top",
      size: 10,
      color: "#EF4444",
      bgColor: "#FFFFFF"
    },
    {
      name: "Rounded Left",
      type: "rounded",
      position: "left",
      size: 12,
      color: "#10B981",
      bgColor: "#FFFFFF"
    },
    {
      name: "Diagonal Right",
      type: "diagonal",
      position: "right",
      size: 8,
      color: "#F59E0B",
      bgColor: "#FFFFFF"
    }
  ];

  // Load a preset
  const loadPreset = (preset) => {
    setBorderType(preset.type);
    setBorderPosition(preset.position);
    setBorderSize(preset.size);
    setBorderColor(preset.color);
    setBackgroundColor(preset.bgColor);
  };

  return (
    <ToolContainer
      className="py-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-colors duration-200"
      containerWidth={toolData?.containerWidth || "default"}>
      <ToolHeader
        toolData={toolData}
        defaultTitle="Border Generator"
        defaultIcon="Square"
        defaultDescription="Create custom CSS borders with different shapes like wavy, zigzag, rounded, or diagonal. Customize the size, position, and get the CSS code."
      />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Controls Panel */}
        <div className="space-y-6">
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
              Border Type
            </h2>
            <div className="grid grid-cols-2 gap-3">
              {BORDER_TYPES.map((type) => (
                <button
                  key={type.id}
                  className={`p-3 rounded-lg border ${
                    borderType === type.id
                      ? "border-primary dark:border-primary-dark bg-primary/10 dark:bg-primary-dark/10"
                      : "border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600"
                  }`}
                  onClick={() => setBorderType(type.id)}>
                  <span className="text-sm font-medium">
                    {type.name}
                  </span>
                </button>
              ))}
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
              Border Position
            </h2>
            <div className="grid grid-cols-2 gap-3">
              {BORDER_POSITIONS.map((position) => (
                <button
                  key={position.id}
                  className={`p-3 rounded-lg border ${
                    borderPosition === position.id
                      ? "border-primary dark:border-primary-dark bg-primary/10 dark:bg-primary-dark/10"
                      : "border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600"
                  }`}
                  onClick={() => setBorderPosition(position.id)}>
                  <span className="text-sm font-medium">
                    {position.name}
                  </span>
                </button>
              ))}
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
              Border Properties
            </h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Size (px)
                </label>
                <input
                  type="range"
                  min="5"
                  max="30"
                  value={borderSize}
                  onChange={(e) => setBorderSize(parseInt(e.target.value))}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-gray-500 mt-1">
                  <span>5px</span>
                  <span>{borderSize}px</span>
                  <span>30px</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Border Color
                </label>
                <input
                  type="color"
                  value={borderColor}
                  onChange={(e) => setBorderColor(e.target.value)}
                  className="w-full h-10 rounded border border-gray-300 dark:border-gray-600"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Background Color
                </label>
                <input
                  type="color"
                  value={backgroundColor}
                  onChange={(e) => setBackgroundColor(e.target.value)}
                  className="w-full h-10 rounded border border-gray-300 dark:border-gray-600"
                />
              </div>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
              Presets
            </h2>
            <div className="grid grid-cols-2 gap-2">
              {PRESETS.map((preset, index) => (
                <button
                  key={index}
                  className="p-2 text-left bg-white dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-600 rounded border border-gray-300 dark:border-gray-600 text-sm"
                  onClick={() => loadPreset(preset)}>
                  {preset.name}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Preview and Code Panel */}
        <div className="space-y-6">
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
              Preview
            </h2>
            <div className="w-full h-40 flex items-center justify-center">
              <div 
                className="w-3/4 h-3/4 rounded-lg flex items-center justify-center"
                style={{
                  position: "relative",
                  backgroundColor: backgroundColor,
                  [`padding${borderPosition.charAt(0).toUpperCase() + borderPosition.slice(1)}`]: `${borderSize + 10}px`,
                  overflow: "hidden"
                }}
              >
                <div className="text-center font-medium" style={{ color: getContrastColor(backgroundColor) }}>
                  <div className="flex flex-col items-center">
                    <span className="text-sm mb-1">
                      {BORDER_TYPES.find(t => t.id === borderType)?.name} Border
                    </span>
                    <span className="text-sm">
                      {BORDER_POSITIONS.find(p => p.id === borderPosition)?.name} Position
                    </span>
                  </div>
                </div>
                
                {/* The actual border element */}
                <div
                  className="absolute"
                  style={{
                    content: '""',
                    position: "absolute",
                    ...(borderPosition === 'top' || borderPosition === 'bottom' 
                      ? {
                          left: 0,
                          [borderPosition]: 0,
                          height: `${borderSize}px`,
                          width: '100%',
                          backgroundImage: getBorderBackgroundImage(),
                          backgroundSize: getBorderBackgroundSize(),
                          backgroundRepeat: 'repeat-x',
                          ...(borderType === 'zigzag' && { backgroundPosition: '0 0, ' + borderSize + 'px 0' })
                        }
                      : {
                          top: 0,
                          [borderPosition]: 0,
                          width: `${borderSize}px`,
                          height: '100%',
                          backgroundImage: getBorderBackgroundImage(),
                          backgroundSize: getBorderBackgroundSize(),
                          backgroundRepeat: 'repeat-y',
                          ...(borderType === 'zigzag' && { backgroundPosition: '0 0, 0 ' + borderSize + 'px' })
                        }
                    )
                  }}
                ></div>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
                CSS Code
              </h2>
              <button
                className="px-3 py-1 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors flex items-center"
                onClick={copyToClipboard}>
                <LucideIcon
                  name={copied ? "Check" : "Copy"}
                  size={16}
                  className="mr-1"
                />
                {copied ? "Copied!" : "Copy Code"}
              </button>
            </div>
            <pre className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-300 dark:border-gray-600 overflow-x-auto text-sm">
              {generateCSS()}
            </pre>
          </div>
        </div>
      </div>
    </ToolContainer>
  );
  
  // Helper function to get border background image based on type and position
  function getBorderBackgroundImage() {
    const isHorizontal = borderPosition === 'top' || borderPosition === 'bottom';
    
    switch (borderType) {
      case 'wavy':
        return `repeating-radial-gradient(
          ellipse at ${getGradientPosition()},
          transparent 0,
          transparent ${borderSize * 0.5}px,
          ${borderColor} ${borderSize * 0.5}px,
          ${borderColor} ${borderSize}px
        )`;
        
      case 'zigzag':
        if (isHorizontal) {
          return `linear-gradient(
            ${borderPosition === 'top' ? '135deg' : '45deg'},
            ${borderColor} 25%,
            transparent 25%
          ),
          linear-gradient(
            ${borderPosition === 'top' ? '45deg' : '135deg'},
            ${borderColor} 25%,
            transparent 25%
          )`;
        } else {
          return `linear-gradient(
            ${borderPosition === 'left' ? '45deg' : '135deg'},
            ${borderColor} 25%,
            transparent 25%
          ),
          linear-gradient(
            ${borderPosition === 'left' ? '135deg' : '45deg'},
            ${borderColor} 25%,
            transparent 25%
          )`;
        }
        
      case 'rounded':
        if (isHorizontal) {
          return `radial-gradient(
            circle at ${borderSize / 2}px ${borderPosition === 'top' ? '0' : borderSize}px,
            transparent ${borderSize / 2}px,
            ${borderColor} ${borderSize / 2}px
          )`;
        } else {
          return `radial-gradient(
            circle at ${borderPosition === 'left' ? '0' : borderSize}px ${borderSize / 2}px,
            transparent ${borderSize / 2}px,
            ${borderColor} ${borderSize / 2}px
          )`;
        }
        
      case 'diagonal':
        return `linear-gradient(
          ${getDiagonalAngle()},
          transparent 50%,
          ${borderColor} 50%
        )`;
        
      default:
        return 'none';
    }
  }
  
  // Helper function to get gradient position based on border position
  function getGradientPosition() {
    switch (borderPosition) {
      case 'top': return 'top';
      case 'bottom': return 'bottom';
      case 'left': return 'left';
      case 'right': return 'right';
      default: return 'bottom';
    }
  }
  
  // Helper function to get diagonal angle based on border position
  function getDiagonalAngle() {
    switch (borderPosition) {
      case 'top': return '135deg';
      case 'bottom': return '45deg';
      case 'left': return '45deg';
      case 'right': return '135deg';
      default: return '45deg';
    }
  }
  
  // Helper function to get background size based on border type and position
  function getBorderBackgroundSize() {
    const isHorizontal = borderPosition === 'top' || borderPosition === 'bottom';
    
    switch (borderType) {
      case 'wavy':
        return isHorizontal 
          ? `${borderSize * 2}px ${borderSize}px`
          : `${borderSize}px ${borderSize * 2}px`;
          
      case 'zigzag':
        return isHorizontal 
          ? `${borderSize * 2}px ${borderSize}px`
          : `${borderSize}px ${borderSize * 2}px`;
          
      case 'rounded':
      case 'diagonal':
        return `${borderSize}px ${borderSize}px`;
        
      default:
        return 'auto';
    }
  }
  
  // Helper function to determine text color based on background color
  function getContrastColor(hexColor) {
    // Convert hex to RGB
    const r = parseInt(hexColor.substr(1, 2), 16);
    const g = parseInt(hexColor.substr(3, 2), 16);
    const b = parseInt(hexColor.substr(5, 2), 16);
    
    // Calculate luminance
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
    
    // Return black or white based on luminance
    return luminance > 0.5 ? '#000000' : '#FFFFFF';
  }
}
