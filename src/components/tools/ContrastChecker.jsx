import { useState, useEffect } from "react";
import { getToolById } from "../../data/tools.js";
import { HexColorPicker } from "react-colorful";
import ToolHeader from "../ui/ToolHeader.jsx";
import ToolContainer from "../ui/ToolContainer.jsx";

// WCAG contrast ratios
const WCAG_LEVELS = {
    "AA Large": 3.0,
    "AA Normal": 4.5,
    "AAA Large": 4.5,
    "AAA Normal": 7.0,
};

export default function ContrastChecker() {
    const [toolData, setToolData] = useState(null);
    const [foregroundColor, setForegroundColor] = useState("#333333");
    const [backgroundColor, setBackgroundColor] = useState("#FFFFFF");
    const [contrastRatio, setContrastRatio] = useState(0);
    const [activeColorPicker, setActiveColorPicker] = useState(null);
    const [suggestedColors, setSuggestedColors] = useState([]);
    const [copied, setCopied] = useState(false);
    const [outputFormat, setOutputFormat] = useState("css");
    const [sampleText, setSampleText] = useState("Sample Text");
    const [fontSize, setFontSize] = useState(16);
    const [isBold, setIsBold] = useState(false);

    useEffect(() => {
        // Get the tool data
        const data = getToolById("contrastchecker");
        setToolData(data);
    }, []);

    useEffect(() => {
        calculateContrastRatio();
    }, [foregroundColor, backgroundColor]);

    // Calculate contrast ratio between background and text colors
    const calculateContrastRatio = () => {
        // Convert hex to RGB
        const hexToRgb = (hex) => {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(
                hex
            );
            return result
                ? {
                      r: parseInt(result[1], 16),
                      g: parseInt(result[2], 16),
                      b: parseInt(result[3], 16),
                  }
                : null;
        };

        // Calculate relative luminance
        const luminance = (r, g, b) => {
            const a = [r, g, b].map((v) => {
                v /= 255;
                return v <= 0.03928
                    ? v / 12.92
                    : Math.pow((v + 0.055) / 1.055, 2.4);
            });
            return a[0] * 0.2126 + a[1] * 0.7152 + a[2] * 0.0722;
        };

        const rgb1 = hexToRgb(backgroundColor);
        const rgb2 = hexToRgb(foregroundColor);

        if (rgb1 && rgb2) {
            const l1 = luminance(rgb1.r, rgb1.g, rgb1.b);
            const l2 = luminance(rgb2.r, rgb2.g, rgb2.b);

            const ratio =
                l1 > l2 ? (l1 + 0.05) / (l2 + 0.05) : (l2 + 0.05) / (l1 + 0.05);

            const roundedRatio = Math.round(ratio * 100) / 100;
            setContrastRatio(roundedRatio);

            // Generate suggested colors if contrast is too low
            if (roundedRatio < WCAG_LEVELS["AA Normal"]) {
                generateSuggestedColors(rgb2, l1, l2);
            } else {
                setSuggestedColors([]);
            }
        }
    };

    // Generate suggested colors with better contrast
    const generateSuggestedColors = (textRgb, bgLuminance, textLuminance) => {
        const suggestions = [];

        // Function to adjust color to meet target contrast
        const adjustColor = (r, g, b, targetContrast, adjustDarker) => {
            let newR = r;
            let newG = g;
            let newB = b;
            let step = adjustDarker ? -1 : 1;
            let currentContrast = 0;
            let iterations = 0;
            const maxIterations = 255;

            // Calculate luminance and contrast
            const calcLuminance = (r, g, b) => {
                const a = [r, g, b].map((v) => {
                    v /= 255;
                    return v <= 0.03928
                        ? v / 12.92
                        : Math.pow((v + 0.055) / 1.055, 2.4);
                });
                return a[0] * 0.2126 + a[1] * 0.7152 + a[2] * 0.0722;
            };

            const calcContrast = (l1, l2) => {
                return l1 > l2
                    ? (l1 + 0.05) / (l2 + 0.05)
                    : (l2 + 0.05) / (l1 + 0.05);
            };

            // Adjust color until we reach target contrast or max iterations
            while (
                currentContrast < targetContrast &&
                iterations < maxIterations
            ) {
                // Adjust all channels proportionally
                newR = Math.max(0, Math.min(255, newR + step));
                newG = Math.max(0, Math.min(255, newG + step));
                newB = Math.max(0, Math.min(255, newB + step));

                const newLuminance = calcLuminance(newR, newG, newB);
                currentContrast = calcContrast(bgLuminance, newLuminance);

                iterations++;
            }

            // Convert back to hex
            return `#${newR.toString(16).padStart(2, "0")}${newG
                .toString(16)
                .padStart(2, "0")}${newB.toString(16).padStart(2, "0")}`;
        };

        // Generate darker and lighter variations
        const { r, g, b } = textRgb;

        // For AA Normal (4.5:1)
        const darkerAA = adjustColor(r, g, b, WCAG_LEVELS["AA Normal"], true);
        const lighterAA = adjustColor(r, g, b, WCAG_LEVELS["AA Normal"], false);

        // For AAA Normal (7:1)
        const darkerAAA = adjustColor(r, g, b, WCAG_LEVELS["AAA Normal"], true);
        const lighterAAA = adjustColor(
            r,
            g,
            b,
            WCAG_LEVELS["AAA Normal"],
            false
        );

        // Add suggestions if they're different from the original color
        if (darkerAA !== foregroundColor) {
            suggestions.push({ color: darkerAA, level: "AA", type: "darker" });
        }

        if (lighterAA !== foregroundColor) {
            suggestions.push({
                color: lighterAA,
                level: "AA",
                type: "lighter",
            });
        }

        if (darkerAAA !== foregroundColor) {
            suggestions.push({
                color: darkerAAA,
                level: "AAA",
                type: "darker",
            });
        }

        if (lighterAAA !== foregroundColor) {
            suggestions.push({
                color: lighterAAA,
                level: "AAA",
                type: "lighter",
            });
        }

        setSuggestedColors(suggestions);
    };

    // Get contrast level based on WCAG guidelines
    const getContrastLevel = () => {
        if (contrastRatio >= WCAG_LEVELS["AAA Normal"]) {
            return {
                level: "AAA",
                text: "All Text",
                className: "text-green-600 dark:text-green-400",
            };
        } else if (contrastRatio >= WCAG_LEVELS["AAA Large"]) {
            return {
                level: "AAA",
                text: "Large Text",
                className: "text-green-600 dark:text-green-400",
            };
        } else if (contrastRatio >= WCAG_LEVELS["AA Normal"]) {
            return {
                level: "AA",
                text: "All Text",
                className: "text-yellow-600 dark:text-yellow-400",
            };
        } else if (contrastRatio >= WCAG_LEVELS["AA Large"]) {
            return {
                level: "AA",
                text: "Large Text",
                className: "text-yellow-600 dark:text-yellow-400",
            };
        } else {
            return {
                level: "Fail",
                text: "All Text",
                className: "text-red-600 dark:text-red-400",
            };
        }
    };

    // Apply a suggested color
    const applySuggestedColor = (color) => {
        setForegroundColor(color);
        setActiveColorPicker(null);
    };

    // Swap foreground and background colors
    const swapColors = () => {
        const temp = foregroundColor;
        setForegroundColor(backgroundColor);
        setBackgroundColor(temp);
    };

    // Generate CSS output
    const generateCSSOutput = () => {
        return `.element {\n  color: ${foregroundColor};\n  background-color: ${backgroundColor};\n}`;
    };

    // Generate Tailwind output
    const generateTailwindOutput = () => {
        return `<div class="text-[${foregroundColor}] bg-[${backgroundColor}]">\n  ${sampleText}\n</div>`;
    };

    // Generate SCSS output
    const generateSCSSOutput = () => {
        return `$text-color: ${foregroundColor};\n$background-color: ${backgroundColor};\n\n.element {\n  color: $text-color;\n  background-color: $background-color;\n}`;
    };

    // Get the output based on selected format
    const getOutput = () => {
        switch (outputFormat) {
            case "css":
                return generateCSSOutput();
            case "tailwind":
                return generateTailwindOutput();
            case "scss":
                return generateSCSSOutput();
            default:
                return generateCSSOutput();
        }
    };

    // Copy to clipboard
    const copyToClipboard = () => {
        navigator.clipboard.writeText(getOutput()).then(() => {
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        });
    };

    return (
        <ToolContainer
            className="py-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-colors duration-200"
            containerWidth={toolData?.containerWidth || "default"}>
            <ToolHeader
                toolData={toolData}
                defaultTitle="Color Contrast Checker"
                defaultIcon="Palette"
                defaultDescription="Check color contrast ratios for WCAG compliance and get accessible color suggestions for your web designs."
            />

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Preview Section */}
                <div>
                    <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                        Preview
                    </h2>
                    <div
                        className="w-full h-64 rounded-lg flex items-center justify-center p-4 mb-6 transition-all"
                        style={{ backgroundColor }}>
                        <div className="text-center">
                            <div
                                className="text-4xl mb-4"
                                style={{
                                    color: foregroundColor,
                                    fontSize: `${fontSize}px`,
                                    fontWeight: isBold ? "bold" : "normal",
                                }}>
                                {sampleText}
                            </div>
                            <div
                                className="text-sm"
                                style={{ color: foregroundColor }}>
                                Contrast Ratio:{" "}
                                <span className="font-bold">
                                    {contrastRatio}:1
                                </span>
                            </div>
                        </div>
                    </div>

                    {/* Text Controls */}
                    <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-2">
                            Text Settings
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Sample Text
                                </label>
                                <input
                                    type="text"
                                    value={sampleText}
                                    onChange={(e) =>
                                        setSampleText(e.target.value)
                                    }
                                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Font Size: {fontSize}px
                                </label>
                                <div className="flex items-center">
                                    <input
                                        type="range"
                                        min="12"
                                        max="48"
                                        value={fontSize}
                                        onChange={(e) =>
                                            setFontSize(
                                                parseInt(e.target.value)
                                            )
                                        }
                                        className="w-full mr-2"
                                    />
                                    <div className="flex items-center">
                                        <input
                                            type="checkbox"
                                            id="bold"
                                            checked={isBold}
                                            onChange={(e) =>
                                                setIsBold(e.target.checked)
                                            }
                                            className="mr-2"
                                        />
                                        <label
                                            htmlFor="bold"
                                            className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Bold
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* WCAG Results */}
                    <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-2">
                            WCAG Compliance
                        </h3>
                        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <div className="flex justify-between items-center mb-2">
                                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Contrast Ratio:
                                </span>
                                <span className="text-lg font-bold text-gray-800 dark:text-white">
                                    {contrastRatio}:1
                                </span>
                            </div>

                            <div className="grid grid-cols-2 gap-4 mb-2">
                                <div className="flex items-center">
                                    <div
                                        className={`w-4 h-4 rounded-full mr-2 ${
                                            contrastRatio >=
                                            WCAG_LEVELS["AA Normal"]
                                                ? "bg-green-500"
                                                : "bg-red-500"
                                        }`}></div>
                                    <span className="text-sm">
                                        AA Normal (4.5:1)
                                    </span>
                                </div>
                                <div className="flex items-center">
                                    <div
                                        className={`w-4 h-4 rounded-full mr-2 ${
                                            contrastRatio >=
                                            WCAG_LEVELS["AA Large"]
                                                ? "bg-green-500"
                                                : "bg-red-500"
                                        }`}></div>
                                    <span className="text-sm">
                                        AA Large (3:1)
                                    </span>
                                </div>
                                <div className="flex items-center">
                                    <div
                                        className={`w-4 h-4 rounded-full mr-2 ${
                                            contrastRatio >=
                                            WCAG_LEVELS["AAA Normal"]
                                                ? "bg-green-500"
                                                : "bg-red-500"
                                        }`}></div>
                                    <span className="text-sm">
                                        AAA Normal (7:1)
                                    </span>
                                </div>
                                <div className="flex items-center">
                                    <div
                                        className={`w-4 h-4 rounded-full mr-2 ${
                                            contrastRatio >=
                                            WCAG_LEVELS["AAA Large"]
                                                ? "bg-green-500"
                                                : "bg-red-500"
                                        }`}></div>
                                    <span className="text-sm">
                                        AAA Large (4.5:1)
                                    </span>
                                </div>
                            </div>

                            <div className="mt-2 p-2 bg-gray-100 dark:bg-gray-600 rounded text-center">
                                <span
                                    className={`text-sm font-bold ${
                                        getContrastLevel().className
                                    }`}>
                                    {getContrastLevel().level} Compliance for{" "}
                                    {getContrastLevel().text}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Controls Section */}
                <div>
                    <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                        Color Controls
                    </h2>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        {/* Foreground Color */}
                        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Text Color
                            </label>
                            <div
                                className="w-full h-12 rounded cursor-pointer border border-gray-300 dark:border-gray-600 flex items-center justify-center mb-2"
                                style={{ backgroundColor: foregroundColor }}
                                onClick={() =>
                                    setActiveColorPicker(
                                        activeColorPicker === "foreground"
                                            ? null
                                            : "foreground"
                                    )
                                }>
                                <span
                                    className="font-mono text-sm"
                                    style={{ color: backgroundColor }}>
                                    {foregroundColor}
                                </span>
                            </div>
                            {activeColorPicker === "foreground" && (
                                <div className="mt-2 p-2 bg-white dark:bg-gray-800 rounded shadow-lg">
                                    <HexColorPicker
                                        color={foregroundColor}
                                        onChange={setForegroundColor}
                                    />
                                    <div className="mt-2 flex justify-between">
                                        <input
                                            type="text"
                                            value={foregroundColor}
                                            onChange={(e) =>
                                                setForegroundColor(
                                                    e.target.value
                                                )
                                            }
                                            className="w-full p-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                                        />
                                        <button
                                            className="ml-2 text-xs text-primary dark:text-primary-dark px-2 py-1 border border-primary dark:border-primary-dark rounded"
                                            onClick={() =>
                                                setActiveColorPicker(null)
                                            }>
                                            Close
                                        </button>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Background Color */}
                        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Background Color
                            </label>
                            <div
                                className="w-full h-12 rounded cursor-pointer border border-gray-300 dark:border-gray-600 flex items-center justify-center mb-2"
                                style={{ backgroundColor }}
                                onClick={() =>
                                    setActiveColorPicker(
                                        activeColorPicker === "background"
                                            ? null
                                            : "background"
                                    )
                                }>
                                <span
                                    className="font-mono text-sm"
                                    style={{ color: foregroundColor }}>
                                    {backgroundColor}
                                </span>
                            </div>
                            {activeColorPicker === "background" && (
                                <div className="mt-2 p-2 bg-white dark:bg-gray-800 rounded shadow-lg">
                                    <HexColorPicker
                                        color={backgroundColor}
                                        onChange={setBackgroundColor}
                                    />
                                    <div className="mt-2 flex justify-between">
                                        <input
                                            type="text"
                                            value={backgroundColor}
                                            onChange={(e) =>
                                                setBackgroundColor(
                                                    e.target.value
                                                )
                                            }
                                            className="w-full p-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                                        />
                                        <button
                                            className="ml-2 text-xs text-primary dark:text-primary-dark px-2 py-1 border border-primary dark:border-primary-dark rounded"
                                            onClick={() =>
                                                setActiveColorPicker(null)
                                            }>
                                            Close
                                        </button>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Swap Colors Button */}
                    <div className="flex justify-center mb-6">
                        <button
                            className="px-4 py-2 bg-gray-100 dark:bg-gray-700 rounded-md text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center"
                            onClick={swapColors}>
                            <svg
                                className="w-4 h-4 mr-2"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"
                                />
                            </svg>
                            Swap Colors
                        </button>
                    </div>

                    {/* Suggested Colors */}
                    {suggestedColors.length > 0 && (
                        <div className="mb-6">
                            <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-2">
                                Suggested Colors
                            </h3>
                            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                                    These colors will provide better contrast
                                    with your current background:
                                </p>
                                <div className="grid grid-cols-2 gap-2">
                                    {suggestedColors.map(
                                        (suggestion, index) => (
                                            <button
                                                key={index}
                                                className="p-2 border border-gray-300 dark:border-gray-600 rounded flex items-center hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                                                onClick={() =>
                                                    applySuggestedColor(
                                                        suggestion.color
                                                    )
                                                }>
                                                <div
                                                    className="w-6 h-6 rounded mr-2"
                                                    style={{
                                                        backgroundColor:
                                                            suggestion.color,
                                                    }}></div>
                                                <div className="text-left">
                                                    <div className="text-xs font-mono">
                                                        {suggestion.color}
                                                    </div>
                                                    <div className="text-xs">
                                                        {suggestion.level} (
                                                        {suggestion.type})
                                                    </div>
                                                </div>
                                            </button>
                                        )
                                    )}
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Output Section */}
                    <div>
                        <div className="flex justify-between items-center mb-2">
                            <h3 className="text-lg font-medium text-gray-800 dark:text-white">
                                Generated Code
                            </h3>
                            <div className="flex gap-2">
                                <select
                                    value={outputFormat}
                                    onChange={(e) =>
                                        setOutputFormat(e.target.value)
                                    }
                                    className="text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 px-2 py-1">
                                    <option value="css">CSS</option>
                                    <option value="tailwind">Tailwind</option>
                                    <option value="scss">SCSS</option>
                                </select>
                                <button
                                    className="px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-md text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                                    onClick={copyToClipboard}>
                                    {copied ? "Copied!" : "Copy"}
                                </button>
                            </div>
                        </div>
                        <div className="bg-gray-100 dark:bg-gray-700 rounded-md p-4 overflow-x-auto">
                            <pre className="text-sm text-gray-800 dark:text-gray-200 font-mono">
                                {getOutput()}
                            </pre>
                        </div>
                    </div>
                </div>
            </div>
        </ToolContainer>
    );
}
