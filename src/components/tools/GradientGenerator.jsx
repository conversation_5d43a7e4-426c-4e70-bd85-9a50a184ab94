import React, { useState, useEffect } from "react";
import { getToolById } from "../../data/tools.js";
import { HexColorPicker } from "react-colorful";
import ToolHeader from "../ui/ToolHeader.jsx";
import ToolContainer from "../ui/ToolContainer.jsx";
import LucideIcon from "../ui/LucideIcon.jsx";

// Gradient types
const GRADIENT_TYPES = {
    LINEAR: "linear",
    RADIAL: "radial",
    CONIC: "conic",
};

// Direction presets for linear gradients
const LINEAR_DIRECTIONS = [
    { value: "to top", label: "↑", title: "To Top" },
    { value: "to top right", label: "↗", title: "To Top Right" },
    { value: "to right", label: "→", title: "To Right" },
    { value: "to bottom right", label: "↘", title: "To Bottom Right" },
    { value: "to bottom", label: "↓", title: "To Bottom" },
    { value: "to bottom left", label: "↙", title: "To Bottom Left" },
    { value: "to left", label: "←", title: "To Left" },
    { value: "to top left", label: "↖", title: "To Top Left" },
];

// Position presets for radial gradients
const RADIAL_POSITIONS = [
    { value: "center", label: "Center" },
    { value: "top", label: "Top" },
    { value: "top right", label: "Top Right" },
    { value: "right", label: "Right" },
    { value: "bottom right", label: "Bottom Right" },
    { value: "bottom", label: "Bottom" },
    { value: "bottom left", label: "Bottom Left" },
    { value: "left", label: "Left" },
    { value: "top left", label: "Top Left" },
];

// Shape options for radial gradients
const RADIAL_SHAPES = [
    { value: "circle", label: "Circle" },
    { value: "ellipse", label: "Ellipse" },
];

// Export format options
const EXPORT_FORMATS = {
    CSS: "css",
    TAILWIND: "tailwind",
    REACT: "react",
};

// Animation timing functions
const TIMING_FUNCTIONS = [
    { value: "linear", label: "Linear" },
    { value: "ease", label: "Ease" },
    { value: "ease-in", label: "Ease In" },
    { value: "ease-out", label: "Ease Out" },
    { value: "ease-in-out", label: "Ease In Out" },
];

// Preset gradients
const PRESET_GRADIENTS = [
    {
        name: "Sunset",
        type: GRADIENT_TYPES.LINEAR,
        direction: "to right",
        colors: [
            { id: 1, color: "#f56565", position: 0 },
            { id: 2, color: "#ed8936", position: 50 },
            { id: 3, color: "#d69e2e", position: 100 },
        ],
    },
    {
        name: "Ocean",
        type: GRADIENT_TYPES.LINEAR,
        direction: "to bottom",
        colors: [
            { id: 1, color: "#4299e1", position: 0 },
            { id: 2, color: "#3182ce", position: 50 },
            { id: 3, color: "#2c5282", position: 100 },
        ],
    },
    {
        name: "Forest",
        type: GRADIENT_TYPES.LINEAR,
        direction: "to right",
        colors: [
            { id: 1, color: "#48bb78", position: 0 },
            { id: 2, color: "#38a169", position: 50 },
            { id: 3, color: "#2f855a", position: 100 },
        ],
    },
    {
        name: "Purple Haze",
        type: GRADIENT_TYPES.LINEAR,
        direction: "to bottom right",
        colors: [
            { id: 1, color: "#9f7aea", position: 0 },
            { id: 2, color: "#6b46c1", position: 50 },
            { id: 3, color: "#553c9a", position: 100 },
        ],
    },
    {
        name: "Candy",
        type: GRADIENT_TYPES.LINEAR,
        direction: "to right",
        colors: [
            { id: 1, color: "#f687b3", position: 0 },
            { id: 2, color: "#fc8181", position: 50 },
            { id: 3, color: "#f6ad55", position: 100 },
        ],
    },
    {
        name: "Midnight",
        type: GRADIENT_TYPES.LINEAR,
        direction: "to bottom",
        colors: [
            { id: 1, color: "#1a202c", position: 0 },
            { id: 2, color: "#2d3748", position: 50 },
            { id: 3, color: "#4a5568", position: 100 },
        ],
    },
    {
        name: "Emerald",
        type: GRADIENT_TYPES.LINEAR,
        direction: "135deg",
        colors: [
            { id: 1, color: "#84fab0", position: 0 },
            { id: 2, color: "#4fd1c5", position: 50 },
            { id: 3, color: "#38b2ac", position: 100 },
        ],
    },
    {
        name: "Radial Burst",
        type: GRADIENT_TYPES.RADIAL,
        shape: "circle",
        position: "center",
        colors: [
            { id: 1, color: "#f6e05e", position: 0 },
            { id: 2, color: "#dd6b20", position: 100 },
        ],
    },
    {
        name: "Radial Blues",
        type: GRADIENT_TYPES.RADIAL,
        shape: "circle",
        position: "center",
        colors: [
            { id: 1, color: "#90cdf4", position: 0 },
            { id: 2, color: "#3182ce", position: 70 },
            { id: 3, color: "#2c5282", position: 100 },
        ],
    },
    {
        name: "Soft Focus",
        type: GRADIENT_TYPES.RADIAL,
        shape: "ellipse",
        position: "top right",
        colors: [
            { id: 1, color: "#ffffff", position: 0 },
            { id: 2, color: "#e2e8f0", position: 50 },
            { id: 3, color: "#cbd5e0", position: 100 },
        ],
    },
    {
        name: "Conic Spectrum",
        type: GRADIENT_TYPES.CONIC,
        angle: 0,
        position: "center",
        colors: [
            { id: 1, color: "#ff0000", position: 0 },
            { id: 2, color: "#ffff00", position: 25 },
            { id: 3, color: "#00ff00", position: 50 },
            { id: 4, color: "#00ffff", position: 75 },
            { id: 5, color: "#0000ff", position: 100 },
        ],
    },
    {
        name: "Conic Pastels",
        type: GRADIENT_TYPES.CONIC,
        angle: 45,
        position: "center",
        colors: [
            { id: 1, color: "#fed7d7", position: 0 },
            { id: 2, color: "#feebc8", position: 25 },
            { id: 3, color: "#fefcbf", position: 50 },
            { id: 4, color: "#c6f6d5", position: 75 },
            { id: 5, color: "#bee3f8", position: 100 },
        ],
    },
];

export default function GradientGenerator() {
    const [toolData, setToolData] = useState(null);
    const [gradientType, setGradientType] = useState(GRADIENT_TYPES.LINEAR);
    const [colors, setColors] = useState([
        { id: 1, color: "#3366cc", position: 0 },
        { id: 2, color: "#6633cc", position: 100 },
    ]);
    const [activeColorId, setActiveColorId] = useState(1);
    const [direction, setDirection] = useState("to right");
    const [angle, setAngle] = useState(90);
    const [radialShape, setRadialShape] = useState("circle");
    const [radialPosition, setRadialPosition] = useState("center");
    const [conicAngle, setConicAngle] = useState(0);
    const [conicPosition, setConicPosition] = useState("center");
    const [showColorPicker, setShowColorPicker] = useState(false);
    const [exportFormat, setExportFormat] = useState(EXPORT_FORMATS.CSS);
    const [copied, setCopied] = useState(false);
    const [useCustomAngle, setUseCustomAngle] = useState(false);

    // Animation states
    const [animationEnabled, setAnimationEnabled] = useState(false);
    const [animationDuration, setAnimationDuration] = useState(5);
    const [animationTiming, setAnimationTiming] = useState("linear");
    const [animationDirection, setAnimationDirection] = useState("alternate");
    const [animationIterations, setAnimationIterations] = useState("infinite");

    useEffect(() => {
        // Get the tool data
        const data = getToolById("gradientgenerator");
        setToolData(data);
    }, []);

    // Get the active color
    const getActiveColor = () => {
        return colors.find((c) => c.id === activeColorId) || colors[0];
    };

    // Update a color in the colors array
    const updateColor = (id, newColor) => {
        setColors(
            colors.map((c) => (c.id === id ? { ...c, color: newColor } : c))
        );
    };

    // Update a color position in the colors array
    const updateColorPosition = (id, position) => {
        setColors(
            colors.map((c) =>
                c.id === id
                    ? { ...c, position: Math.max(0, Math.min(100, position)) }
                    : c
            )
        );
    };

    // Add a new color to the gradient
    const addColor = () => {
        const newId = Math.max(...colors.map((c) => c.id)) + 1;
        const activeColor = getActiveColor();
        const newPosition = Math.min(activeColor.position + 10, 100);
        const newColor = {
            id: newId,
            color: activeColor.color,
            position: newPosition,
        };
        setColors([...colors, newColor]);
        setActiveColorId(newId);
        setShowColorPicker(true);
    };

    // Remove a color from the gradient
    const removeColor = (id) => {
        if (colors.length <= 2) return; // Keep at least 2 colors
        setColors(colors.filter((c) => c.id !== id));
        if (activeColorId === id) {
            setActiveColorId(colors[0].id);
        }
    };

    // Generate the CSS gradient string
    const generateGradientCSS = () => {
        // Sort colors by position
        const sortedColors = [...colors].sort(
            (a, b) => a.position - b.position
        );
        const colorStops = sortedColors
            .map((c) => `${c.color} ${c.position}%`)
            .join(", ");

        switch (gradientType) {
            case GRADIENT_TYPES.LINEAR:
                return useCustomAngle
                    ? `linear-gradient(${angle}deg, ${colorStops})`
                    : `linear-gradient(${direction}, ${colorStops})`;

            case GRADIENT_TYPES.RADIAL:
                return `radial-gradient(${radialShape} at ${radialPosition}, ${colorStops})`;

            case GRADIENT_TYPES.CONIC:
                return `conic-gradient(from ${conicAngle}deg at ${conicPosition}, ${colorStops})`;

            default:
                return `linear-gradient(to right, ${colorStops})`;
        }
    };

    // Generate animation CSS
    const generateAnimationCSS = () => {
        if (!animationEnabled) return "";

        let animationClass = "";
        let additionalProperties = "";

        // Different properties based on gradient type
        if (gradientType === GRADIENT_TYPES.LINEAR) {
            animationClass = "gradient-animation-linear";
            additionalProperties = "background-size: 200% 200%;";
        } else if (gradientType === GRADIENT_TYPES.RADIAL) {
            animationClass = "gradient-animation-radial";
            additionalProperties = "background-position: center center;";
        } else if (gradientType === GRADIENT_TYPES.CONIC) {
            animationClass = "gradient-animation-conic";
            additionalProperties = "transform-origin: center center;";
        }

        return `/* Import the gradient animations CSS file in your project */
@import 'gradient-animations.css';

.animated-gradient {
  background: ${generateGradientCSS()};
  animation-name: ${animationClass};
  animation-duration: ${animationDuration}s;
  animation-timing-function: ${animationTiming};
  animation-direction: ${animationDirection};
  animation-iteration-count: ${animationIterations};
  ${additionalProperties}
}`;
    };

    // Get the output code based on selected format
    const getOutputCode = () => {
        const gradientCSS = generateGradientCSS();
        const animationCSS = animationEnabled ? generateAnimationCSS() : "";

        switch (exportFormat) {
            case EXPORT_FORMATS.CSS:
                if (animationEnabled) {
                    return animationCSS;
                } else {
                    return `.gradient {\n  background: ${gradientCSS};\n}`;
                }

            case EXPORT_FORMATS.TAILWIND:
                if (animationEnabled) {
                    // For animated gradients, we need to use a more complex approach with custom keyframes
                    let animationClass = "";
                    let additionalClasses = "";

                    if (gradientType === GRADIENT_TYPES.LINEAR) {
                        animationClass = "gradient-animation-linear";
                        additionalClasses = "bg-[length:200%_200%]";
                    } else if (gradientType === GRADIENT_TYPES.RADIAL) {
                        animationClass = "gradient-animation-radial";
                        additionalClasses = "bg-center";
                    } else if (gradientType === GRADIENT_TYPES.CONIC) {
                        animationClass = "gradient-animation-conic";
                        additionalClasses = "origin-center";
                    }

                    return `/* Add gradient-animations.css to your project */

/* Use this in your HTML */
<div class="bg-gradient-to-r from-[${colors[0].color}] to-[${
                        colors[colors.length - 1].color
                    }] ${additionalClasses} animate-${animationClass}"></div>

/* Add this to your tailwind.config.js */
module.exports = {
  theme: {
    extend: {
      animation: {
        '${animationClass}': '${animationClass} ${animationDuration}s ${animationTiming} ${animationDirection} ${animationIterations}',
      },
      keyframes: {
        '${animationClass}': {
          ${
              gradientType === GRADIENT_TYPES.LINEAR
                  ? `'0%, 100%': { backgroundPosition: '0% 50%' },
          '50%': { backgroundPosition: '100% 50%' },`
                  : gradientType === GRADIENT_TYPES.RADIAL
                  ? `'0%, 100%': { backgroundSize: '100% 100%' },
          '50%': { backgroundSize: '200% 200%' },`
                  : `'0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(360deg)' },`
          }
        }
      }
    }
  }
}`;
                } else {
                    // Simple gradient without animation
                    return `<div class="bg-gradient-to-r from-[${
                        colors[0].color
                    }] to-[${colors[colors.length - 1].color}]"></div>`;
                }

            case EXPORT_FORMATS.REACT:
                if (animationEnabled) {
                    let animationClass = "";
                    let additionalProps = "";

                    if (gradientType === GRADIENT_TYPES.LINEAR) {
                        animationClass = "gradient-animation-linear";
                        additionalProps = '\n  backgroundSize: "200% 200%",';
                    } else if (gradientType === GRADIENT_TYPES.RADIAL) {
                        animationClass = "gradient-animation-radial";
                        additionalProps =
                            '\n  backgroundPosition: "center center",';
                    } else if (gradientType === GRADIENT_TYPES.CONIC) {
                        animationClass = "gradient-animation-conic";
                        additionalProps =
                            '\n  transformOrigin: "center center",';
                    }

                    return `// Import gradient-animations.css in your project

// Use this in your component
const gradientStyle = {
  background: '${gradientCSS}',${additionalProps}
  animationName: '${animationClass}',
  animationDuration: '${animationDuration}s',
  animationTimingFunction: '${animationTiming}',
  animationDirection: '${animationDirection}',
  animationIterationCount: '${animationIterations}'
};

<div style={gradientStyle}></div>`;
                } else {
                    return `const gradientStyle = {
  background: '${gradientCSS}'
};

<div style={gradientStyle}></div>`;
                }

            default:
                return `.gradient {\n  background: ${gradientCSS};\n}`;
        }
    };

    // Copy to clipboard
    const copyToClipboard = () => {
        navigator.clipboard.writeText(getOutputCode()).then(() => {
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        });
    };

    // Load a preset gradient
    const loadPreset = (preset) => {
        setGradientType(preset.type);
        setColors(preset.colors);
        setActiveColorId(preset.colors[0].id);

        if (preset.type === GRADIENT_TYPES.LINEAR) {
            if (preset.direction.includes("deg")) {
                setUseCustomAngle(true);
                setAngle(parseInt(preset.direction));
            } else {
                setUseCustomAngle(false);
                setDirection(preset.direction);
            }
        } else if (preset.type === GRADIENT_TYPES.RADIAL) {
            setRadialShape(preset.shape);
            setRadialPosition(preset.position);
        } else if (preset.type === GRADIENT_TYPES.CONIC) {
            setConicAngle(preset.angle);
            setConicPosition(preset.position);
        }
    };

    // Load a random preset gradient for quick testing
    const loadRandomPreset = () => {
        // Choose a random preset
        const randomPreset =
            PRESET_GRADIENTS[
                Math.floor(Math.random() * PRESET_GRADIENTS.length)
            ];
        loadPreset(randomPreset);

        // Enable animation for the random preset
        setAnimationEnabled(true);
        setAnimationDuration(5);
        setAnimationTiming("ease-in-out");
        setAnimationDirection("alternate");
        setAnimationIterations("infinite");
    };

    // Define animation classes that will be used instead of inline keyframes
    // These will be added to the global CSS in a separate file
    const getAnimationClass = () => {
        if (!animationEnabled) return "";

        if (gradientType === GRADIENT_TYPES.LINEAR) {
            return "gradient-animation-linear";
        } else if (gradientType === GRADIENT_TYPES.RADIAL) {
            return "gradient-animation-radial";
        } else if (gradientType === GRADIENT_TYPES.CONIC) {
            return "gradient-animation-conic";
        }

        return "";
    };

    // Preview style with the current gradient
    const previewStyle = {
        background: generateGradientCSS(),
        width: "100%",
        height: "100%",
        borderRadius: "0.5rem",
        transition: "all 0.3s ease",
    };

    // Add animation properties based on gradient type
    if (animationEnabled) {
        // Apply different animation styles based on gradient type
        if (gradientType === GRADIENT_TYPES.LINEAR) {
            previewStyle.backgroundSize = "200% 200%";
        } else if (gradientType === GRADIENT_TYPES.RADIAL) {
            previewStyle.backgroundPosition = "center center";
        } else if (gradientType === GRADIENT_TYPES.CONIC) {
            previewStyle.transformOrigin = "center center";
        }
    }

    return (
        <ToolContainer
            className="py-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-colors duration-200"
            containerWidth={toolData?.containerWidth || "default"}>
            <ToolHeader
                toolData={toolData}
                defaultTitle="Gradient Generator"
                defaultIcon="Paintbrush"
                defaultDescription="Create stunning CSS gradients for your web projects with our visual gradient generator. Design linear, radial, and conic gradients with multiple color stops, customize angles and positions, and add smooth animations."
            />

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Controls Panel */}
                <div>
                    {/* Gradient Type Selection */}
                    <div className="mb-6">
                        <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200 mb-2">
                            Gradient Type
                        </h3>
                        <div className="flex space-x-2">
                            {Object.values(GRADIENT_TYPES).map((type) => (
                                <button
                                    key={type}
                                    type="button"
                                    className={`px-4 py-2 rounded-md text-sm ${
                                        gradientType === type
                                            ? "bg-primary dark:bg-primary-dark text-white"
                                            : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                                    } transition-colors`}
                                    onClick={() => setGradientType(type)}>
                                    {type.charAt(0).toUpperCase() +
                                        type.slice(1)}
                                </button>
                            ))}
                        </div>
                    </div>

                    {/* Gradient Direction/Angle Controls - Linear */}
                    {gradientType === GRADIENT_TYPES.LINEAR && (
                        <div className="mb-6">
                            <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200 mb-2">
                                Direction
                            </h3>
                            <div className="flex flex-wrap gap-2 mb-3">
                                {LINEAR_DIRECTIONS.map((dir) => (
                                    <button
                                        key={dir.value}
                                        type="button"
                                        className={`w-10 h-10 flex items-center justify-center rounded-md text-lg ${
                                            !useCustomAngle &&
                                            direction === dir.value
                                                ? "bg-primary dark:bg-primary-dark text-white"
                                                : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                                        } transition-colors`}
                                        onClick={() => {
                                            setDirection(dir.value);
                                            setUseCustomAngle(false);
                                        }}
                                        title={dir.title}>
                                        {dir.label}
                                    </button>
                                ))}
                            </div>
                            <div className="flex items-center mt-3">
                                <input
                                    type="checkbox"
                                    id="useCustomAngle"
                                    checked={useCustomAngle}
                                    onChange={() =>
                                        setUseCustomAngle(!useCustomAngle)
                                    }
                                    className="mr-2 h-4 w-4 text-primary dark:text-primary-dark rounded border-gray-300 focus:ring-primary dark:focus:ring-primary-dark"
                                />
                                <label
                                    htmlFor="useCustomAngle"
                                    className="text-sm text-gray-700 dark:text-gray-300">
                                    Use custom angle
                                </label>
                            </div>
                            {useCustomAngle && (
                                <div className="mt-3">
                                    <label
                                        htmlFor="angleSlider"
                                        className="block text-sm text-gray-700 dark:text-gray-300 mb-1">
                                        Angle: {angle}°
                                    </label>
                                    <input
                                        type="range"
                                        id="angleSlider"
                                        min="0"
                                        max="360"
                                        value={angle}
                                        onChange={(e) =>
                                            setAngle(parseInt(e.target.value))
                                        }
                                        className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer"
                                    />
                                </div>
                            )}
                        </div>
                    )}

                    {/* Radial Gradient Controls */}
                    {gradientType === GRADIENT_TYPES.RADIAL && (
                        <div className="mb-6">
                            <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200 mb-2">
                                Shape
                            </h3>
                            <div className="flex space-x-2 mb-4">
                                {RADIAL_SHAPES.map((shape) => (
                                    <button
                                        key={shape.value}
                                        type="button"
                                        className={`px-4 py-2 rounded-md text-sm ${
                                            radialShape === shape.value
                                                ? "bg-primary dark:bg-primary-dark text-white"
                                                : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                                        } transition-colors`}
                                        onClick={() =>
                                            setRadialShape(shape.value)
                                        }>
                                        {shape.label}
                                    </button>
                                ))}
                            </div>

                            <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200 mb-2">
                                Position
                            </h3>
                            <div className="grid grid-cols-3 gap-2">
                                {RADIAL_POSITIONS.map((pos) => (
                                    <button
                                        key={pos.value}
                                        type="button"
                                        className={`px-3 py-2 rounded-md text-sm ${
                                            radialPosition === pos.value
                                                ? "bg-primary dark:bg-primary-dark text-white"
                                                : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                                        } transition-colors`}
                                        onClick={() =>
                                            setRadialPosition(pos.value)
                                        }>
                                        {pos.label}
                                    </button>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* Conic Gradient Controls */}
                    {gradientType === GRADIENT_TYPES.CONIC && (
                        <div className="mb-6">
                            <div className="mb-4">
                                <label
                                    htmlFor="conicAngleSlider"
                                    className="block text-sm font-semibold text-gray-800 dark:text-gray-200 mb-1">
                                    Angle: {conicAngle}°
                                </label>
                                <input
                                    type="range"
                                    id="conicAngleSlider"
                                    min="0"
                                    max="360"
                                    value={conicAngle}
                                    onChange={(e) =>
                                        setConicAngle(parseInt(e.target.value))
                                    }
                                    className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer"
                                />
                            </div>

                            <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200 mb-2">
                                Position
                            </h3>
                            <div className="grid grid-cols-3 gap-2">
                                {RADIAL_POSITIONS.map((pos) => (
                                    <button
                                        key={pos.value}
                                        type="button"
                                        className={`px-3 py-2 rounded-md text-sm ${
                                            conicPosition === pos.value
                                                ? "bg-primary dark:bg-primary-dark text-white"
                                                : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                                        } transition-colors`}
                                        onClick={() =>
                                            setConicPosition(pos.value)
                                        }>
                                        {pos.label}
                                    </button>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* Color Stops Management */}
                    <div className="mb-6">
                        <div className="flex justify-between items-center mb-2">
                            <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200">
                                Color Stops
                            </h3>
                            <button
                                type="button"
                                className="px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-md text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center"
                                onClick={addColor}>
                                <LucideIcon
                                    name="Plus"
                                    size={16}
                                    className="mr-1"
                                />
                                Add Color
                            </button>
                        </div>

                        {/* Color Stop Slider */}
                        <div className="h-8 mb-4 bg-gray-200 dark:bg-gray-700 rounded-lg relative">
                            {colors.map((color) => (
                                <div
                                    key={color.id}
                                    className={`absolute w-6 h-6 -mt-3 -ml-3 rounded-full cursor-pointer border-2 ${
                                        activeColorId === color.id
                                            ? "border-primary dark:border-primary-dark"
                                            : "border-gray-300 dark:border-gray-600"
                                    }`}
                                    style={{
                                        backgroundColor: color.color,
                                        left: `${color.position}%`,
                                        top: "50%",
                                    }}
                                    onClick={() => {
                                        setActiveColorId(color.id);
                                        setShowColorPicker(true);
                                    }}></div>
                            ))}
                        </div>

                        {/* Active Color Controls */}
                        {getActiveColor() && (
                            <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                <div className="flex items-center justify-between mb-3">
                                    <div className="flex items-center">
                                        <div
                                            className="w-8 h-8 rounded-full mr-3"
                                            style={{
                                                backgroundColor:
                                                    getActiveColor().color,
                                            }}
                                            onClick={() =>
                                                setShowColorPicker(
                                                    !showColorPicker
                                                )
                                            }></div>
                                        <input
                                            type="text"
                                            value={getActiveColor().color}
                                            onChange={(e) =>
                                                updateColor(
                                                    activeColorId,
                                                    e.target.value
                                                )
                                            }
                                            className="w-24 p-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-sm"
                                        />
                                    </div>
                                    {colors.length > 2 && (
                                        <button
                                            type="button"
                                            className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                                            onClick={() =>
                                                removeColor(activeColorId)
                                            }>
                                            <LucideIcon
                                                name="Trash2"
                                                size={16}
                                            />
                                        </button>
                                    )}
                                </div>

                                <div>
                                    <label
                                        htmlFor="positionSlider"
                                        className="block text-sm text-gray-700 dark:text-gray-300 mb-1">
                                        Position: {getActiveColor().position}%
                                    </label>
                                    <input
                                        type="range"
                                        id="positionSlider"
                                        min="0"
                                        max="100"
                                        value={getActiveColor().position}
                                        onChange={(e) =>
                                            updateColorPosition(
                                                activeColorId,
                                                parseInt(e.target.value)
                                            )
                                        }
                                        className="w-full h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer"
                                    />
                                </div>

                                {/* Color Picker */}
                                {showColorPicker && (
                                    <div className="mt-3">
                                        <HexColorPicker
                                            color={getActiveColor().color}
                                            onChange={(color) =>
                                                updateColor(
                                                    activeColorId,
                                                    color
                                                )
                                            }
                                            className="w-full"
                                        />
                                    </div>
                                )}
                            </div>
                        )}
                    </div>

                    {/* Preset Gradients */}
                    <div className="mb-6">
                        <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200 mb-2">
                            Preset Gradients
                        </h3>
                        <div className="grid grid-cols-2 gap-2">
                            {PRESET_GRADIENTS.map((preset, index) => {
                                const presetColors = preset.colors
                                    .map((c) => `${c.color} ${c.position}%`)
                                    .join(", ");
                                const presetGradient =
                                    preset.type === GRADIENT_TYPES.LINEAR
                                        ? `linear-gradient(${preset.direction}, ${presetColors})`
                                        : preset.type === GRADIENT_TYPES.RADIAL
                                        ? `radial-gradient(${preset.shape} at ${preset.position}, ${presetColors})`
                                        : `conic-gradient(from ${preset.angle}deg at ${preset.position}, ${presetColors})`;

                                return (
                                    <button
                                        key={index}
                                        type="button"
                                        className="h-12 rounded-md overflow-hidden border border-gray-300 dark:border-gray-600 hover:border-primary dark:hover:border-primary-dark transition-all"
                                        style={{ background: presetGradient }}
                                        onClick={() => loadPreset(preset)}
                                        title={preset.name}></button>
                                );
                            })}
                        </div>
                    </div>
                </div>

                {/* Preview Panel */}
                <div>
                    <div className="flex justify-between items-center mb-4">
                        <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
                            Preview
                        </h2>
                        <button
                            type="button"
                            className="px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-md text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center"
                            onClick={loadRandomPreset}>
                            <LucideIcon
                                name="Sparkles"
                                size={16}
                                className="mr-1"
                            />
                            Random Preset
                        </button>
                    </div>
                    <div className="w-full h-64 rounded-lg overflow-hidden mb-6">
                        <div
                            className={`${
                                animationEnabled ? getAnimationClass() : ""
                            }`}
                            style={{
                                ...previewStyle,
                                animationDuration: `${animationDuration}s`,
                                animationTimingFunction: animationTiming,
                                animationDirection: animationDirection,
                                animationIterationCount: animationIterations,
                            }}></div>
                    </div>

                    {/* Animation Controls */}
                    <div className="mb-6">
                        <div className="flex items-center justify-between mb-3">
                            <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200">
                                Animation
                            </h3>
                            <div className="flex items-center">
                                <input
                                    type="checkbox"
                                    id="animationEnabled"
                                    checked={animationEnabled}
                                    onChange={() =>
                                        setAnimationEnabled(!animationEnabled)
                                    }
                                    className="mr-2 h-4 w-4 text-primary dark:text-primary-dark rounded border-gray-300 focus:ring-primary dark:focus:ring-primary-dark"
                                />
                                <label
                                    htmlFor="animationEnabled"
                                    className="text-sm text-gray-700 dark:text-gray-300">
                                    Enable animation
                                </label>
                            </div>
                        </div>

                        {animationEnabled && (
                            <div className="space-y-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                {/* Duration */}
                                <div>
                                    <label
                                        htmlFor="durationSlider"
                                        className="block text-sm text-gray-700 dark:text-gray-300 mb-1">
                                        Duration: {animationDuration}s
                                    </label>
                                    <input
                                        type="range"
                                        id="durationSlider"
                                        min="1"
                                        max="20"
                                        value={animationDuration}
                                        onChange={(e) =>
                                            setAnimationDuration(
                                                parseInt(e.target.value)
                                            )
                                        }
                                        className="w-full h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer"
                                    />
                                </div>

                                {/* Timing Function */}
                                <div>
                                    <label className="block text-sm text-gray-700 dark:text-gray-300 mb-1">
                                        Timing Function
                                    </label>
                                    <div className="flex flex-wrap gap-2">
                                        {TIMING_FUNCTIONS.map((timing) => (
                                            <button
                                                key={timing.value}
                                                type="button"
                                                className={`px-3 py-1 rounded-md text-xs ${
                                                    animationTiming ===
                                                    timing.value
                                                        ? "bg-primary dark:bg-primary-dark text-white"
                                                        : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                                                } transition-colors`}
                                                onClick={() =>
                                                    setAnimationTiming(
                                                        timing.value
                                                    )
                                                }>
                                                {timing.label}
                                            </button>
                                        ))}
                                    </div>
                                </div>

                                {/* Direction */}
                                <div>
                                    <label className="block text-sm text-gray-700 dark:text-gray-300 mb-1">
                                        Direction
                                    </label>
                                    <div className="flex flex-wrap gap-2">
                                        {[
                                            "normal",
                                            "reverse",
                                            "alternate",
                                            "alternate-reverse",
                                        ].map((dir) => (
                                            <button
                                                key={dir}
                                                type="button"
                                                className={`px-3 py-1 rounded-md text-xs ${
                                                    animationDirection === dir
                                                        ? "bg-primary dark:bg-primary-dark text-white"
                                                        : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                                                } transition-colors`}
                                                onClick={() =>
                                                    setAnimationDirection(dir)
                                                }>
                                                {dir.charAt(0).toUpperCase() +
                                                    dir.slice(1)}
                                            </button>
                                        ))}
                                    </div>
                                </div>

                                {/* Iterations */}
                                <div>
                                    <label className="block text-sm text-gray-700 dark:text-gray-300 mb-1">
                                        Iterations
                                    </label>
                                    <div className="flex flex-wrap gap-2">
                                        {[
                                            "1",
                                            "2",
                                            "3",
                                            "5",
                                            "10",
                                            "infinite",
                                        ].map((iter) => (
                                            <button
                                                key={iter}
                                                type="button"
                                                className={`px-3 py-1 rounded-md text-xs ${
                                                    animationIterations === iter
                                                        ? "bg-primary dark:bg-primary-dark text-white"
                                                        : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                                                } transition-colors`}
                                                onClick={() =>
                                                    setAnimationIterations(iter)
                                                }>
                                                {iter === "infinite"
                                                    ? "∞"
                                                    : iter}
                                            </button>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Code Output */}
                    <div className="mb-6">
                        <div className="flex justify-between items-center mb-2">
                            <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200">
                                Code Output
                            </h3>
                            <div className="flex space-x-2">
                                {Object.values(EXPORT_FORMATS).map((format) => (
                                    <button
                                        key={format}
                                        type="button"
                                        className={`px-3 py-1 rounded-md text-xs ${
                                            exportFormat === format
                                                ? "bg-primary dark:bg-primary-dark text-white"
                                                : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                                        } transition-colors`}
                                        onClick={() => setExportFormat(format)}>
                                        {format.toUpperCase()}
                                    </button>
                                ))}
                            </div>
                        </div>
                        <div className="relative">
                            <pre className="w-full h-48 p-4 overflow-auto border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-900 text-gray-800 dark:text-gray-200 font-mono text-sm">
                                {getOutputCode()}
                            </pre>
                            <button
                                onClick={copyToClipboard}
                                className="absolute top-2 right-2 px-3 py-1 bg-primary dark:bg-primary-dark text-white rounded-md text-sm hover:bg-primary-dark dark:hover:bg-primary transition-colors">
                                {copied ? "Copied!" : "Copy"}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </ToolContainer>
    );
}
