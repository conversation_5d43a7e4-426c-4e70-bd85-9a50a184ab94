import React, { useState, useEffect } from "react";
import { getToolById } from "../../data/tools.js";
import ToolHeader from "../ui/ToolHeader.jsx";
import ToolContainer from "../ui/ToolContainer.jsx";
import LucideIcon from "../ui/LucideIcon.jsx";

export default function RedesignedBorderGenerator() {
    const [toolData, setToolData] = useState(null);
    const [borderType, setBorderType] = useState("zigzag");
    const [borderSides, setBorderSides] = useState("bottom");
    const [borderSize, setBorderSize] = useState(20);
    const [borderColor, setBorderColor] = useState("#4F46E5");
    const [copied, setCopied] = useState(false);

    useEffect(() => {
        // Get the tool data
        const data = getToolById("bordergenerator");
        setToolData(data);
    }, []);

    // Border types
    const BORDER_TYPES = [
        { id: "zigzag", name: "Zig-Zag" },
        { id: "scalloped", name: "Scalloped" },
        { id: "wavy", name: "Wavy" },
        { id: "triangles", name: "Triangles" },
    ];

    // Border sides
    const BORDER_SIDES = [
        { id: "bottom", name: "Bottom" },
        { id: "top", name: "Top" },
        { id: "left", name: "Left" },
        { id: "right", name: "Right" },
    ];

    // Generate SVG path for the selected border type
    const generateSVGPath = () => {
        const size = borderSize;
        let path = "";
        let viewBox = "";
        let preserveAspectRatio = "";

        switch (borderType) {
            case "zigzag":
                if (borderSides === "top" || borderSides === "bottom") {
                    // Horizontal zigzag
                    viewBox = `0 0 ${size * 4} ${size}`;
                    preserveAspectRatio = "none";
                    // Create a closed path for filling
                    path = `M0,0 L${size},${size} L${size * 2},0 L${
                        size * 3
                    },${size} L${size * 4},0 L${size * 4},${size} L0,${size} Z`;
                } else {
                    // Vertical zigzag
                    viewBox = `0 0 ${size} ${size * 4}`;
                    preserveAspectRatio = "none";
                    // Create a closed path for filling
                    path = `M0,0 L${size},${size} L0,${size * 2} L${size},${
                        size * 3
                    } L0,${size * 4} L${size},${size * 4} L${size},0 Z`;
                }
                break;
            case "scalloped":
                if (borderSides === "top" || borderSides === "bottom") {
                    // Horizontal scalloped - multiple scallops
                    const numScallops = 4; // Number of scallops to show
                    const scallopsWidth = size * numScallops * 2;
                    viewBox = `0 0 ${scallopsWidth} ${size}`;
                    preserveAspectRatio = "none";

                    // Start path
                    let scallopsPath = `M0,${borderSides === "top" ? size : 0}`;

                    // Add each scallop
                    for (let i = 0; i < numScallops; i++) {
                        const startX = i * size * 2;
                        const midX = startX + size;
                        const endX = startX + size * 2;

                        if (borderSides === "top") {
                            // For top border, scallops curve upward
                            scallopsPath += ` A${size},${size} 0 0,1 ${midX},0 A${size},${size} 0 0,1 ${endX},${size}`;
                        } else {
                            // For bottom border, scallops curve downward
                            scallopsPath += ` A${size},${size} 0 0,0 ${midX},${size} A${size},${size} 0 0,0 ${endX},0`;
                        }
                    }

                    // Close the path for filling
                    scallopsPath +=
                        borderSides === "top"
                            ? ` L${scallopsWidth},${size} L0,${size} Z`
                            : ` L${scallopsWidth},0 L${scallopsWidth},${size} L0,${size} Z`;

                    path = scallopsPath;
                } else {
                    // Vertical scalloped - multiple scallops
                    const numScallops = 4; // Number of scallops to show
                    const scallopsHeight = size * numScallops * 2;
                    viewBox = `0 0 ${size} ${scallopsHeight}`;
                    preserveAspectRatio = "none";

                    // Start path
                    let scallopsPath = `M${
                        borderSides === "left" ? size : 0
                    },0`;

                    // Add each scallop
                    for (let i = 0; i < numScallops; i++) {
                        const startY = i * size * 2;
                        const midY = startY + size;
                        const endY = startY + size * 2;

                        if (borderSides === "left") {
                            // For left border, scallops curve to the left
                            scallopsPath += ` A${size},${size} 0 0,0 0,${midY} A${size},${size} 0 0,0 ${size},${endY}`;
                        } else {
                            // For right border, scallops curve to the right
                            scallopsPath += ` A${size},${size} 0 0,1 ${size},${midY} A${size},${size} 0 0,1 0,${endY}`;
                        }
                    }

                    // Close the path for filling
                    scallopsPath +=
                        borderSides === "left"
                            ? ` L${size},${scallopsHeight} L0,${scallopsHeight} L0,0 Z`
                            : ` L0,${scallopsHeight} L${size},${scallopsHeight} L${size},0 Z`;

                    path = scallopsPath;
                }
                break;
            case "wavy":
                if (borderSides === "top" || borderSides === "bottom") {
                    // Horizontal wavy - completely redesigned for smooth continuous waves
                    const waveWidth = size * 8; // Wider to show more waves
                    const waveHeight = size;
                    viewBox = `0 0 ${waveWidth} ${waveHeight}`;
                    preserveAspectRatio = "none";

                    // Create a continuous wave pattern
                    let wavePath = "";
                    const numWaves = 4; // Number of complete waves
                    const waveSegmentWidth = waveWidth / numWaves;

                    if (borderSides === "top") {
                        // For top border, waves should curve upward (away from the content) - INVERTED
                        wavePath = `M0,${waveHeight}`;

                        // Add each wave segment
                        for (let i = 0; i < numWaves; i++) {
                            const startX = i * waveSegmentWidth;
                            const quarterX = startX + waveSegmentWidth / 4;
                            const halfX = startX + waveSegmentWidth / 2;
                            const threeQuarterX =
                                startX + (waveSegmentWidth * 3) / 4;
                            const endX = startX + waveSegmentWidth;

                            // Create a smooth sine-like wave using cubic bezier curves
                            // For top border, the wave should rise up (away from the content)
                            wavePath += ` C${quarterX},${waveHeight} ${quarterX},0 ${halfX},0`;
                            wavePath += ` C${threeQuarterX},0 ${threeQuarterX},${waveHeight} ${endX},${waveHeight}`;
                        }

                        // Close the path for filling
                        wavePath += ` L${waveWidth},0 L0,0 Z`;
                    } else {
                        // For bottom border, waves should curve downward (away from the content) - INVERTED
                        wavePath = `M0,0`;

                        // Add each wave segment
                        for (let i = 0; i < numWaves; i++) {
                            const startX = i * waveSegmentWidth;
                            const quarterX = startX + waveSegmentWidth / 4;
                            const halfX = startX + waveSegmentWidth / 2;
                            const threeQuarterX =
                                startX + (waveSegmentWidth * 3) / 4;
                            const endX = startX + waveSegmentWidth;

                            // Create a smooth sine-like wave using cubic bezier curves
                            // For bottom border, the wave should dip down (away from the content)
                            wavePath += ` C${quarterX},0 ${quarterX},${waveHeight} ${halfX},${waveHeight}`;
                            wavePath += ` C${threeQuarterX},${waveHeight} ${threeQuarterX},0 ${endX},0`;
                        }

                        // Close the path for filling
                        wavePath += ` L${waveWidth},${waveHeight} L0,${waveHeight} Z`;
                    }

                    path = wavePath;
                } else {
                    // Vertical wavy - completely redesigned for smooth continuous waves
                    const waveWidth = size;
                    const waveHeight = size * 8; // Taller to show more waves
                    viewBox = `0 0 ${waveWidth} ${waveHeight}`;
                    preserveAspectRatio = "none";

                    // Create a continuous wave pattern
                    let wavePath = "";
                    const numWaves = 4; // Number of complete waves
                    const waveSegmentHeight = waveHeight / numWaves;

                    if (borderSides === "left") {
                        // For left border, waves should curve leftward (away from the content) - INVERTED
                        wavePath = `M${waveWidth},0`;

                        // Add each wave segment
                        for (let i = 0; i < numWaves; i++) {
                            const startY = i * waveSegmentHeight;
                            const quarterY = startY + waveSegmentHeight / 4;
                            const halfY = startY + waveSegmentHeight / 2;
                            const threeQuarterY =
                                startY + (waveSegmentHeight * 3) / 4;
                            const endY = startY + waveSegmentHeight;

                            // Create a smooth sine-like wave using cubic bezier curves
                            // For left border, the wave should curve to the left (away from the content)
                            wavePath += ` C${waveWidth},${quarterY} 0,${quarterY} 0,${halfY}`;
                            wavePath += ` C0,${threeQuarterY} ${waveWidth},${threeQuarterY} ${waveWidth},${endY}`;
                        }

                        // Close the path for filling
                        wavePath += ` L0,${waveHeight} L0,0 Z`;
                    } else {
                        // For right border, waves should curve rightward (away from the content) - INVERTED
                        wavePath = `M0,0`;

                        // Add each wave segment
                        for (let i = 0; i < numWaves; i++) {
                            const startY = i * waveSegmentHeight;
                            const quarterY = startY + waveSegmentHeight / 4;
                            const halfY = startY + waveSegmentHeight / 2;
                            const threeQuarterY =
                                startY + (waveSegmentHeight * 3) / 4;
                            const endY = startY + waveSegmentHeight;

                            // Create a smooth sine-like wave using cubic bezier curves
                            // For right border, the wave should curve to the right (away from the content)
                            wavePath += ` C0,${quarterY} ${waveWidth},${quarterY} ${waveWidth},${halfY}`;
                            wavePath += ` C${waveWidth},${threeQuarterY} 0,${threeQuarterY} 0,${endY}`;
                        }

                        // Close the path for filling
                        wavePath += ` L${waveWidth},${waveHeight} L${waveWidth},0 Z`;
                    }

                    path = wavePath;
                }
                break;
            case "triangles":
                if (borderSides === "top" || borderSides === "bottom") {
                    // Horizontal triangles - multiple triangles
                    const numTriangles = 4;
                    const trianglesWidth = size * numTriangles;
                    viewBox = `0 0 ${trianglesWidth} ${size}`;
                    preserveAspectRatio = "none";

                    // Start path
                    let trianglesPath = "";

                    // Add each triangle
                    for (let i = 0; i < numTriangles; i++) {
                        const startX = i * size;
                        const midX = startX + size / 2;
                        const endX = startX + size;

                        if (borderSides === "top") {
                            // For top border, triangles point up
                            trianglesPath += `M${startX},${size} L${midX},0 L${endX},${size} `;
                        } else {
                            // For bottom border, triangles point down
                            trianglesPath += `M${startX},0 L${midX},${size} L${endX},0 `;
                        }
                    }

                    // Close the path for filling
                    if (borderSides === "top") {
                        trianglesPath += `L${trianglesWidth},${size} L0,${size} Z`;
                    } else {
                        trianglesPath += `L${trianglesWidth},0 L${trianglesWidth},${size} L0,${size} L0,0 Z`;
                    }

                    path = trianglesPath;
                } else {
                    // Vertical triangles - multiple triangles
                    const numTriangles = 4;
                    const trianglesHeight = size * numTriangles;
                    viewBox = `0 0 ${size} ${trianglesHeight}`;
                    preserveAspectRatio = "none";

                    // Start path
                    let trianglesPath = "";

                    // Add each triangle
                    for (let i = 0; i < numTriangles; i++) {
                        const startY = i * size;
                        const midY = startY + size / 2;
                        const endY = startY + size;

                        if (borderSides === "left") {
                            // For left border, triangles point left
                            trianglesPath += `M${size},${startY} L0,${midY} L${size},${endY} `;
                        } else {
                            // For right border, triangles point right
                            trianglesPath += `M0,${startY} L${size},${midY} L0,${endY} `;
                        }
                    }

                    // Close the path for filling
                    if (borderSides === "left") {
                        trianglesPath += `L${size},${trianglesHeight} L0,${trianglesHeight} L0,0 L${size},0 Z`;
                    } else {
                        trianglesPath += `L0,${trianglesHeight} L${size},${trianglesHeight} L${size},0 L0,0 Z`;
                    }

                    path = trianglesPath;
                }
                break;
            default:
                // Default to zigzag
                if (borderSides === "top" || borderSides === "bottom") {
                    viewBox = `0 0 ${size * 4} ${size}`;
                    path = `M0,0 L${size},${size} L${size * 2},0 L${
                        size * 3
                    },${size} L${size * 4},0 L${size * 4},${size} L0,${size} Z`;
                } else {
                    viewBox = `0 0 ${size} ${size * 4}`;
                    path = `M0,0 L${size},${size} L0,${size * 2} L${size},${
                        size * 3
                    } L0,${size * 4} L${size},${size * 4} L${size},0 Z`;
                }
        }

        return { path, viewBox, preserveAspectRatio };
    };

    // Generate the SVG for the preview and CSS
    const generateSVG = () => {
        const { path, viewBox, preserveAspectRatio } = generateSVGPath();

        return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="${viewBox}" preserveAspectRatio="${preserveAspectRatio}">
  <path d="${path}" fill="${borderColor}" />
</svg>`;
    };

    // Get the CSS for the border
    const generateCSS = () => {
        const svg = generateSVG();
        const encodedSVG = encodeURIComponent(svg);
        const isHorizontal = borderSides === "top" || borderSides === "bottom";

        return `/* ${borderType.toUpperCase()} BORDER ON ${borderSides.toUpperCase()} SIDE */
/* Size: ${borderSize}px */

.${borderType}-${borderSides}-border {
  position: relative;
  ${borderSides === "top" ? `padding-top: ${borderSize + 10}px;` : ""}
  ${borderSides === "bottom" ? `padding-bottom: ${borderSize + 10}px;` : ""}
  ${borderSides === "left" ? `padding-left: ${borderSize + 10}px;` : ""}
  ${borderSides === "right" ? `padding-right: ${borderSize + 10}px;` : ""}
}

.${borderType}-${borderSides}-border::before {
  content: "";
  position: absolute;
  ${borderSides === "top" ? "top: 0;" : ""}
  ${borderSides === "bottom" ? "bottom: 0;" : ""}
  ${borderSides === "left" ? "left: 0;" : ""}
  ${borderSides === "right" ? "right: 0;" : ""}
  ${isHorizontal ? "width: 100%;" : `width: ${borderSize}px;`}
  ${isHorizontal ? `height: ${borderSize}px;` : "height: 100%;"}
  background-image: url("data:image/svg+xml,${encodedSVG}");
  background-repeat: repeat-${isHorizontal ? "x" : "y"};
  background-size: ${
      borderType === "wavy"
          ? isHorizontal
              ? `${borderSize * 8}px ${borderSize}px`
              : `${borderSize}px ${borderSize * 8}px`
          : isHorizontal
          ? `${borderSize * 2}px ${borderSize}px`
          : `${borderSize}px ${borderSize * 2}px`
  };
}

/* Example usage */
.example {
  ${borderType}-${borderSides}-border;
  /* Your other styles */
}`;
    };

    // Get preview style with the current border settings
    const getPreviewStyle = () => {
        const svg = generateSVG();
        const encodedSVG = encodeURIComponent(svg);
        const isHorizontal = borderSides === "top" || borderSides === "bottom";

        return {
            position: "relative",
            width: "100%",
            height: "200px",
            backgroundColor: "#f8fafc",
            border: "1px solid #e2e8f0",
            borderRadius: "0.5rem",
            overflow: "hidden",
            [`padding${
                borderSides.charAt(0).toUpperCase() + borderSides.slice(1)
            }`]: `${borderSize + 10}px`,
            [`border${
                borderSides.charAt(0).toUpperCase() + borderSides.slice(1)
            }`]: "none",
            "&::before": {
                content: '""',
                position: "absolute",
                [borderSides]: 0,
                [isHorizontal ? "width" : "height"]: "100%",
                [isHorizontal ? "height" : "width"]: `${borderSize}px`,
                backgroundImage: `url("data:image/svg+xml,${encodedSVG}")`,
                backgroundRepeat: `repeat-${isHorizontal ? "x" : "y"}`,
                backgroundSize: isHorizontal
                    ? `${borderSize * 2}px ${borderSize}px`
                    : `${borderSize}px ${borderSize * 2}px`,
            },
        };
    };

    // Copy CSS to clipboard
    const copyToClipboard = () => {
        navigator.clipboard.writeText(generateCSS()).then(() => {
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        });
    };

    // Sample presets
    const PRESETS = [
        {
            name: "Zigzag Bottom",
            type: "zigzag",
            sides: "bottom",
            size: 15,
            color: "#4F46E5",
        },
        {
            name: "Scalloped Top",
            type: "scalloped",
            sides: "top",
            size: 20,
            color: "#EF4444",
        },
        {
            name: "Wavy Left",
            type: "wavy",
            sides: "left",
            size: 12,
            color: "#10B981",
        },
        {
            name: "Triangles Right",
            type: "triangles",
            sides: "right",
            size: 18,
            color: "#F59E0B",
        },
    ];

    // Load a preset
    const loadPreset = (preset) => {
        setBorderType(preset.type);
        setBorderSides(preset.sides);
        setBorderSize(preset.size);
        setBorderColor(preset.color);
    };

    return (
        <ToolContainer
            className="py-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-colors duration-200"
            containerWidth={toolData?.containerWidth || "default"}>
            <ToolHeader
                toolData={toolData}
                defaultTitle="Border Generator"
                defaultIcon="Square"
                defaultDescription="Create custom CSS borders with different shapes like zig-zag, scalloped, wavy, or triangles. Customize the size, position, and get the CSS code."
            />

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Controls Panel */}
                <div className="space-y-6">
                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                            Border Type
                        </h2>
                        <div className="grid grid-cols-2 gap-3">
                            {BORDER_TYPES.map((type) => (
                                <button
                                    key={type.id}
                                    className={`p-3 rounded-lg border ${
                                        borderType === type.id
                                            ? "border-primary dark:border-primary-dark bg-primary/10 dark:bg-primary-dark/10"
                                            : "border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600"
                                    }`}
                                    onClick={() => setBorderType(type.id)}>
                                    <span className="text-sm font-medium">
                                        {type.name}
                                    </span>
                                </button>
                            ))}
                        </div>
                    </div>

                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                            Border Side
                        </h2>
                        <div className="grid grid-cols-2 gap-3">
                            {BORDER_SIDES.map((side) => (
                                <button
                                    key={side.id}
                                    className={`p-3 rounded-lg border ${
                                        borderSides === side.id
                                            ? "border-primary dark:border-primary-dark bg-primary/10 dark:bg-primary-dark/10"
                                            : "border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600"
                                    }`}
                                    onClick={() => setBorderSides(side.id)}>
                                    <span className="text-sm font-medium">
                                        {side.name}
                                    </span>
                                </button>
                            ))}
                        </div>
                    </div>

                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                            Border Properties
                        </h2>
                        <div className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Size (px)
                                </label>
                                <input
                                    type="range"
                                    min="5"
                                    max="50"
                                    value={borderSize}
                                    onChange={(e) =>
                                        setBorderSize(parseInt(e.target.value))
                                    }
                                    className="w-full"
                                />
                                <div className="flex justify-between text-sm text-gray-500 mt-1">
                                    <span>5px</span>
                                    <span>{borderSize}px</span>
                                    <span>50px</span>
                                </div>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Color
                                </label>
                                <input
                                    type="color"
                                    value={borderColor}
                                    onChange={(e) =>
                                        setBorderColor(e.target.value)
                                    }
                                    className="w-full h-10 rounded border border-gray-300 dark:border-gray-600"
                                />
                            </div>
                        </div>
                    </div>

                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                            Presets
                        </h2>
                        <div className="grid grid-cols-2 gap-2">
                            {PRESETS.map((preset, index) => (
                                <button
                                    key={index}
                                    className="p-2 text-left bg-white dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-600 rounded border border-gray-300 dark:border-gray-600 text-sm"
                                    onClick={() => loadPreset(preset)}>
                                    {preset.name}
                                </button>
                            ))}
                        </div>
                    </div>
                </div>

                {/* Preview and Code Panel */}
                <div className="space-y-6">
                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                            Preview
                        </h2>
                        <div className="w-full h-40 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-lg">
                            {/* Container with white color and border */}
                            <div
                                className="w-3/4 h-3/4 bg-white dark:bg-gray-800 rounded-lg flex items-center justify-center"
                                style={{
                                    position: "relative",
                                    // Apply the border to the specified side
                                    [`border${
                                        borderSides.charAt(0).toUpperCase() +
                                        borderSides.slice(1)
                                    }`]: "none",
                                    // Add some padding to make room for the border
                                    [`padding${
                                        borderSides.charAt(0).toUpperCase() +
                                        borderSides.slice(1)
                                    }`]: `${borderSize + 5}px`,
                                    // Add a regular border on other sides for better visualization
                                    border: "1px solid rgba(0,0,0,0.1)",
                                    [`border${
                                        borderSides.charAt(0).toUpperCase() +
                                        borderSides.slice(1)
                                    }`]: "none",
                                }}>
                                {/* Content inside the container */}
                                <div className="text-center text-gray-700 dark:text-gray-200 font-medium">
                                    <div className="flex flex-col items-center">
                                        <span className="text-sm mb-1">
                                            Border Type: {borderType}
                                        </span>
                                        <span className="text-sm">
                                            Side: {borderSides}
                                        </span>
                                    </div>
                                </div>

                                {/* The actual border element */}
                                <div
                                    className="absolute"
                                    style={{
                                        // Position the border on the correct side
                                        [borderSides]: 0,
                                        // Set the width/height to span the entire side
                                        [borderSides === "top" ||
                                        borderSides === "bottom"
                                            ? "left"
                                            : "top"]: 0,
                                        [borderSides === "top" ||
                                        borderSides === "bottom"
                                            ? "width"
                                            : "height"]: "100%",
                                        // Set the thickness of the border
                                        [borderSides === "top" ||
                                        borderSides === "bottom"
                                            ? "height"
                                            : "width"]: `${borderSize}px`,
                                        // Apply the SVG as a background image
                                        backgroundImage: `url("data:image/svg+xml,${encodeURIComponent(
                                            generateSVG()
                                        )}")`,
                                        // Repeat the pattern along the border
                                        backgroundRepeat:
                                            borderSides === "top" ||
                                            borderSides === "bottom"
                                                ? "repeat-x"
                                                : "repeat-y",
                                        // Size the background appropriately
                                        backgroundSize:
                                            borderType === "wavy"
                                                ? borderSides === "top" ||
                                                  borderSides === "bottom"
                                                    ? `${
                                                          borderSize * 8
                                                      }px ${borderSize}px`
                                                    : `${borderSize}px ${
                                                          borderSize * 8
                                                      }px`
                                                : borderSides === "top" ||
                                                  borderSides === "bottom"
                                                ? `${
                                                      borderSize * 2
                                                  }px ${borderSize}px`
                                                : `${borderSize}px ${
                                                      borderSize * 2
                                                  }px`,
                                    }}></div>
                            </div>
                        </div>
                    </div>

                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <div className="flex justify-between items-center mb-4">
                            <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
                                CSS Code
                            </h2>
                            <button
                                className="px-3 py-1 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors flex items-center"
                                onClick={copyToClipboard}>
                                <LucideIcon
                                    name={copied ? "Check" : "Copy"}
                                    size={16}
                                    className="mr-1"
                                />
                                {copied ? "Copied!" : "Copy Code"}
                            </button>
                        </div>
                        <pre className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-300 dark:border-gray-600 overflow-x-auto text-sm">
                            {generateCSS()}
                        </pre>
                    </div>
                </div>
            </div>
        </ToolContainer>
    );
}
