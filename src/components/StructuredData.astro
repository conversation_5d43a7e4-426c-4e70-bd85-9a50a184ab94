---
// StructuredData.astro
// This component adds JSON-LD structured data to pages

interface Props {
  type: 'website' | 'article' | 'product' | 'tool' | 'organization';
  data: any;
}

const { type, data } = Astro.props;

// Base organization data
const organizationData = {
  "@type": "Organization",
  "name": "DevBottle",
  "url": "https://devbottle.com",
  "logo": "https://devbottle.com/favicon.svg",
  "sameAs": [
    // Add social media profiles when available
    // "https://twitter.com/devbottle",
    // "https://github.com/devbottle"
  ]
};

// Generate the appropriate schema based on the type
let schema;

switch (type) {
  case 'website':
    schema = {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "url": data.url || "https://devbottle.com",
      "name": data.title || "DevBottle",
      "description": data.description || "A collection of simple, useful tools for developers.",
      "publisher": organizationData
    };
    break;
  
  case 'article':
    schema = {
      "@context": "https://schema.org",
      "@type": "Article",
      "headline": data.title,
      "description": data.description,
      "image": data.image,
      "datePublished": data.datePublished,
      "dateModified": data.dateModified || data.datePublished,
      "author": {
        "@type": "Person",
        "name": data.author
      },
      "publisher": organizationData,
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": data.url
      }
    };
    break;
  
  case 'tool':
    schema = {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": data.name,
      "description": data.description,
      "applicationCategory": "DeveloperApplication",
      "operatingSystem": "Any",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD"
      },
      "provider": organizationData
    };
    break;
  
  case 'organization':
    schema = {
      "@context": "https://schema.org",
      ...organizationData
    };
    break;
  
  default:
    schema = {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "url": data.url,
      "name": data.title,
      "description": data.description
    };
}
---

<script type="application/ld+json" set:html={JSON.stringify(schema)}></script>
