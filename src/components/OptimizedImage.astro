---
import { Image } from "astro:assets";

// Import all images from the assets directory
const images = import.meta.glob(
    "../assets/images/**/*.{jpeg,jpg,png,gif,webp,svg,avif}",
    { eager: true }
);

interface Props {
    src: string | ImageMetadata;
    alt: string;
    width?: number;
    height?: number;
    class?: string;
    format?: "webp" | "avif" | "png" | "jpg" | "jpeg";
    quality?: number;
    loading?: "eager" | "lazy";
}

const {
    src,
    alt,
    width,
    height,
    class: className = "",
    format = "webp",
    quality = 80,
    loading = "lazy",
} = Astro.props;

// Default dimensions for responsive images
const defaultWidth = 1200;
const defaultHeight = 630;

// For profile images, use smaller dimensions
const isProfile = typeof src === "string" && src.includes("profile");
const isSmallProfile =
    isProfile &&
    (className.includes("w-8") ||
        className.includes("h-8") ||
        className.includes("rounded-full"));

// Determine final dimensions
const finalWidth = isSmallProfile ? 80 : width || defaultWidth;
const finalHeight = isSmallProfile ? 80 : height || defaultHeight;

// Handle different types of src inputs
let imageSrc;
let isRemote = false;

if (typeof src === "string") {
    // Check if it's a remote URL
    isRemote = src.startsWith("http");

    if (isRemote) {
        // For remote images, we'll use the standard img tag
        imageSrc = src;
    } else {
        // For local images, check if it exists in the glob import
        const imagePath = `../assets/images/${src}`;
        if (images[imagePath]) {
            imageSrc = images[imagePath].default;
        } else {
            // Fallback to public directory
            imageSrc = `/${src}`;
            isRemote = true;
        }
    }
} else {
    // If src is already an imported image, use it directly
    imageSrc = src;
}
---

{
    isRemote ? (
        <img
            src={imageSrc}
            alt={alt}
            width={finalWidth}
            height={finalHeight}
            class={className}
            loading={loading}
        />
    ) : (
        <Image
            src={imageSrc}
            alt={alt}
            width={finalWidth}
            height={finalHeight}
            class={className}
            format={format}
            quality={quality}
            loading={loading}
        />
    )
}
