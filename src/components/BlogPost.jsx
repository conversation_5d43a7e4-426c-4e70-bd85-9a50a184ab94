import React from 'react';
import BlogPostImage from './BlogPostImage';

export default function BlogPost({
  title,
  category,
  excerpt,
  image,
  author,
  authorImage,
  date,
  slug
}) {
  // If slug is a full URL path (from Astro.url), use it directly
  const postUrl = slug.startsWith('/') ? slug : `/blog/${slug}`;
  return (
    <div className="flex flex-wrap lg:items-center -m-4">
      <div className="w-auto p-4">
        <div className="overflow-hidden rounded-xl">
          <BlogPostImage
            className="transform hover:scale-105 transition ease-in-out duration-1000 w-32 h-32 md:w-40 md:h-40 object-cover"
            src={image}
            alt={title}
          />
        </div>
      </div>
      <div className="flex-1 p-4">
        <div className="md:max-w-xs">
          <div className="flex flex-col justify-between h-full">
            <div className="mb-6">
              <p className="mb-1.5 text-sm text-gray-500 dark:text-gray-400 font-medium uppercase tracking-px">{category}</p>
              <a className="inline-block hover:text-primary dark:hover:text-primary-dark hover:underline" href={postUrl}>
                <h3 className="text-xl font-semibold leading-normal">{title}</h3>
              </a>
              {excerpt && (
                <p className="mt-2 text-gray-600 dark:text-gray-400 text-sm line-clamp-2">{excerpt}</p>
              )}
            </div>
            <div className="flex flex-wrap items-center -m-1">
              {authorImage && (
                <div className="w-auto p-1">
                  <BlogPostImage src={authorImage} alt={author} className="w-8 h-8 rounded-full" />
                </div>
              )}
              <div className="w-auto p-1">
                <p className="text-sm font-semibold leading-relaxed">{author}</p>
                {date && <p className="text-xs text-gray-500 dark:text-gray-400">{date}</p>}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
