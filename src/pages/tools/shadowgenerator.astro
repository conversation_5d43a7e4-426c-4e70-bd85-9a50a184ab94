---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import ShadowGeneratorTool from "../../components/tools/ShadowGeneratorTool.jsx";
const pageTitle = "CSS Shadow Generator | DevBottle";
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="CSS Shadow Generator - Create beautiful shadows"
        description="Create beautiful CSS shadows with a visual editor. Includes box-shadow and filter shadow options."
        openGraph={{
            basic: {
                title: "CSS Shadow Generator - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-shadowgenerator.jpg",
                url: "https://devbottle.com/tools/shadowgenerator",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Create beautiful CSS shadows with a visual editor. Includes box-shadow and filter shadow options.",
            },
        }}
    />

    <ShadowGeneratorTool client:load />
</ToolLayout>
