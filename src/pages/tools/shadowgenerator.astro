---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import ShadowGeneratorTool from "../../components/tools/ShadowGeneratorTool.jsx";
const pageTitle = "CSS Shadow Generator | DevBottle";
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="CSS Shadow Generator - Create beautiful shadows"
        description="Create beautiful CSS shadows with a visual editor. Includes box-shadow and filter shadow options."
        openGraph={{
            basic: {
                title: "CSS Shadow Generator - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-shadowgenerator.jpg",
                url: "https://devbottle.com/tools/shadowgenerator",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Create beautiful CSS shadows with a visual editor. Includes box-shadow and filter shadow options.",
            },
        }}
    />

    <ShadowGeneratorTool client:load />

    <div class="container mx-auto px-4 py-12 max-w-4xl">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mt-8">
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">About the CSS Shadow Generator</h2>

            <p class="text-gray-700 dark:text-gray-300 mb-4">
                The CSS Shadow Generator is a visual tool that helps you create beautiful, customized shadow effects for your web projects. Whether you're designing buttons, cards, modals, or other UI elements, this tool makes it easy to craft the perfect shadow without writing CSS code from scratch.
            </p>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">How the Shadow Generator Works</h3>

            <p class="text-gray-700 dark:text-gray-300 mb-4">
                Our Shadow Generator provides an intuitive interface to adjust all aspects of CSS shadows:
            </p>

            <ol class="list-decimal pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li>Horizontal offset (X) - controls the shadow's position left or right of the element</li>
                <li>Vertical offset (Y) - controls the shadow's position above or below the element</li>
                <li>Blur radius - determines how blurry or sharp the shadow appears</li>
                <li>Spread radius - expands or contracts the shadow size</li>
                <li>Color and opacity - customize the shadow's appearance and intensity</li>
                <li>Inset option - creates inner shadows instead of outer shadows</li>
            </ol>

            <p class="text-gray-700 dark:text-gray-300 mb-4">
                As you adjust these parameters, you'll see a real-time preview of your shadow. Once you're satisfied with the result, you can copy the generated CSS code in various formats (CSS, JavaScript, or Tailwind) for immediate use in your projects.
            </p>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Types of CSS Shadows</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Box Shadow</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        The CSS <code class="bg-gray-200 dark:bg-gray-600 px-1 rounded">box-shadow</code> property adds shadow effects around an element's frame. You can create multiple shadows with different properties, and even create inner shadows using the inset keyword.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Drop Shadow</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        The CSS <code class="bg-gray-200 dark:bg-gray-600 px-1 rounded">filter: drop-shadow()</code> function creates a drop shadow that follows the alpha channel of the element, making it ideal for irregular shapes like icons or images with transparency.
                    </p>
                </div>
            </div>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Popular Shadow Styles</h3>

            <ul class="list-disc pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li><strong>Material Design shadows</strong> - Layered shadows that create a sense of elevation</li>
                <li><strong>Soft shadows</strong> - Subtle, diffused shadows for a gentle depth effect</li>
                <li><strong>Hard shadows</strong> - Sharp, defined shadows for a more dramatic look</li>
                <li><strong>Neumorphism</strong> - Combines inner and outer shadows to create a soft, extruded appearance</li>
                <li><strong>Floating effect</strong> - Shadows that make elements appear to float above the page</li>
            </ul>

            <div class="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg mt-6">
                <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">Pro Tip</h3>
                <p class="text-blue-700 dark:text-blue-300">
                    For the most realistic shadows, try using multiple shadow layers with different properties. Light in the real world creates complex shadows, and combining several subtle shadows can create more natural-looking depth than a single strong shadow.
                </p>
            </div>
        </div>
    </div>
</ToolLayout>
