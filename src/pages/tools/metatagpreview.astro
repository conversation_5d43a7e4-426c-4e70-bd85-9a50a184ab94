---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import MetaTagPreview from "../../components/tools/MetaTagPreview.jsx";
import StructuredData from "../../components/StructuredData.astro";
import { getToolById } from "../../data/tools.js";

const toolData = getToolById("metatagpreview");
const pageTitle = `${toolData?.name || "Meta Tag Preview"} | DevBottle`;
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="Meta Tag Preview - See how your website appears on social media"
        description="Visualize and analyze meta tags, Open Graph tags, and Twitter Card tags from any URL. Preview how your website appears when shared on social media platforms."
        openGraph={{
            basic: {
                title: "Meta Tag Preview - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-metatagpreview.jpg",
                url: "https://devbottle.com/tools/metatagpreview",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Visualize and analyze meta tags, Open Graph tags, and Twitter Card tags from any URL.",
            },
        }}
    />
    <StructuredData
        slot="head"
        type="tool"
        data={{
            name: toolData?.name || "Meta Tag Preview",
            description:
                toolData?.description ||
                "Visualize and analyze meta tags, Open Graph tags, and Twitter Card tags from any URL.",
        }}
    />

    <MetaTagPreview client:load />
</ToolLayout>
