---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import CssUnitConverter from "../../components/tools/CssUnitConverter.jsx";
import StructuredData from "../../components/StructuredData.astro";
import { getToolById } from "../../data/tools.js";

// Get tool data from the tools.js file
const tool = getToolById("cssunitconverter");
const pageTitle = `${tool?.name || "CSS Unit Converter"} | DevBottle`;
---

<ToolLayout>
    <SEO
        slot="head"
        title={pageTitle}
        description={tool?.description || "Convert pixels to rem and em units with customizable base font sizes. Essential for responsive web design and accessibility."}
        canonical="https://devbottle.com/tools/cssunitconverter"
        openGraph={{
            basic: {
                title: pageTitle,
                type: "website",
                image: "https://devbottle.com/images/og-cssunitconverter.jpg",
                url: "https://devbottle.com/tools/cssunitconverter",
            },
            optional: {
                description: tool?.description || "Convert pixels to rem and em units with customizable base font sizes. Essential for responsive web design and accessibility.",
                siteName: "DevBottle",
            },
        }}
        twitter={{
            creator: "@devbottle",
            site: "@devbottle",
            card: "summary_large_image",
        }}
    />
    <StructuredData
        slot="head"
        type="website"
        data={{
            url: "https://devbottle.com/tools/cssunitconverter",
            title: "CSS Unit Converter - DevBottle",
            description:
                "Convert pixels to rem and em units for responsive CSS. Perfect for converting Figma designs to scalable web layouts with customizable base font sizes.",
        }}
    />

    <CssUnitConverter client:idle />

    <!-- SEO Content Section -->
    <div class="container mx-auto px-4 py-12 seo-content">
        <div class="max-w-4xl mx-auto prose prose-lg dark:prose-invert">
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-6">
                About CSS Unit Converter
            </h2>
            <p class="text-gray-600 dark:text-gray-400 mb-6">
                The CSS Unit Converter is an essential tool for modern web developers who need to convert pixel values from design tools like Figma, Sketch, or Adobe XD into responsive rem and em units. This conversion is crucial for creating accessible, scalable web designs that adapt beautifully across different devices and user preferences.
            </p>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                How CSS Unit Conversion Works
            </h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
                CSS unit conversion is based on a simple mathematical relationship between pixels and relative units:
            </p>
            <ul class="list-disc list-inside text-gray-600 dark:text-gray-400 mb-6 space-y-2">
                <li><strong>REM units:</strong> Relative to the root element's font size (typically 16px)</li>
                <li><strong>EM units:</strong> Relative to the parent element's font size</li>
                <li><strong>Conversion formula:</strong> rem/em = pixels ÷ base font size</li>
                <li><strong>Example:</strong> 24px ÷ 16px = 1.5rem</li>
            </ul>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                Key Features
            </h3>
            <div class="grid md:grid-cols-2 gap-6 mb-8">
                <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Single Conversion</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        Convert individual pixel values to rem and em units with instant results and one-click copying.
                    </p>
                </div>
                <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Batch Processing</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        Paste entire CSS blocks and convert all pixel values at once, perfect for large design systems.
                    </p>
                </div>
                <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Customizable Base Size</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        Adjust the base font size to match your project's root font size for accurate conversions.
                    </p>
                </div>
                <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Conversion Table</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        Quick reference table for common design measurements and typography scales.
                    </p>
                </div>
            </div>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                Common Use Cases
            </h3>
            <ul class="list-disc list-inside text-gray-600 dark:text-gray-400 mb-6 space-y-2">
                <li><strong>Figma to CSS:</strong> Convert design measurements to responsive units</li>
                <li><strong>Typography scaling:</strong> Create consistent font size hierarchies</li>
                <li><strong>Spacing systems:</strong> Convert margins and padding to scalable units</li>
                <li><strong>Component dimensions:</strong> Make UI components responsive and accessible</li>
                <li><strong>Design system migration:</strong> Convert existing pixel-based designs to rem/em</li>
            </ul>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                Best Practices for CSS Units
            </h3>
            <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mb-6">
                <h4 class="font-semibold text-blue-800 dark:text-blue-200 mb-2">When to Use REM vs EM</h4>
                <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                    <li><strong>Use REM for:</strong> Font sizes, margins, padding, and layout dimensions</li>
                    <li><strong>Use EM for:</strong> Component-specific spacing that should scale with the component</li>
                    <li><strong>Use PX for:</strong> Borders, box shadows, and elements that shouldn't scale</li>
                </ul>
            </div>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                Pro Tips for Responsive Design
            </h3>
            <div class="space-y-4 mb-8">
                <div class="border-l-4 border-primary dark:border-primary-dark pl-4">
                    <p class="text-gray-600 dark:text-gray-400">
                        <strong>Accessibility First:</strong> Using rem units ensures your design respects user font size preferences, improving accessibility for users with visual impairments.
                    </p>
                </div>
                <div class="border-l-4 border-primary dark:border-primary-dark pl-4">
                    <p class="text-gray-600 dark:text-gray-400">
                        <strong>Consistent Base:</strong> Stick to a 16px base font size unless you have specific requirements, as it's the browser default and most accessible.
                    </p>
                </div>
                <div class="border-l-4 border-primary dark:border-primary-dark pl-4">
                    <p class="text-gray-600 dark:text-gray-400">
                        <strong>Design System Integration:</strong> Use this tool to create consistent spacing and typography scales that work across your entire design system.
                    </p>
                </div>
            </div>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                Conversion Examples
            </h3>
            <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg mb-6">
                <div class="font-mono text-sm space-y-2">
                    <div class="grid grid-cols-3 gap-4 font-semibold text-gray-800 dark:text-white border-b pb-2">
                        <span>Pixels</span>
                        <span>REM (16px base)</span>
                        <span>Use Case</span>
                    </div>
                    <div class="grid grid-cols-3 gap-4 text-gray-600 dark:text-gray-400">
                        <span>8px</span>
                        <span>0.5rem</span>
                        <span>Small spacing</span>
                    </div>
                    <div class="grid grid-cols-3 gap-4 text-gray-600 dark:text-gray-400">
                        <span>16px</span>
                        <span>1rem</span>
                        <span>Base font size</span>
                    </div>
                    <div class="grid grid-cols-3 gap-4 text-gray-600 dark:text-gray-400">
                        <span>24px</span>
                        <span>1.5rem</span>
                        <span>Heading small</span>
                    </div>
                    <div class="grid grid-cols-3 gap-4 text-gray-600 dark:text-gray-400">
                        <span>32px</span>
                        <span>2rem</span>
                        <span>Heading medium</span>
                    </div>
                </div>
            </div>

            <p class="text-gray-600 dark:text-gray-400">
                Start converting your pixel-based designs to responsive, accessible CSS units today. Simply enter your pixel values above and get instant rem and em conversions that will make your web designs more flexible and user-friendly.
            </p>
        </div>
    </div>
</ToolLayout>
