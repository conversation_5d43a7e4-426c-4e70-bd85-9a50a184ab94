---
import ToolLayout from "../../layouts/ToolLayout.astro";
import TestComponent from "../../components/tools/TestComponent.jsx";
import NewBorderGenerator from "../../components/tools/NewBorderGenerator.jsx";
---

<ToolLayout title="Test Page | DevBottle">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-6">Test Page</h1>

        <div class="mb-8">
            <p class="text-lg mb-4">This is a static content in the Astro file.</p>
        </div>

        <div class="mb-8">
            <h2 class="text-2xl font-semibold mb-4">Test Component:</h2>
            <TestComponent client:load />
        </div>

        <div class="mb-8">
            <h2 class="text-2xl font-semibold mb-4">New Border Generator:</h2>
            <NewBorderGenerator client:load />
        </div>
    </div>
</ToolLayout>
