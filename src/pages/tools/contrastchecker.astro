---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import ContrastChecker from "../../components/tools/ContrastChecker.jsx";
const pageTitle = "Color Contrast Checker | DevBottle";
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="Color Contrast Checker - WCAG compliance tool"
        description="Check color contrast ratios for WCAG compliance and get accessible color suggestions for your web designs."
        openGraph={{
            basic: {
                title: "Color Contrast Checker - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-contrastchecker.jpg",
                url: "https://devbottle.com/tools/contrastchecker",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Check color contrast ratios for WCAG compliance and get accessible color suggestions for your web designs.",
            },
        }}
    />

    <ContrastChecker client:load />
</ToolLayout>
