---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import ContrastChecker from "../../components/tools/ContrastChecker.jsx";
const pageTitle = "Color Contrast Checker | DevBottle";
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="Color Contrast Checker - WCAG compliance tool"
        description="Check color contrast ratios for WCAG compliance and get accessible color suggestions for your web designs."
        openGraph={{
            basic: {
                title: "Color Contrast Checker - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-contrastchecker.jpg",
                url: "https://devbottle.com/tools/contrastchecker",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Check color contrast ratios for WCAG compliance and get accessible color suggestions for your web designs.",
            },
        }}
    />

    <ContrastChecker client:load />

    <div class="container mx-auto px-4 py-12 seo-content">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mt-8">
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">About the Color Contrast Checker</h2>

            <p class="text-gray-700 dark:text-gray-300 mb-4">
                The Color Contrast Checker is an essential tool for web designers and developers who want to create accessible websites. It helps ensure that text is readable against its background by measuring the contrast ratio according to Web Content Accessibility Guidelines (WCAG).
            </p>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Why Color Contrast Matters</h3>

            <p class="text-gray-700 dark:text-gray-300 mb-4">
                Proper color contrast is crucial for several reasons:
            </p>

            <ul class="list-disc pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li><strong>Accessibility</strong> - Ensures content is readable for people with visual impairments, including color blindness and low vision</li>
                <li><strong>Legal compliance</strong> - Many countries require websites to meet accessibility standards, especially for government and public service sites</li>
                <li><strong>Improved usability</strong> - Better contrast benefits all users, especially in challenging viewing conditions like bright sunlight</li>
                <li><strong>Professional appearance</strong> - Properly contrasted text looks more polished and professional</li>
            </ul>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Understanding WCAG Contrast Requirements</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">AA Level (Minimum)</h4>
                    <ul class="list-disc pl-5 text-gray-700 dark:text-gray-300 space-y-1">
                        <li>Normal text: 4.5:1 ratio</li>
                        <li>Large text (18pt+): 3:1 ratio</li>
                        <li>UI components and graphics: 3:1 ratio</li>
                    </ul>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">AAA Level (Enhanced)</h4>
                    <ul class="list-disc pl-5 text-gray-700 dark:text-gray-300 space-y-1">
                        <li>Normal text: 7:1 ratio</li>
                        <li>Large text (18pt+): 4.5:1 ratio</li>
                    </ul>
                </div>
            </div>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">How the Contrast Checker Works</h3>

            <ol class="list-decimal pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li>Select foreground (text) and background colors using the color pickers</li>
                <li>The tool calculates the contrast ratio using the WCAG formula</li>
                <li>Results show whether your colors pass AA and AAA standards for different text sizes</li>
                <li>If your colors don't meet standards, the tool suggests alternative colors that maintain your design aesthetic while improving accessibility</li>
                <li>Preview your text with the selected colors to see how it will look on your website</li>
            </ol>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Tips for Accessible Color Combinations</h3>

            <ul class="list-disc pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li>Dark text on light backgrounds (or vice versa) typically provides the best contrast</li>
                <li>Avoid placing text on busy backgrounds or images without a solid overlay</li>
                <li>Don't rely solely on color to convey information (use icons, patterns, or text labels as well)</li>
                <li>Test your design in grayscale to ensure it works for users with color blindness</li>
                <li>Consider using our Color Palette Generator to create accessible color schemes from the start</li>
            </ul>

            <div class="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg mt-6">
                <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">Pro Tip</h3>
                <p class="text-blue-700 dark:text-blue-300">
                    When designing with brand colors that don't meet contrast requirements, consider using those colors for non-text elements like borders or accents, while using more accessible colors for text and critical UI elements.
                </p>
            </div>
        </div>
    </div>
</ToolLayout>
