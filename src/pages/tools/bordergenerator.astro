---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import ExactBorderGenerator from "../../components/tools/BorderGenerator.jsx";
import StructuredData from "../../components/StructuredData.astro";
import { getToolById } from "../../data/tools.js";

const toolData = getToolById("bordergenerator");
const pageTitle = `${toolData?.name || "Border Generator"} | DevBottle`;
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="Border Generator - Create custom CSS borders with different shapes"
        description="Create custom CSS borders with different shapes like zig-zag, scooped, scalloped, or wavy. Customize the size, position, and get the CSS code."
        canonical="https://devbottle.com/tools/bordergenerator"
        openGraph={{
            basic: {
                title: "Border Generator - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-bordergenerator.jpg",
                url: "https://devbottle.com/tools/bordergenerator",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Create custom CSS borders with different shapes like zig-zag, scooped, scalloped, or wavy. Customize the size, position, and get the CSS code.",
            },
        }}
    />
    <StructuredData
        slot="head"
        type="tool"
        data={{
            name: toolData?.name || "Border Generator",
            description:
                toolData?.description ||
                "Create custom CSS borders with different shapes like zig-zag, scooped, scalloped, or wavy. Customize the size, position, and get the CSS code.",
        }}
    />

    <ExactBorderGenerator client:load />
</ToolLayout>
