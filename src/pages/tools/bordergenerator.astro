---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import ExactBorderGenerator from "../../components/tools/BorderGenerator.jsx";
import StructuredData from "../../components/StructuredData.astro";
import { getToolById } from "../../data/tools.js";

const toolData = getToolById("bordergenerator");
const pageTitle = `${toolData?.name || "Border Generator"} | DevBottle`;
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="Border Generator - Create custom CSS borders with different shapes"
        description="Create custom CSS borders with different shapes like zig-zag, scooped, scalloped, or wavy. Customize the size, position, and get the CSS code."
        canonical="https://devbottle.com/tools/bordergenerator"
        openGraph={{
            basic: {
                title: "Border Generator - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-bordergenerator.jpg",
                url: "https://devbottle.com/tools/bordergenerator",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Create custom CSS borders with different shapes like zig-zag, scooped, scalloped, or wavy. Customize the size, position, and get the CSS code.",
            },
        }}
    />
    <StructuredData
        slot="head"
        type="tool"
        data={{
            name: toolData?.name || "Border Generator",
            description:
                toolData?.description ||
                "Create custom CSS borders with different shapes like zig-zag, scooped, scalloped, or wavy. Customize the size, position, and get the CSS code.",
        }}
    />

    <ExactBorderGenerator client:load />

    <div class="container mx-auto px-4 py-12 max-w-4xl">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mt-8">
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">About the Border Generator</h2>

            <p class="text-gray-700 dark:text-gray-300 mb-4">
                The Border Generator helps you create custom CSS borders with unique shapes and styles that go beyond the standard rectangular borders. With this tool, you can design decorative borders like zig-zag, scalloped, wavy, and more for your web projects.
            </p>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">How CSS Border Shapes Work</h3>

            <p class="text-gray-700 dark:text-gray-300 mb-4">
                Modern CSS techniques allow for creating non-rectangular borders using CSS masks, clip-paths, and SVG patterns. Our Border Generator uses these techniques to create various border styles:
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">CSS Masks</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        CSS masks use image or gradient sources to create transparency effects. The Border Generator uses mask-image with SVG patterns to create decorative borders while maintaining the element's content and functionality.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Border Positioning</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        You can apply custom borders to any side of an element (top, right, bottom, left) or to all sides. Each border can have its own style, size, and color for maximum design flexibility.
                    </p>
                </div>
            </div>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Border Styles Available</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Zig-Zag Borders</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Creates a sawtooth pattern along the border edge. Perfect for creating a torn paper effect, price tags, or adding visual interest to containers.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Scalloped Borders</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Creates a series of semicircles along the border edge. Ideal for decorative containers, cards with a vintage feel, or creating a softer edge appearance.
                    </p>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Wavy Borders</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Creates a smooth, undulating pattern along the border edge. Perfect for water-themed designs, creating a relaxed feel, or adding organic shapes to your layout.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Scooped Borders</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Creates concave curves along the border edge. Great for creating a unique container shape, highlighting content, or adding a modern design element.
                    </p>
                </div>
            </div>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Features of the Border Generator</h3>

            <ul class="list-disc pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li><strong>Multiple border styles</strong> - Choose from various decorative border patterns</li>
                <li><strong>Customizable properties</strong> - Adjust size, color, position, and other attributes</li>
                <li><strong>Live preview</strong> - See your border design update in real-time</li>
                <li><strong>Cross-browser compatibility</strong> - Generated code works across modern browsers</li>
                <li><strong>Copy-ready CSS</strong> - Get the complete CSS code to implement your custom border</li>
                <li><strong>Border positioning</strong> - Apply borders to specific sides or all sides of an element</li>
            </ul>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">How to Use the Border Generator</h3>

            <ol class="list-decimal pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li>Select a border style (zig-zag, scalloped, wavy, etc.)</li>
                <li>Choose which sides of the element should have the border</li>
                <li>Customize the border properties (size, color, etc.)</li>
                <li>Preview your design in real-time</li>
                <li>Copy the generated CSS code</li>
                <li>Implement the code in your web project</li>
            </ol>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Creative Uses for Custom Borders</h3>

            <ul class="list-disc pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li>Creating decorative content cards or containers</li>
                <li>Designing unique section dividers for long-form content</li>
                <li>Adding visual interest to image galleries or portfolios</li>
                <li>Creating themed designs (e.g., wavy borders for ocean-themed sites)</li>
                <li>Highlighting important information or call-to-action elements</li>
                <li>Designing custom coupon or ticket-style elements</li>
            </ul>

            <div class="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg mt-6">
                <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">Pro Tip</h3>
                <p class="text-blue-700 dark:text-blue-300">
                    For the best performance, consider using these decorative borders sparingly on your page. While modern browsers handle CSS masks well, applying too many complex borders can impact rendering performance, especially on mobile devices.
                </p>
            </div>
        </div>
    </div>
</ToolLayout>
