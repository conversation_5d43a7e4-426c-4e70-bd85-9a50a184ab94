---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import CriticalCssExtractor from "../../components/tools/CriticalCssExtractor.jsx";
const pageTitle = "Critical CSS Extractor | DevBottle";
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="Critical CSS Extractor - Improve page load times"
        description="Extract critical CSS from your HTML and CSS to improve page load times and performance."
        openGraph={{
            basic: {
                title: "Critical CSS Extractor - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-criticalcss.jpg",
                url: "https://devbottle.com/tools/criticalcss",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Extract critical CSS from your HTML and CSS to improve page load times and performance.",
            },
        }}
    />

    <CriticalCssExtractor client:load />

    <div class="container mx-auto px-4 py-12 seo-content">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mt-8">
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">About the Critical CSS Extractor</h2>

            <p class="text-gray-700 dark:text-gray-300 mb-4">
                The Critical CSS Extractor helps you identify and separate the CSS needed for above-the-fold content on your web pages. By extracting and inlining this critical CSS, you can significantly improve page load times and Core Web Vitals scores.
            </p>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">What is Critical CSS?</h3>

            <p class="text-gray-700 dark:text-gray-300 mb-4">
                Critical CSS is the minimum set of CSS rules needed to render the above-the-fold content of a webpage (the portion visible without scrolling). By inlining this CSS in the <code class="bg-gray-200 dark:bg-gray-600 px-1 rounded">&lt;head&gt;</code> of your HTML document, you can render the visible portion of your page quickly while the rest of your CSS loads asynchronously.
            </p>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Why Critical CSS Matters</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Faster Perceived Load Times</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        By inlining critical CSS, users see the above-the-fold content almost immediately, creating the perception that your site loads quickly even before all resources are fully loaded.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Improved Core Web Vitals</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Critical CSS directly improves metrics like First Contentful Paint (FCP) and Largest Contentful Paint (LCP), which are key factors in Google's page experience ranking signals.
                    </p>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Eliminate Render-Blocking Resources</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        External CSS files are render-blocking by default, meaning the browser pauses rendering until they're downloaded. Critical CSS allows you to load your main CSS files asynchronously.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Better Mobile Performance</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        On slower mobile connections, critical CSS makes an even bigger difference, ensuring users don't abandon your site while waiting for it to render.
                    </p>
                </div>
            </div>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">How the Critical CSS Extractor Works</h3>

            <ol class="list-decimal pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li>Paste your HTML code in the HTML input area</li>
                <li>Paste your full CSS in the CSS input area</li>
                <li>The tool analyzes which HTML elements are likely to appear above the fold</li>
                <li>It extracts only the CSS rules that apply to those elements</li>
                <li>The result is a minimal set of CSS rules needed for initial rendering</li>
            </ol>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">How to Implement Critical CSS</h3>

            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-6">
                <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Basic Implementation</h4>
                <pre class="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm overflow-x-auto"><code>&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
  &lt;!-- Inline critical CSS --&gt;
  &lt;style&gt;
    /* Paste your critical CSS here */
  &lt;/style>

  &lt;!-- Load full CSS asynchronously --&gt;
  &lt;link rel="preload" href="styles.css" as="style" onload="this.onload=null;this.rel='stylesheet'"&gt;
  &lt;noscript&gt;&lt;link rel="stylesheet" href="styles.css"&gt;&lt;/noscript&gt;
&lt;/head&gt;
&lt;body&gt;
  &lt;!-- Your content --&gt;
&lt;/body&gt;
&lt;/html&gt;</code></pre>
            </div>

            <div class="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg mt-6">
                <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">Pro Tip</h3>
                <p class="text-blue-700 dark:text-blue-300">
                    Critical CSS should be regenerated whenever you make significant changes to your site's layout or styling. Consider automating this process in your build pipeline using tools like Critical, CriticalCSS, or Penthouse.
                </p>
            </div>
        </div>
    </div>
</ToolLayout>
