---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import CriticalCssExtractor from "../../components/tools/CriticalCssExtractor.jsx";
const pageTitle = "Critical CSS Extractor | DevBottle";
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="Critical CSS Extractor - Improve page load times"
        description="Extract critical CSS from your HTML and CSS to improve page load times and performance."
        openGraph={{
            basic: {
                title: "Critical CSS Extractor - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-criticalcss.jpg",
                url: "https://devbottle.com/tools/criticalcss",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Extract critical CSS from your HTML and CSS to improve page load times and performance.",
            },
        }}
    />

    <CriticalCssExtractor client:load />
</ToolLayout>
