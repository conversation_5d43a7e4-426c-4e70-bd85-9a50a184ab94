---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import GradientGenerator from "../../components/tools/GradientGenerator.jsx";
import StructuredData from "../../components/StructuredData.astro";
import { getToolById } from "../../data/tools.js";

const toolData = getToolById("gradientgenerator");
const pageTitle = `${toolData?.name || "Gradient Generator"} | DevBottle`;
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="Gradient Generator - Create beautiful CSS gradients"
        description="Create beautiful CSS gradients with animation capabilities for your web projects. Design linear, radial, and conic gradients with a visual editor."
        canonical="https://devbottle.com/tools/gradientgenerator"
        openGraph={{
            basic: {
                title: "Gradient Generator - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-gradientgenerator.jpg",
                url: "https://devbottle.com/tools/gradientgenerator",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Create beautiful CSS gradients with animation capabilities for your web projects. Design linear, radial, and conic gradients with a visual editor.",
            },
        }}
    />
    <StructuredData
        slot="head"
        type="tool"
        data={{
            name: toolData?.name || "Gradient Generator",
            description:
                toolData?.description ||
                "Design linear and radial gradients with a visual editor, add animations, and export ready-to-use CSS code.",
        }}
    />

    <GradientGenerator client:load />
</ToolLayout>
