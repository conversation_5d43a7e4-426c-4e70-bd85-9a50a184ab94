---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import GradientGenerator from "../../components/tools/GradientGenerator.jsx";
import StructuredData from "../../components/StructuredData.astro";
import { getToolById } from "../../data/tools.js";

const toolData = getToolById("gradientgenerator");
const pageTitle = `${toolData?.name || "Gradient Generator"} | DevBottle`;
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="Gradient Generator - Create beautiful CSS gradients"
        description="Create beautiful CSS gradients with animation capabilities for your web projects. Design linear, radial, and conic gradients with a visual editor."
        canonical="https://devbottle.com/tools/gradientgenerator"
        openGraph={{
            basic: {
                title: "Gradient Generator - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-gradientgenerator.jpg",
                url: "https://devbottle.com/tools/gradientgenerator",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Create beautiful CSS gradients with animation capabilities for your web projects. Design linear, radial, and conic gradients with a visual editor.",
            },
        }}
    />
    <StructuredData
        slot="head"
        type="tool"
        data={{
            name: toolData?.name || "Gradient Generator",
            description:
                toolData?.description ||
                "Design linear and radial gradients with a visual editor, add animations, and export ready-to-use CSS code.",
        }}
    />

    <GradientGenerator client:load />

    <div class="container mx-auto px-4 py-12 seo-content">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mt-8">
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">About the Gradient Generator</h2>

            <p class="text-gray-700 dark:text-gray-300 mb-4">
                The Gradient Generator is a powerful tool for creating beautiful CSS gradients for your web projects. Whether you need a subtle background, a vibrant button, or an eye-catching header, this tool helps you design and implement gradients with ease.
            </p>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Types of CSS Gradients</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Linear Gradients</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Linear gradients transition colors along a straight line. You can control the direction (angle) and the color stops to create everything from subtle fades to sharp color transitions.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Radial Gradients</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Radial gradients transition colors from a central point outward in a circular or elliptical pattern. They're perfect for creating spotlight effects, glows, or circular design elements.
                    </p>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Conic Gradients</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Conic gradients transition colors around a center point (rather than radiating from it). They're excellent for creating pie charts, color wheels, or unique background patterns.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Animated Gradients</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Using CSS animations, gradients can shift and change over time. Our tool helps you create animated gradients that add subtle movement and visual interest to your designs.
                    </p>
                </div>
            </div>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Features of Our Gradient Generator</h3>

            <ul class="list-disc pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li><strong>Visual editor</strong> - Design gradients with an intuitive interface</li>
                <li><strong>Multiple gradient types</strong> - Create linear, radial, and conic gradients</li>
                <li><strong>Custom color stops</strong> - Add multiple colors and control their positions precisely</li>
                <li><strong>Animation options</strong> - Add movement to your gradients with customizable animations</li>
                <li><strong>Preset library</strong> - Choose from a collection of beautiful gradient presets</li>
                <li><strong>Copy-ready CSS</strong> - Get the exact CSS code needed to implement your gradient</li>
                <li><strong>Browser compatibility</strong> - Generate code with appropriate vendor prefixes</li>
            </ul>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">How to Use the Gradient Generator</h3>

            <ol class="list-decimal pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li>Select a gradient type (linear, radial, or conic)</li>
                <li>Choose your colors by clicking on the color stops</li>
                <li>Adjust the position of color stops by dragging them</li>
                <li>Set additional properties like angle, shape, or position</li>
                <li>Enable animation if desired and customize its properties</li>
                <li>Preview your gradient in real-time</li>
                <li>Copy the generated CSS code for use in your project</li>
            </ol>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Creative Uses for CSS Gradients</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Website Backgrounds</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Gradients make excellent backgrounds for websites, providing visual interest without the file size of images. Subtle gradients can add depth while bold ones create striking visual impact.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">UI Elements</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Buttons, cards, and other UI components can be enhanced with gradients to create a sense of dimension and improve visual hierarchy. Animated gradients can draw attention to important call-to-action elements.
                    </p>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Text Effects</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Using <code class="bg-gray-200 dark:bg-gray-600 px-1 rounded">background-clip: text</code>, you can apply gradients directly to text for eye-catching headlines and titles without the need for images.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Data Visualization</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Conic gradients can be used to create pie charts and other data visualizations directly with CSS, reducing the need for JavaScript libraries for simple charts.
                    </p>
                </div>
            </div>

            <div class="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg mt-6">
                <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">Pro Tip</h3>
                <p class="text-blue-700 dark:text-blue-300">
                    When creating gradients for text or UI elements, consider using HSL color values instead of HEX or RGB. HSL makes it easier to create harmonious color transitions by adjusting only the hue while keeping saturation and lightness consistent.
                </p>
            </div>
        </div>
    </div>
</ToolLayout>
