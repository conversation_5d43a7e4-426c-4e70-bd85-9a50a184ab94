---
import ToolsIndexLayout from "../../layouts/ToolsIndexLayout.astro";
import { SEO } from "astro-seo";
import Card from "../../components/ui/Card.jsx";
import { getAllTools } from "../../data/tools.js";
import * as LucideIcons from "@lucide/astro";
import StructuredData from "../../components/StructuredData.astro";

// Get all tools
const tools = getAllTools();
---

<ToolsIndexLayout title="Tools | DevBottle">
    <SEO
        slot="head"
        title="Developer Tools - DevBottle"
        description="A collection of simple, useful tools for developers, designers, and content creators."
        canonical="https://devbottle.com/tools"
        openGraph={{
            basic: {
                title: "Developer Tools - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-tools.jpg",
                url: "https://devbottle.com/tools",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "A collection of simple, useful tools for developers.",
            },
        }}
    />
    <StructuredData
        slot="head"
        type="website"
        data={{
            url: "https://devbottle.com/tools",
            title: "Developer Tools - DevBottle",
            description:
                "A collection of simple, useful tools for developers, designers, and content creators.",
        }}
    />

    <div class="bg-white dark:bg-gray-900">
        <!-- Hero Section -->

        <section class="relative py-16 md:py-24 overflow-hidden">
            <!-- Gradient Backgrounds (only in light mode) -->
            <div
                class="absolute left-0 top-0 w-1/3 h-1/2 bg-gradient-to-br from-primary/20 to-transparent opacity-50 blur-3xl dark:opacity-0"
            >
            </div>
            <div
                class="absolute right-0 bottom-0 w-1/3 h-1/2 bg-gradient-to-tl from-secondary/20 to-transparent opacity-50 blur-3xl dark:opacity-0"
            >
            </div>

            <div class="relative z-10 container mx-auto px-4">
                <div class="max-w-3xl mx-auto text-center">
                    <h1
                        class="text-4xl md:text-5xl font-bold mb-6 text-gray-800 dark:text-white"
                    >
                        Developer Tools
                    </h1>
                    <p class="text-xl text-gray-600 dark:text-gray-400 mb-8">
                        Simple, focused tools for developers, designers, and
                        content creators.
                    </p>
                </div>
            </div>
        </section>

        <!-- Tools Grid Section -->
        <section class="py-12 bg-gray-50 dark:bg-gray-800 overflow-hidden">
            <div class="container mx-auto px-4">
                <div class="max-w-6xl mx-auto">
                    <div
                        class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 justify-items-center"
                    >
                        {
                            tools.map((tool) => (
                                <Card
                                    href={tool.path}
                                    className="flex flex-col h-full w-full max-w-md hover:scale-105 transition-transform duration-300"
                                    client:load
                                >
                                    <div class="flex items-center mb-4">
                                        <div class="text-primary dark:text-primary-dark mr-3">
                                            {(() => {
                                                const IconComponent =
                                                    LucideIcons[tool.icon];
                                                return IconComponent ? (
                                                    <IconComponent size={28} />
                                                ) : null;
                                            })()}
                                        </div>
                                        <h3 class="text-xl font-bold">
                                            {tool.name}
                                        </h3>
                                    </div>
                                    <p class="text-gray-600 dark:text-gray-400 mb-4">
                                        {(() => {
                                            // Get the appropriate description
                                            let desc =
                                                tool.shortDescription ||
                                                tool.description;

                                            // Limit to 120 characters
                                            if (desc.length > 120) {
                                                // Find the last space before the 120 character limit
                                                const lastSpace = desc
                                                    .substring(0, 120)
                                                    .lastIndexOf(" ");
                                                // If there's a space, cut at that point, otherwise cut at 120
                                                const cutPoint =
                                                    lastSpace > 0
                                                        ? lastSpace
                                                        : 120;
                                                desc =
                                                    desc.substring(
                                                        0,
                                                        cutPoint
                                                    ) + "...";
                                            }

                                            return desc;
                                        })()}
                                    </p>
                                    <div class="mt-auto pt-4 flex justify-end">
                                        <span class="text-primary dark:text-primary-dark font-medium inline-flex items-center">
                                            Launch this tool
                                            <svg
                                                class="w-4 h-4 ml-1"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                                xmlns="http://www.w3.org/2000/svg"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    stroke-width="2"
                                                    d="M14 5l7 7m0 0l-7 7m7-7H3"
                                                />
                                            </svg>
                                        </span>
                                    </div>
                                </Card>
                            ))
                        }
                    </div>
                </div>
            </div>
        </section>
    </div>
</ToolsIndexLayout>
