---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import SchemaGenerator from "../../components/tools/SchemaGenerator.jsx";
import StructuredData from "../../components/StructuredData.astro";
import { getToolById } from "../../data/tools.js";

const toolData = getToolById("schemagenerator");
const pageTitle = `${toolData?.name || "Schema Generator"} | DevBottle`;
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="Schema Generator - Create structured data markup for better SEO"
        description="Generate JSON-LD schema markup for your website to improve search engine visibility and enable rich results in search engines."
        openGraph={{
            basic: {
                title: "Schema Generator - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-schemagenerator.jpg",
                url: "https://devbottle.com/tools/schemagenerator",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Create structured data markup that helps search engines understand your content better.",
            },
        }}
    />
    <StructuredData
        slot="head"
        type="tool"
        data={{
            name: toolData?.name || "Schema Generator",
            description:
                toolData?.description ||
                "Generate JSON-LD schema markup for your website to improve search engine visibility and enable rich results.",
        }}
    />

    <SchemaGenerator client:load />

    <div class="container mx-auto px-4 py-12 seo-content">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mt-8">
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">About the Schema Generator</h2>

            <p class="text-gray-700 dark:text-gray-300 mb-4">
                The Schema Generator helps you create structured data markup in JSON-LD format for your website. This markup helps search engines better understand your content, potentially improving your search visibility and enabling rich results in search engine results pages (SERPs).
            </p>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">What is Schema Markup?</h3>

            <p class="text-gray-700 dark:text-gray-300 mb-4">
                Schema markup (also called structured data) is a standardized format for providing information about a page and classifying its content. It's based on Schema.org vocabulary, which was created by Google, Microsoft, Yahoo, and Yandex to establish common standards for structured data markup on the internet.
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">JSON-LD Format</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        JSON-LD (JavaScript Object Notation for Linked Data) is the recommended format for schema markup. It's embedded in a <code class="bg-gray-200 dark:bg-gray-600 px-1 rounded">&lt;script&gt;</code> tag in the <code class="bg-gray-200 dark:bg-gray-600 px-1 rounded">&lt;head&gt;</code> section of your HTML and doesn't affect your page's appearance.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Rich Results</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        When search engines understand your content through schema markup, they can display it in enhanced ways called "rich results" or "rich snippets." These can include star ratings, images, additional links, and other information directly in search results.
                    </p>
                </div>
            </div>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Benefits of Using Schema Markup</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Enhanced Search Visibility</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Rich results stand out in search results, potentially increasing your click-through rate. They take up more space on the results page and provide more information to users before they click.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Better Content Understanding</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Schema markup removes ambiguity about your content. For example, it can clarify whether "Apple" on your page refers to the fruit or the technology company, helping search engines deliver more relevant results.
                    </p>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Voice Search Optimization</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        As voice search becomes more prevalent, structured data helps digital assistants like Google Assistant, Siri, and Alexa find and present information from your website more effectively.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Future-Proofing</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        As search engines evolve, they're increasingly relying on structured data to understand content. Implementing schema markup now helps ensure your site remains optimized for future search technologies.
                    </p>
                </div>
            </div>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Common Schema Types</h3>

            <div class="mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-4">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Local Business</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        For businesses with physical locations. Includes information like address, phone number, business hours, and reviews. Can help your business appear in local search results and Google Maps.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-4">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Article</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        For news articles, blog posts, and other written content. Includes information like headline, author, published date, and featured image. Can help your content appear in Google News and other news aggregators.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-4">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Product</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        For e-commerce product pages. Includes information like price, availability, reviews, and product features. Can enable rich product results in search, including price and availability information.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">FAQ</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        For pages with frequently asked questions. Includes questions and their answers. Can enable FAQ rich results that show the questions and answers directly in search results, taking up more space and potentially increasing click-through rates.
                    </p>
                </div>
            </div>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">How to Use the Schema Generator</h3>

            <ol class="list-decimal pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li>Select the type of schema you want to create (Article, Product, Local Business, etc.)</li>
                <li>Fill in the required fields for your selected schema type</li>
                <li>Add any optional fields that are relevant to your content</li>
                <li>Preview the generated JSON-LD code</li>
                <li>Copy the code and paste it into the <code class="bg-gray-200 dark:bg-gray-600 px-1 rounded">&lt;head&gt;</code> section of your HTML</li>
                <li>Test your markup using Google's Rich Results Test or Schema Markup Validator</li>
            </ol>

            <div class="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg mt-6">
                <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">Pro Tip</h3>
                <p class="text-blue-700 dark:text-blue-300">
                    After implementing schema markup, use Google's <a href="https://search.google.com/test/rich-results" class="underline hover:text-blue-500 dark:hover:text-blue-300" target="_blank" rel="noopener">Rich Results Test</a> to verify that your markup is valid and eligible for rich results. Also, monitor your performance in Google Search Console, which provides reports on how your structured data is performing in search results.
                </p>
            </div>
        </div>
    </div>
</ToolLayout>
