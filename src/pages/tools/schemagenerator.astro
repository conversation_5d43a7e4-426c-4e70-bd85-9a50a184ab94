---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import SchemaGenerator from "../../components/tools/SchemaGenerator.jsx";
import StructuredData from "../../components/StructuredData.astro";
import { getToolById } from "../../data/tools.js";

const toolData = getToolById("schemagenerator");
const pageTitle = `${toolData?.name || "Schema Generator"} | DevBottle`;
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="Schema Generator - Create structured data markup for better SEO"
        description="Generate JSON-LD schema markup for your website to improve search engine visibility and enable rich results in search engines."
        openGraph={{
            basic: {
                title: "Schema Generator - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-schemagenerator.jpg",
                url: "https://devbottle.com/tools/schemagenerator",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Create structured data markup that helps search engines understand your content better.",
            },
        }}
    />
    <StructuredData
        slot="head"
        type="tool"
        data={{
            name: toolData?.name || "Schema Generator",
            description:
                toolData?.description ||
                "Generate JSON-LD schema markup for your website to improve search engine visibility and enable rich results.",
        }}
    />

    <SchemaGenerator client:load />
</ToolLayout>
