---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import SlugifyTool from "../../components/tools/SlugTool.jsx";
import StructuredData from "../../components/StructuredData.astro";
import { getToolById } from "../../data/tools.js";

const pageTitle = "Slug Tool | DevBottle";
const toolData = getToolById("slugtool");
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="Slug Tool - Create clean URL-friendly slugs"
        description="Convert any text into a clean, URL-friendly slug. Perfect for SEO, filenames, or creating readable identifiers."
        canonical="https://devbottle.com/tools/slugtool"
        openGraph={{
            basic: {
                title: "Slug Tool - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-slugtool.jpg",
                url: "https://devbottle.com/tools/slugtool",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Convert any text into a clean, URL-friendly slug. Perfect for SEO, filenames, or creating readable identifiers.",
            },
        }}
    />
    <StructuredData
        slot="head"
        type="tool"
        data={{
            name: toolData?.name || "Slug Tool",
            description:
                toolData?.description ||
                "Convert any text into a clean, URL-friendly slug. Perfect for SEO, filenames, or creating readable identifiers.",
        }}
    />

    <SlugifyTool client:load />

    <div class="container mx-auto px-4 py-12 seo-content">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mt-8">
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">About the Slug Tool</h2>

            <p class="text-gray-700 dark:text-gray-300 mb-4">
                A slug is a URL-friendly version of a string, typically used in web addresses to create readable, SEO-friendly URLs. Our Slug Tool converts any text into a clean, URL-friendly slug by removing special characters, replacing spaces with separators, and applying other formatting rules.
            </p>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">How the Slug Tool Works</h3>

            <p class="text-gray-700 dark:text-gray-300 mb-4">
                The Slug Tool processes your input text through several steps:
            </p>

            <ol class="list-decimal pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li>Converts text to lowercase (optional)</li>
                <li>Removes accents and diacritical marks</li>
                <li>Replaces spaces with your chosen separator (hyphen, underscore, or period)</li>
                <li>Removes common stop words (optional)</li>
                <li>Strips special characters and punctuation</li>
                <li>Ensures no consecutive separators</li>
            </ol>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Why Use URL Slugs?</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">SEO Benefits</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Search engines prefer URLs that contain relevant keywords rather than random strings or IDs. Well-crafted slugs can improve your page's search engine ranking and make your URLs more shareable.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">User Experience</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Descriptive, readable URLs help users understand what a page is about before clicking. They're easier to remember, type manually, and share verbally.
                    </p>
                </div>
            </div>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Common Uses for the Slug Tool</h3>

            <ul class="list-disc pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li>Creating SEO-friendly URLs for blog posts and articles</li>
                <li>Generating filenames for downloadable content</li>
                <li>Creating readable identifiers for database records</li>
                <li>Normalizing user input for consistent data storage</li>
                <li>Creating permalink structures for content management systems</li>
            </ul>

            <div class="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg mt-6">
                <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">Pro Tip</h3>
                <p class="text-blue-700 dark:text-blue-300">
                    For maximum SEO benefit, keep your slugs concise while including relevant keywords. Aim for 3-5 words that accurately describe your content. Too-long slugs can get truncated in search results and are harder to share.
                </p>
            </div>
        </div>
    </div>
</ToolLayout>
