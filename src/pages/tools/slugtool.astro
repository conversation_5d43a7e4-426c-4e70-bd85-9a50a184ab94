---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import SlugifyTool from "../../components/tools/SlugTool.jsx";
import StructuredData from "../../components/StructuredData.astro";
import { getToolById } from "../../data/tools.js";

const pageTitle = "Slug Tool | DevBottle";
const toolData = getToolById("slugtool");
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="Slug Tool - Create clean URL-friendly slugs"
        description="Convert any text into a clean, URL-friendly slug. Perfect for SEO, filenames, or creating readable identifiers."
        canonical="https://devbottle.com/tools/slugtool"
        openGraph={{
            basic: {
                title: "Slug Tool - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-slugtool.jpg",
                url: "https://devbottle.com/tools/slugtool",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Convert any text into a clean, URL-friendly slug. Perfect for SEO, filenames, or creating readable identifiers.",
            },
        }}
    />
    <StructuredData
        slot="head"
        type="tool"
        data={{
            name: toolData?.name || "Slug Tool",
            description:
                toolData?.description ||
                "Convert any text into a clean, URL-friendly slug. Perfect for SEO, filenames, or creating readable identifiers.",
        }}
    />

    <SlugifyTool client:load />
</ToolLayout>
