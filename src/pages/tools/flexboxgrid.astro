---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import FlexboxGridGenerator from "../../components/tools/FlexboxGridGenerator.jsx";
import StructuredData from "../../components/StructuredData.astro";
import { getToolById } from "../../data/tools.js";

const toolData = getToolById("flexboxgrid");
const pageTitle = `${toolData?.name || "Flexbox & Grid Generator"} | DevBottle`;
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="Flexbox & Grid Generator - Create CSS layouts visually"
        description="Create and customize flexbox and CSS grid layouts with an interactive visual editor. Adjust container and item properties, see real-time previews, and generate clean CSS code."
        canonical="https://devbottle.com/tools/flexboxgrid"
        openGraph={{
            basic: {
                title: "Flexbox & Grid Generator - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-flexboxgrid.jpg",
                url: "https://devbottle.com/tools/flexboxgrid",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Create and customize flexbox and CSS grid layouts with an interactive visual editor. Adjust container and item properties, see real-time previews, and generate clean CSS code.",
            },
        }}
    />
    <StructuredData
        slot="head"
        type="tool"
        data={{
            name: toolData?.name || "Flexbox & Grid Generator",
            description:
                toolData?.description ||
                "Create and customize flexbox and CSS grid layouts with an interactive visual editor. Adjust container and item properties, see real-time previews, and generate clean CSS code.",
        }}
    />

    <FlexboxGridGenerator client:load />

    <div class="container mx-auto px-4 py-12 max-w-4xl">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mt-8">
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">About the Flexbox & Grid Generator</h2>

            <p class="text-gray-700 dark:text-gray-300 mb-4">
                The Flexbox & Grid Generator is a powerful visual tool for creating and customizing CSS layouts using Flexbox and CSS Grid. Whether you're a beginner learning these layout systems or an experienced developer looking to save time, this tool helps you design responsive layouts without writing CSS code from scratch.
            </p>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Understanding CSS Layout Systems</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Flexbox</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        CSS Flexbox (Flexible Box Layout) is a one-dimensional layout method designed for laying out items in rows or columns. Flexbox makes it easier to design flexible responsive layouts without using floats or positioning. It's ideal for components of an application and small-scale layouts.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">CSS Grid</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        CSS Grid Layout is a two-dimensional layout system designed for laying out items in rows and columns simultaneously. Grid gives developers more precise control over the placement of elements in a layout and is perfect for larger-scale layouts and complex designs.
                    </p>
                </div>
            </div>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Features of Our Flexbox & Grid Generator</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Visual Editor</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Design your layout visually with an interactive interface. Add, remove, and customize container and item properties while seeing the changes in real-time. This makes it easy to understand how different CSS properties affect your layout.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Comprehensive Controls</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Access all the important Flexbox and Grid properties including flex-direction, justify-content, align-items, grid-template-columns, grid-template-rows, and many more. Fine-tune your layout with precise control over every aspect.
                    </p>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Item-Level Customization</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Customize individual items within your layout with properties like flex-grow, flex-shrink, align-self, grid-column, and grid-row. This allows for complex layouts where certain elements behave differently than others.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Clean CSS Output</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Generate clean, optimized CSS code that you can copy and paste directly into your projects. The code is well-formatted and includes only the properties you've actually used, avoiding bloat in your stylesheets.
                    </p>
                </div>
            </div>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">When to Use Flexbox vs. Grid</h3>

            <div class="mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-4">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Use Flexbox When:</h4>
                    <ul class="list-disc pl-5 text-gray-700 dark:text-gray-300 space-y-1">
                        <li>You need a layout in a single dimension (row OR column)</li>
                        <li>You want content to determine the size of elements</li>
                        <li>You're creating navigation menus or toolbars</li>
                        <li>You need to distribute space between items evenly</li>
                        <li>You're aligning items within a container</li>
                    </ul>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Use CSS Grid When:</h4>
                    <ul class="list-disc pl-5 text-gray-700 dark:text-gray-300 space-y-1">
                        <li>You need a layout in two dimensions (rows AND columns)</li>
                        <li>You want the layout to determine the size and position of elements</li>
                        <li>You're creating a full-page layout or complex component</li>
                        <li>You need precise control over item placement</li>
                        <li>You're creating a layout with overlapping elements</li>
                    </ul>
                </div>
            </div>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">How to Use the Flexbox & Grid Generator</h3>

            <ol class="list-decimal pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li>Choose between Flexbox or Grid layout mode</li>
                <li>Configure the container properties (direction, alignment, etc.)</li>
                <li>Add items to your layout</li>
                <li>Customize individual item properties if needed</li>
                <li>Preview your layout in real-time</li>
                <li>Copy the generated CSS code</li>
                <li>Implement the code in your project</li>
            </ol>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Common Layout Patterns</h3>

            <ul class="list-disc pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li><strong>Holy Grail Layout</strong> - A classic web layout with header, footer, main content, and two sidebars</li>
                <li><strong>Card Grid</strong> - A responsive grid of cards that reflow based on screen size</li>
                <li><strong>Sticky Footer</strong> - A layout where the footer stays at the bottom even with minimal content</li>
                <li><strong>Masonry Layout</strong> - A grid layout with items of varying heights, similar to Pinterest</li>
                <li><strong>Split Screen</strong> - A layout that divides the screen into two or more sections</li>
            </ul>

            <div class="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg mt-6">
                <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">Pro Tip</h3>
                <p class="text-blue-700 dark:text-blue-300">
                    For the most flexible and responsive layouts, consider using a combination of Flexbox and Grid. Use Grid for the overall page layout and Flexbox for components within that layout. This approach leverages the strengths of both systems and creates highly adaptable designs.
                </p>
            </div>
        </div>
    </div>
</ToolLayout>
