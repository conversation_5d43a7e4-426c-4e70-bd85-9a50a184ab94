---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import FlexboxGridGenerator from "../../components/tools/FlexboxGridGenerator.jsx";
import StructuredData from "../../components/StructuredData.astro";
import { getToolById } from "../../data/tools.js";

const toolData = getToolById("flexboxgrid");
const pageTitle = `${toolData?.name || "Flexbox & Grid Generator"} | DevBottle`;
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="Flexbox & Grid Generator - Create CSS layouts visually"
        description="Create and customize flexbox and CSS grid layouts with an interactive visual editor. Adjust container and item properties, see real-time previews, and generate clean CSS code."
        canonical="https://devbottle.com/tools/flexboxgrid"
        openGraph={{
            basic: {
                title: "Flexbox & Grid Generator - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-flexboxgrid.jpg",
                url: "https://devbottle.com/tools/flexboxgrid",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Create and customize flexbox and CSS grid layouts with an interactive visual editor. Adjust container and item properties, see real-time previews, and generate clean CSS code.",
            },
        }}
    />
    <StructuredData
        slot="head"
        type="tool"
        data={{
            name: toolData?.name || "Flexbox & Grid Generator",
            description:
                toolData?.description ||
                "Create and customize flexbox and CSS grid layouts with an interactive visual editor. Adjust container and item properties, see real-time previews, and generate clean CSS code.",
        }}
    />

    <FlexboxGridGenerator client:load />
</ToolLayout>
