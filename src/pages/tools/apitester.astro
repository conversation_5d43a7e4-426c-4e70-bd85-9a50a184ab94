---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import ApiTester from "../../components/tools/ApiTester.jsx";
import StructuredData from "../../components/StructuredData.astro";
import { getToolById } from "../../data/tools.js";

const pageTitle = "API Tester | DevBottle";
const toolData = getToolById("apitester");
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="API Tester - Test and debug API endpoints"
        description="Test and debug API endpoints with a simple, intuitive interface. Make HTTP requests and inspect formatted responses."
        openGraph={{
            basic: {
                title: "API Tester - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-apitester.jpg",
                url: "https://devbottle.com/tools/apitester",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Test and debug API endpoints with a simple, intuitive interface. Make HTTP requests and inspect formatted responses.",
            },
        }}
    />
    <StructuredData
        slot="head"
        type="website"
        data={{
            url: "https://devbottle.com/tools/apitester",
            title: "API Tester - DevBottle",
            description:
                "Test and debug API endpoints with a simple, intuitive interface. Make HTTP requests and inspect formatted responses.",
        }}
    />

    <ApiTester client:load />
</ToolLayout>
