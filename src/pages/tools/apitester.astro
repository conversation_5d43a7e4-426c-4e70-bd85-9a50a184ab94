---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import ApiTester from "../../components/tools/ApiTester.jsx";
import StructuredData from "../../components/StructuredData.astro";
import { getToolById } from "../../data/tools.js";

const pageTitle = "API Tester | DevBottle";
const toolData = getToolById("apitester");
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="API Tester - Test and debug API endpoints"
        description="Test and debug API endpoints with a simple, intuitive interface. Make HTTP requests and inspect formatted responses."
        openGraph={{
            basic: {
                title: "API Tester - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-apitester.jpg",
                url: "https://devbottle.com/tools/apitester",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Test and debug API endpoints with a simple, intuitive interface. Make HTTP requests and inspect formatted responses.",
            },
        }}
    />
    <StructuredData
        slot="head"
        type="website"
        data={{
            url: "https://devbottle.com/tools/apitester",
            title: "API Tester - DevBottle",
            description:
                "Test and debug API endpoints with a simple, intuitive interface. Make HTTP requests and inspect formatted responses.",
        }}
    />

    <ApiTester client:load />

    <div class="container mx-auto px-4 py-12 max-w-4xl">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mt-8">
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">About the API Tester</h2>

            <p class="text-gray-700 dark:text-gray-300 mb-4">
                The API Tester is a powerful tool for testing, debugging, and exploring RESTful APIs directly in your browser. Whether you're developing your own API, integrating with third-party services, or just learning about APIs, this tool provides a simple yet powerful interface for making HTTP requests and analyzing responses.
            </p>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">What is an API?</h3>

            <p class="text-gray-700 dark:text-gray-300 mb-4">
                An API (Application Programming Interface) is a set of rules and protocols that allows different software applications to communicate with each other. RESTful APIs, which are the most common type on the web, use standard HTTP methods (GET, POST, PUT, DELETE, etc.) to perform operations on resources, typically returning data in JSON or XML format.
            </p>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Features of Our API Tester</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Multiple HTTP Methods</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Support for all standard HTTP methods including GET, POST, PUT, PATCH, DELETE, and more. This allows you to test the full range of API endpoints and operations.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Request Headers</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Add custom headers to your requests, such as authorization tokens, content types, or API keys. This is essential for working with secured APIs or specifying how your data should be processed.
                    </p>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Request Body Editor</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        A powerful editor for crafting request bodies in JSON, XML, or form data formats. Perfect for POST, PUT, and PATCH requests that need to send data to the server.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Response Formatting</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Automatic formatting and syntax highlighting of JSON and XML responses, making it easy to read and understand the data returned by the API. The tool also displays response headers and status codes.
                    </p>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Request History</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Save and reuse previous requests, making it easy to test the same endpoint multiple times or compare responses across different API calls.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Performance Metrics</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        View response times and other performance metrics to help identify potential bottlenecks or issues with API responsiveness.
                    </p>
                </div>
            </div>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Common API Testing Scenarios</h3>

            <ul class="list-disc pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li><strong>Authentication testing</strong> - Verify that your API correctly handles authentication tokens and permissions</li>
                <li><strong>CRUD operations</strong> - Test Create, Read, Update, and Delete operations on your resources</li>
                <li><strong>Error handling</strong> - Check how your API responds to invalid inputs or unauthorized requests</li>
                <li><strong>Performance testing</strong> - Measure response times and identify potential bottlenecks</li>
                <li><strong>Integration testing</strong> - Ensure that your application correctly interacts with third-party APIs</li>
                <li><strong>Documentation verification</strong> - Confirm that an API behaves as described in its documentation</li>
            </ul>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">How to Use the API Tester</h3>

            <ol class="list-decimal pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li>Enter the URL of the API endpoint you want to test</li>
                <li>Select the appropriate HTTP method (GET, POST, PUT, DELETE, etc.)</li>
                <li>Add any required headers (e.g., Authorization, Content-Type)</li>
                <li>For POST, PUT, or PATCH requests, enter the request body in the editor</li>
                <li>Click "Send" to make the request</li>
                <li>View the formatted response, including status code, headers, and body</li>
                <li>Analyze the response to verify that the API is working as expected</li>
            </ol>

            <div class="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg mt-6">
                <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">Pro Tip</h3>
                <p class="text-blue-700 dark:text-blue-300">
                    When testing APIs that require authentication, consider using environment variables or a secure storage mechanism for your API keys and tokens rather than hardcoding them in your requests. This helps prevent accidental exposure of sensitive credentials in your code or version control systems.
                </p>
            </div>
        </div>
    </div>
</ToolLayout>
