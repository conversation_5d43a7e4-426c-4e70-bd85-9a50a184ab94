---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import DevTextGenerator from "../../components/tools/DevTextGenerator.jsx";
import StructuredData from "../../components/StructuredData.astro";
import { getToolById } from "../../data/tools.js";

const pageTitle = "DevText Generator | DevBottle";
const toolData = getToolById("devtextgenerator");
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="DevText Generator - Developer-focused placeholder text"
        description="Generate technical Lorem Ipsum with programming terms, code snippets, and developer jargon for mockups and documentation."
        openGraph={{
            basic: {
                title: "DevText Generator - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-devtextgenerator.jpg",
                url: "https://devbottle.com/tools/devtextgenerator",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Generate developer-focused placeholder text with programming terminology, variable names, and technical jargon.",
            },
        }}
    />
    <StructuredData
        slot="head"
        type="tool"
        data={{
            name: toolData?.name || "DevText Generator",
            description:
                toolData?.description ||
                "Create technical Lorem Ipsum text with programming terms, code snippets, and developer jargon.",
        }}
    />

    <DevTextGenerator client:load />
</ToolLayout>
