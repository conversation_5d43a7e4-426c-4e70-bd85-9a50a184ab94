---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import DevTextGenerator from "../../components/tools/DevTextGenerator.jsx";
import StructuredData from "../../components/StructuredData.astro";
import { getToolById } from "../../data/tools.js";

const pageTitle = "DevText Generator | DevBottle";
const toolData = getToolById("devtextgenerator");
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="DevText Generator - Developer-focused placeholder text"
        description="Generate technical Lorem Ipsum with programming terms, code snippets, and developer jargon for mockups and documentation."
        openGraph={{
            basic: {
                title: "DevText Generator - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-devtextgenerator.jpg",
                url: "https://devbottle.com/tools/devtextgenerator",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Generate developer-focused placeholder text with programming terminology, variable names, and technical jargon.",
            },
        }}
    />
    <StructuredData
        slot="head"
        type="tool"
        data={{
            name: toolData?.name || "DevText Generator",
            description:
                toolData?.description ||
                "Create technical Lorem Ipsum text with programming terms, code snippets, and developer jargon.",
        }}
    />

    <DevTextGenerator client:load />

    <div class="container mx-auto px-4 py-12 seo-content">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mt-8">
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">About the DevText Generator</h2>

            <p class="text-gray-700 dark:text-gray-300 mb-4">
                The DevText Generator creates developer-focused placeholder text for mockups, documentation, and testing. Unlike traditional Lorem Ipsum, this tool generates content with programming terminology, variable names, and technical jargon that looks like real developer documentation or comments.
            </p>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Why Use Developer-Focused Placeholder Text?</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">More Realistic Mockups</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        When designing developer documentation, API references, or technical blogs, standard Lorem Ipsum looks out of place. DevText creates placeholder content that resembles actual technical writing, making your mockups more realistic.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Better Design Decisions</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Technical content has different characteristics than regular text—it includes code snippets, longer variable names, and specialized terminology. Using DevText helps designers make better decisions about layout, typography, and spacing.
                    </p>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Testing Content Layouts</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        When testing how technical content will flow in your UI, DevText provides a more accurate representation of the actual content that will be displayed, helping identify potential layout issues early.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Client Presentations</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        When presenting mockups to technical clients, using developer-focused placeholder text shows attention to detail and understanding of the target audience, creating a more positive impression.
                    </p>
                </div>
            </div>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Features of the DevText Generator</h3>

            <ul class="list-disc pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li><strong>Multiple text types</strong> - Generate different styles of technical content, including general developer text, code comments, and documentation</li>
                <li><strong>Customizable length</strong> - Choose the number of paragraphs or sentences to generate</li>
                <li><strong>Programming language focus</strong> - Tailor the content to include terminology from specific programming languages or frameworks</li>
                <li><strong>HTML formatting options</strong> - Wrap generated text in paragraph tags or other HTML elements for easy integration into web projects</li>
                <li><strong>Copy functionality</strong> - Quickly copy the generated text to your clipboard</li>
            </ul>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Common Use Cases</h3>

            <ol class="list-decimal pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li>Creating mockups for developer documentation websites</li>
                <li>Designing technical blog layouts</li>
                <li>Prototyping code editor interfaces</li>
                <li>Testing how code snippets will display in your UI</li>
                <li>Filling space in technical presentations or slides</li>
                <li>Creating example content for developer-focused marketing materials</li>
            </ol>

            <div class="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg mt-6">
                <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">Pro Tip</h3>
                <p class="text-blue-700 dark:text-blue-300">
                    When using DevText for mockups that will be reviewed by developers, consider selecting the HTML formatting option. This makes the placeholder text look more like real content and helps technical reviewers better visualize the final product.
                </p>
            </div>
        </div>
    </div>
</ToolLayout>
