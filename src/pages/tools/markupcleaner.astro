---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import MarkupCleanerTool from "../../components/tools/MarkupCleanerTool.jsx";
const pageTitle = "Markup Cleaner | DevBottle";
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="Markup Cleaner - Clean HTML for CMS and blog editors"
        description="Clean up messy HTML for your CMS or blog editor. Removes unwanted tags, inline styles, and formatting."
        openGraph={{
            basic: {
                title: "Markup Cleaner - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-markupcleaner.jpg",
                url: "https://devbottle.com/tools/markupcleaner",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Clean up messy HTML for your CMS or blog editor. Removes unwanted tags, inline styles, and formatting.",
            },
        }}
    />

    <MarkupCleanerTool client:load />

    <div class="container mx-auto px-4 py-12 max-w-4xl">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mt-8">
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">About the Markup Cleaner</h2>

            <p class="text-gray-700 dark:text-gray-300 mb-4">
                The Markup Cleaner is a powerful tool designed to clean and sanitize HTML code, making it suitable for content management systems (CMS), blog editors, and other platforms that may have strict HTML requirements or formatting preferences.
            </p>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">How the Markup Cleaner Works</h3>

            <p class="text-gray-700 dark:text-gray-300 mb-4">
                When you paste HTML content into the Markup Cleaner, it processes your code through several cleaning operations:
            </p>

            <ol class="list-decimal pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li>Removes unnecessary inline styles that often come from word processors</li>
                <li>Strips out class and ID attributes that may conflict with your site's styling</li>
                <li>Eliminates embedded stylesheets and JavaScript</li>
                <li>Removes redundant or empty tags</li>
                <li>Preserves the essential structure and content</li>
                <li>Adds paragraph tags to text blocks without proper HTML structure</li>
            </ol>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Common HTML Cleaning Problems Solved</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Word Processor Bloat</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Content copied from Microsoft Word, Google Docs, or other word processors often contains excessive styling and non-standard HTML that can break your website's layout. Our cleaner strips this bloat while preserving your content.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">CMS Compatibility</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Many content management systems have specific HTML requirements or restrictions. Clean HTML is less likely to cause formatting issues or security warnings in platforms like WordPress, Drupal, or custom CMS solutions.
                    </p>
                </div>
            </div>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">When to Use the Markup Cleaner</h3>

            <ul class="list-disc pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li>Before pasting content from word processors into your CMS or blog editor</li>
                <li>When migrating content between different platforms or systems</li>
                <li>To standardize HTML from various sources for consistent styling</li>
                <li>When working with content from WYSIWYG editors that generate messy code</li>
                <li>To remove potentially harmful scripts or iframes from user-submitted content</li>
            </ul>

            <div class="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg mt-6">
                <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">Pro Tip</h3>
                <p class="text-blue-700 dark:text-blue-300">
                    For the best results, review your cleaned HTML before using it in production. While our tool preserves the essential structure, you may want to make minor adjustments to ensure the content appears exactly as intended in your specific environment.
                </p>
            </div>
        </div>
    </div>
</ToolLayout>
