---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import MarkupCleanerTool from "../../components/tools/MarkupCleanerTool.jsx";
const pageTitle = "Markup Cleaner | DevBottle";
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="Markup Cleaner - Clean HTML for CMS and blog editors"
        description="Clean up messy HTML for your C<PERSON> or blog editor. Removes unwanted tags, inline styles, and formatting."
        openGraph={{
            basic: {
                title: "Markup Cleaner - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-markupcleaner.jpg",
                url: "https://devbottle.com/tools/markupcleaner",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Clean up messy HTML for your C<PERSON> or blog editor. Removes unwanted tags, inline styles, and formatting.",
            },
        }}
    />

    <MarkupCleanerTool client:load />
</ToolLayout>
