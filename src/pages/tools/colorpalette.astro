---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import ColorPaletteGenerator from "../../components/tools/ColorPaletteGenerator.jsx";
import StructuredData from "../../components/StructuredData.astro";
import { getToolById } from "../../data/tools.js";

const tool = getToolById("colorpalette");
const pageTitle = `${tool?.name || "Color Palette Generator"} | DevBottle`;
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="Color Palette Generator - Create harmonious color schemes"
        description="Generate beautiful color palettes using different color harmony rules like monochromatic, analogous, and complementary."
        openGraph={{
            basic: {
                title: "Color Palette Generator - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-colorpalette.jpg",
                url: "https://devbottle.com/tools/colorpalette",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Create harmonious color palettes for your web projects with various color schemes.",
            },
        }}
    />
    <StructuredData
        slot="head"
        type="website"
        data={{
            url: "https://devbottle.com/tools/colorpalette",
            title: "Color Palette Generator - DevBottle",
            description:
                "Generate beautiful color palettes using different color harmony rules like monochromatic, analogous, and complementary.",
        }}
    />

    <ColorPaletteGenerator client:idle />

    <div class="container mx-auto px-4 py-12 max-w-4xl">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mt-8">
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">About the Color Palette Generator</h2>

            <p class="text-gray-700 dark:text-gray-300 mb-4">
                The Color Palette Generator helps you create harmonious color schemes for your web projects, graphic designs, and UI/UX work. Using color theory principles, this tool generates balanced palettes based on your selected base colors and color harmony rules.
            </p>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Understanding Color Harmony</h3>

            <p class="text-gray-700 dark:text-gray-300 mb-4">
                Color harmony refers to the theory of combining colors in a way that is pleasing to the eye. Our Color Palette Generator uses several established color harmony rules to create cohesive palettes:
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Monochromatic</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Uses variations in lightness and saturation of a single color. This creates a cohesive look that's easy to manage and always looks harmonious.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Analogous</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Uses colors that are adjacent to each other on the color wheel. This creates a serene and comfortable design that works well when you want a design that flows well.
                    </p>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Complementary</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Uses colors that are opposite each other on the color wheel. This creates a high-contrast, vibrant look that can be very effective for drawing attention.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Triadic</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Uses three colors equally spaced around the color wheel. This provides a vibrant palette even when using paler or unsaturated versions of your colors.
                    </p>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Split Complementary</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Uses a base color and two colors adjacent to its complement. This provides strong visual contrast while being more sophisticated than a complementary scheme.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Square</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Uses four colors evenly spaced around the color wheel. This creates a balanced, dynamic palette with rich variety.
                    </p>
                </div>
            </div>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Features of Our Color Palette Generator</h3>

            <ul class="list-disc pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li><strong>Multiple base colors</strong> - Start with one or more colors of your choice</li>
                <li><strong>Various harmony rules</strong> - Choose from different color schemes to match your project's mood</li>
                <li><strong>Expanded palettes</strong> - Get primary, secondary, and tertiary color variations</li>
                <li><strong>Color code formats</strong> - Copy colors in HEX, RGB, or HSL format</li>
                <li><strong>Accessibility checking</strong> - Verify that your colors meet WCAG contrast guidelines</li>
                <li><strong>Palette export</strong> - Save your palette for use in design tools or code</li>
            </ul>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">How to Use the Color Palette Generator</h3>

            <ol class="list-decimal pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li>Select one or more base colors using the color picker</li>
                <li>Choose a color harmony rule (monochromatic, analogous, etc.)</li>
                <li>Adjust the generated palette if needed</li>
                <li>Copy individual colors or export the entire palette</li>
                <li>Use the colors in your web design, graphic design, or UI/UX projects</li>
            </ol>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Color Palette Best Practices</h3>

            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-6">
                <h4 class="font-semibold text-gray-800 dark:text-white mb-2">The 60-30-10 Rule</h4>
                <p class="text-gray-700 dark:text-gray-300">
                    A classic design principle for using color palettes effectively:
                </p>
                <ul class="list-disc pl-5 text-gray-700 dark:text-gray-300 mt-2 space-y-1">
                    <li><strong>60%</strong> - Your dominant color (often a neutral or less saturated color)</li>
                    <li><strong>30%</strong> - Your secondary color(s) for supporting elements</li>
                    <li><strong>10%</strong> - Your accent color(s) for calls to action and highlights</li>
                </ul>
            </div>

            <div class="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg mt-6">
                <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">Pro Tip</h3>
                <p class="text-blue-700 dark:text-blue-300">
                    When designing for the web, consider creating an extended palette that includes not just your main colors, but also lighter and darker variations of each. This gives you flexibility for hover states, backgrounds, borders, and text while maintaining color harmony.
                </p>
            </div>
        </div>
    </div>
</ToolLayout>
