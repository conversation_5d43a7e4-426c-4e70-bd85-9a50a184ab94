---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import ColorPaletteGenerator from "../../components/tools/ColorPaletteGenerator.jsx";
import StructuredData from "../../components/StructuredData.astro";
import { getToolById } from "../../data/tools.js";

const tool = getToolById("colorpalette");
const pageTitle = `${tool?.name || "Color Palette Generator"} | DevBottle`;
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="Color Palette Generator - Create harmonious color schemes"
        description="Generate beautiful color palettes using different color harmony rules like monochromatic, analogous, and complementary."
        openGraph={{
            basic: {
                title: "Color Palette Generator - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-colorpalette.jpg",
                url: "https://devbottle.com/tools/colorpalette",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Create harmonious color palettes for your web projects with various color schemes.",
            },
        }}
    />
    <StructuredData
        slot="head"
        type="website"
        data={{
            url: "https://devbottle.com/tools/colorpalette",
            title: "Color Palette Generator - DevBottle",
            description:
                "Generate beautiful color palettes using different color harmony rules like monochromatic, analogous, and complementary.",
        }}
    />

    <ColorPaletteGenerator client:idle />
</ToolLayout>
