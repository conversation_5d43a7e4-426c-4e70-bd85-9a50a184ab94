---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import RegExTester from "../../components/tools/RegExTester.jsx";
import StructuredData from "../../components/StructuredData.astro";
import { getToolById } from "../../data/tools.js";

const toolData = getToolById("regextester");
const pageTitle = `${toolData?.name || "RegEx Tester"} | DevBottle`;
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="RegEx Tester - Test and debug regular expressions"
        description="Test and debug regular expressions with real-time matching and highlighting. Perfect for developers working with form validation, text parsing, or data extraction."
        canonical="https://devbottle.com/tools/regextester"
        openGraph={{
            basic: {
                title: "RegEx Tester - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-regextester.jpg",
                url: "https://devbottle.com/tools/regextester",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Test and debug regular expressions with real-time matching and highlighting. Perfect for developers working with form validation, text parsing, or data extraction.",
            },
        }}
    />
    <StructuredData
        slot="head"
        type="tool"
        data={{
            name: toolData?.name || "RegEx Tester",
            description:
                toolData?.description ||
                "Interactive regular expression tester with match highlighting, group extraction, and common pattern examples.",
        }}
    />

    <RegExTester client:load />
</ToolLayout>
