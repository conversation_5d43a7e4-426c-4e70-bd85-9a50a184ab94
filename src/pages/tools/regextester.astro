---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import RegExTester from "../../components/tools/RegExTester.jsx";
import StructuredData from "../../components/StructuredData.astro";
import { getToolById } from "../../data/tools.js";

const toolData = getToolById("regextester");
const pageTitle = `${toolData?.name || "RegEx Tester"} | DevBottle`;
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="RegEx Tester - Test and debug regular expressions"
        description="Test and debug regular expressions with real-time matching and highlighting. Perfect for developers working with form validation, text parsing, or data extraction."
        canonical="https://devbottle.com/tools/regextester"
        openGraph={{
            basic: {
                title: "RegEx Tester - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-regextester.jpg",
                url: "https://devbottle.com/tools/regextester",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Test and debug regular expressions with real-time matching and highlighting. Perfect for developers working with form validation, text parsing, or data extraction.",
            },
        }}
    />
    <StructuredData
        slot="head"
        type="tool"
        data={{
            name: toolData?.name || "RegEx Tester",
            description:
                toolData?.description ||
                "Interactive regular expression tester with match highlighting, group extraction, and common pattern examples.",
        }}
    />

    <RegExTester client:load />

    <div class="container mx-auto px-4 py-12 max-w-4xl">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mt-8">
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">About the RegEx Tester</h2>

            <p class="text-gray-700 dark:text-gray-300 mb-4">
                The RegEx Tester is a powerful tool for creating, testing, and debugging regular expressions. Whether you're validating form inputs, parsing text, or extracting data patterns, this tool helps you visualize and refine your regular expressions in real-time.
            </p>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">What are Regular Expressions?</h3>

            <p class="text-gray-700 dark:text-gray-300 mb-4">
                Regular expressions (regex or regexp) are sequences of characters that define a search pattern. They are used for pattern matching within strings and are supported in most programming languages. Regular expressions can be simple, like finding all occurrences of a specific word, or complex, like validating email addresses or extracting structured data from text.
            </p>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Features of Our RegEx Tester</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Real-time Testing</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        See your regex matches instantly as you type, with visual highlighting of matched text and capture groups. This immediate feedback helps you quickly refine your patterns.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Capture Group Extraction</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        View all captured groups separately, making it easy to understand how your regex is breaking down the text and extracting specific components.
                    </p>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Regex Flags Support</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Toggle various regex flags like global (g), case-insensitive (i), multiline (m), and others to modify how your pattern behaves when matching text.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Common Pattern Library</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Access a library of commonly used regex patterns for emails, URLs, phone numbers, dates, and more. Use these as starting points for your own patterns.
                    </p>
                </div>
            </div>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Common RegEx Use Cases</h3>

            <ul class="list-disc pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li><strong>Form validation</strong> - Ensure user inputs match required formats (emails, passwords, phone numbers)</li>
                <li><strong>Data extraction</strong> - Pull specific information from unstructured text or logs</li>
                <li><strong>Search and replace</strong> - Find and modify text patterns in documents or code</li>
                <li><strong>Text parsing</strong> - Break down complex strings into meaningful components</li>
                <li><strong>URL routing</strong> - Match URL patterns in web applications</li>
                <li><strong>Syntax highlighting</strong> - Identify code elements for proper formatting</li>
            </ul>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">RegEx Quick Reference</h3>

            <div class="overflow-x-auto mb-6">
                <table class="min-w-full bg-white dark:bg-gray-700 rounded-lg overflow-hidden">
                    <thead class="bg-gray-100 dark:bg-gray-600">
                        <tr>
                            <th class="px-4 py-2 text-left text-gray-800 dark:text-white">Symbol</th>
                            <th class="px-4 py-2 text-left text-gray-800 dark:text-white">Description</th>
                            <th class="px-4 py-2 text-left text-gray-800 dark:text-white">Example</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 dark:divide-gray-600">
                        <tr>
                            <td class="px-4 py-2 text-gray-700 dark:text-gray-300"><code class="bg-gray-200 dark:bg-gray-600 px-1 rounded">.</code></td>
                            <td class="px-4 py-2 text-gray-700 dark:text-gray-300">Matches any character except newline</td>
                            <td class="px-4 py-2 text-gray-700 dark:text-gray-300"><code class="bg-gray-200 dark:bg-gray-600 px-1 rounded">a.c</code> matches "abc", "adc", etc.</td>
                        </tr>
                        <tr>
                            <td class="px-4 py-2 text-gray-700 dark:text-gray-300"><code class="bg-gray-200 dark:bg-gray-600 px-1 rounded">^</code></td>
                            <td class="px-4 py-2 text-gray-700 dark:text-gray-300">Matches start of string</td>
                            <td class="px-4 py-2 text-gray-700 dark:text-gray-300"><code class="bg-gray-200 dark:bg-gray-600 px-1 rounded">^hello</code> matches "hello world"</td>
                        </tr>
                        <tr>
                            <td class="px-4 py-2 text-gray-700 dark:text-gray-300"><code class="bg-gray-200 dark:bg-gray-600 px-1 rounded">$</code></td>
                            <td class="px-4 py-2 text-gray-700 dark:text-gray-300">Matches end of string</td>
                            <td class="px-4 py-2 text-gray-700 dark:text-gray-300"><code class="bg-gray-200 dark:bg-gray-600 px-1 rounded">world$</code> matches "hello world"</td>
                        </tr>
                        <tr>
                            <td class="px-4 py-2 text-gray-700 dark:text-gray-300"><code class="bg-gray-200 dark:bg-gray-600 px-1 rounded">*</code></td>
                            <td class="px-4 py-2 text-gray-700 dark:text-gray-300">0 or more occurrences</td>
                            <td class="px-4 py-2 text-gray-700 dark:text-gray-300"><code class="bg-gray-200 dark:bg-gray-600 px-1 rounded">ab*c</code> matches "ac", "abc", "abbc", etc.</td>
                        </tr>
                        <tr>
                            <td class="px-4 py-2 text-gray-700 dark:text-gray-300"><code class="bg-gray-200 dark:bg-gray-600 px-1 rounded">+</code></td>
                            <td class="px-4 py-2 text-gray-700 dark:text-gray-300">1 or more occurrences</td>
                            <td class="px-4 py-2 text-gray-700 dark:text-gray-300"><code class="bg-gray-200 dark:bg-gray-600 px-1 rounded">ab+c</code> matches "abc", "abbc", but not "ac"</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg mt-6">
                <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">Pro Tip</h3>
                <p class="text-blue-700 dark:text-blue-300">
                    When working with complex regular expressions, build them incrementally. Start with a simple pattern that matches part of what you need, then gradually add complexity while testing at each step. This approach makes it easier to identify and fix issues.
                </p>
            </div>
        </div>
    </div>
</ToolLayout>
