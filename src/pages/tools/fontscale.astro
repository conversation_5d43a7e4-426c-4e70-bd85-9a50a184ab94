---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import FontScaleCalculator from "../../components/tools/FontScaleCalculator.jsx";
const pageTitle = "Font Scale Calculator | DevBottle";
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="Font Scale Calculator - Create harmonious typography"
        description="Typography ratio calculator with visual hierarchy preview. Generate a consistent type scale for your web projects."
        openGraph={{
            basic: {
                title: "Font Scale Calculator - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-fontscale.jpg",
                url: "https://devbottle.com/tools/fontscale",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Create harmonious type scales for your designs with popular ratios like the golden ratio.",
            },
        }}
    />

    <FontScaleCalculator client:load />
</ToolLayout>
