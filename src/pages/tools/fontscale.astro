---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import FontScaleCalculator from "../../components/tools/FontScaleCalculator.jsx";
const pageTitle = "Font Scale Calculator | DevBottle";
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="Font Scale Calculator - Create harmonious typography"
        description="Typography ratio calculator with visual hierarchy preview. Generate a consistent type scale for your web projects."
        openGraph={{
            basic: {
                title: "Font Scale Calculator - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-fontscale.jpg",
                url: "https://devbottle.com/tools/fontscale",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Create harmonious type scales for your designs with popular ratios like the golden ratio.",
            },
        }}
    />

    <FontScaleCalculator client:load />

    <div class="container mx-auto px-4 py-12 max-w-4xl">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mt-8">
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">About the Font Scale Calculator</h2>

            <p class="text-gray-700 dark:text-gray-300 mb-4">
                The Font Scale Calculator helps you create harmonious typography systems for your web projects by generating a consistent type scale based on mathematical ratios. This approach to typography creates visual hierarchy and rhythm that makes your designs more professional and readable.
            </p>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">What is a Type Scale?</h3>

            <p class="text-gray-700 dark:text-gray-300 mb-4">
                A type scale is a progression of font sizes that follow a specific ratio, creating a harmonious relationship between different text elements on your page. Instead of randomly choosing font sizes, a type scale ensures that each size relates mathematically to the others, creating visual consistency and hierarchy.
            </p>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">How the Font Scale Calculator Works</h3>

            <ol class="list-decimal pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li>Choose a base font size (typically your body text size)</li>
                <li>Select a ratio from our presets or enter a custom ratio</li>
                <li>The calculator generates a complete type scale with multiple sizes</li>
                <li>Preview the visual hierarchy with sample text</li>
                <li>Copy the generated CSS variables for use in your project</li>
                <li>Check WCAG contrast compliance for accessibility</li>
            </ol>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Popular Type Scale Ratios</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Golden Ratio (1.618)</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Based on the famous mathematical ratio found throughout nature and classical design. The golden ratio creates a dramatic, dynamic scale with significant size differences between steps.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Perfect Fourth (1.333)</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Based on the musical interval, this ratio creates a moderate, balanced progression that works well for most web projects, offering good hierarchy without extreme size jumps.
                    </p>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Major Third (1.25)</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        A subtle ratio that creates gentle size progression, ideal for designs that need hierarchy without dramatic size differences. Works well in space-constrained interfaces.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Minor Second (1.067)</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        The most subtle ratio, creating very small increments between sizes. Useful for minimal designs or when you need many steps in your hierarchy without dramatic size changes.
                    </p>
                </div>
            </div>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Benefits of Using a Type Scale</h3>

            <ul class="list-disc pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li><strong>Improved readability</strong> - Properly scaled typography creates clear visual hierarchy</li>
                <li><strong>Design consistency</strong> - Mathematically related sizes create harmony across your interface</li>
                <li><strong>Efficient workflow</strong> - Eliminate guesswork when choosing font sizes</li>
                <li><strong>Responsive design</strong> - Scale can be implemented with relative units for responsive typography</li>
                <li><strong>Accessibility</strong> - Proper hierarchy helps users scan and understand content structure</li>
            </ul>

            <div class="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg mt-6">
                <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">Pro Tip</h3>
                <p class="text-blue-700 dark:text-blue-300">
                    When implementing your type scale with CSS variables, consider using <code class="bg-blue-100 dark:bg-blue-800 px-1 rounded">rem</code> units for your base size. This allows users to adjust their browser's default font size while maintaining your carefully designed scale relationships.
                </p>
            </div>
        </div>
    </div>
</ToolLayout>
