---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import SvgOptimizer from "../../components/tools/SvgOptimizer.jsx";
const pageTitle = "SVG Optimizer | DevBottle";
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="SVG Optimizer - Clean and optimize SVG files"
        description="Optimize SVG files by cleaning viewbox, stripping metadata, and reducing file size for web use."
        openGraph={{
            basic: {
                title: "SVG Optimizer - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-svgoptimizer.jpg",
                url: "https://devbottle.com/tools/svgoptimizer",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Optimize SVG files by cleaning viewbox, stripping metadata, and reducing file size for web use.",
            },
        }}
    />

    <SvgOptimizer client:load />
</ToolLayout>
