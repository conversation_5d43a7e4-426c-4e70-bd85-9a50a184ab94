---
import ToolLayout from "../../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import SvgOptimizer from "../../components/tools/SvgOptimizer.jsx";
const pageTitle = "SVG Optimizer | DevBottle";
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="SVG Optimizer - Clean and optimize SVG files"
        description="Optimize SVG files by cleaning viewbox, stripping metadata, and reducing file size for web use."
        openGraph={{
            basic: {
                title: "SVG Optimizer - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-svgoptimizer.jpg",
                url: "https://devbottle.com/tools/svgoptimizer",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "Optimize SVG files by cleaning viewbox, stripping metadata, and reducing file size for web use.",
            },
        }}
    />

    <SvgOptimizer client:load />

    <div class="container mx-auto px-4 py-12 max-w-4xl">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mt-8">
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">About the SVG Optimizer</h2>

            <p class="text-gray-700 dark:text-gray-300 mb-4">
                The SVG Optimizer is a powerful tool designed to clean and optimize SVG (Scalable Vector Graphics) files for web use. By removing unnecessary metadata, fixing viewBox attributes, and optimizing paths, this tool helps reduce file size and improve rendering performance.
            </p>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Why Optimize SVG Files?</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Reduced File Size</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        SVG files from design software often contain unnecessary data like editor metadata, comments, and redundant path information. Optimization can reduce file sizes by 30-70%, improving load times.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Better Performance</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Cleaner SVG code renders faster in browsers, especially for complex illustrations or animations. This is particularly important for mobile devices or pages with multiple SVG elements.
                    </p>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Improved Compatibility</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Optimized SVGs with proper viewBox attributes and standardized path data display more consistently across different browsers and devices, reducing rendering issues.
                    </p>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 dark:text-white mb-2">Easier Styling with CSS</h4>
                    <p class="text-gray-700 dark:text-gray-300">
                        Clean SVGs with simplified structure and removed inline styles are easier to manipulate with CSS, enabling dynamic color changes, animations, and other interactive features.
                    </p>
                </div>
            </div>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">What Our SVG Optimizer Does</h3>

            <ol class="list-decimal pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li><strong>Removes unnecessary metadata</strong> - Strips editor data, comments, and non-essential attributes</li>
                <li><strong>Fixes viewBox attributes</strong> - Ensures proper scaling and positioning of the SVG content</li>
                <li><strong>Optimizes path data</strong> - Simplifies path commands and coordinates for smaller file size</li>
                <li><strong>Removes unused IDs and classes</strong> - Eliminates styling information that isn't being used</li>
                <li><strong>Collapses redundant groups</strong> - Simplifies the structure for better performance</li>
                <li><strong>Converts presentation attributes to properties</strong> - Improves CSS styling capabilities</li>
            </ol>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">How to Use the SVG Optimizer</h3>

            <ol class="list-decimal pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li>Paste your SVG code into the input area or use the file upload option</li>
                <li>Select optimization options based on your needs</li>
                <li>Click "Optimize SVG" to process your file</li>
                <li>Preview the optimized SVG to ensure it looks correct</li>
                <li>Copy the optimized code or download the new SVG file</li>
            </ol>

            <h3 class="text-xl font-semibold text-gray-800 dark:text-white mt-6 mb-3">Common SVG Optimization Use Cases</h3>

            <ul class="list-disc pl-6 mb-6 text-gray-700 dark:text-gray-300 space-y-2">
                <li>Preparing icons and logos for website implementation</li>
                <li>Optimizing illustrations before embedding them in HTML</li>
                <li>Cleaning up exported SVGs from design tools like Adobe Illustrator, Figma, or Sketch</li>
                <li>Preparing SVGs for animation with CSS or JavaScript</li>
                <li>Reducing file size for SVG sprite sheets</li>
            </ul>

            <div class="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg mt-6">
                <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">Pro Tip</h3>
                <p class="text-blue-700 dark:text-blue-300">
                    For SVGs that you plan to animate or style with CSS, make sure to keep the IDs and classes that you'll need for targeting specific elements. You can use the "Preserve IDs" option to maintain important identifiers while still optimizing other aspects of the file.
                </p>
            </div>
        </div>
    </div>
</ToolLayout>
