---
import AppLayout from "../layouts/AppLayout.astro";
import { SEO } from "astro-seo";
import StructuredData from "../components/StructuredData.astro";
---

<AppLayout title="Contact Us | DevBottle">
    <SEO
        slot="head"
        title="Contact Us - DevBottle"
        description="Get in touch with the DevBottle team. We'd love to hear your feedback, suggestions, or questions about our developer tools."
        canonical="https://devbottle.com/contact"
        openGraph={{
            basic: {
                title: "Contact Us - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-contact.jpg",
                url: "https://devbottle.com/contact",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description: "Get in touch with the DevBottle team.",
            },
        }}
    />
    <StructuredData
        slot="head"
        type="website"
        data={{
            url: "https://devbottle.com/contact",
            title: "Contact Us - DevBottle",
            description:
                "Get in touch with the DevBottle team. We'd love to hear your feedback, suggestions, or questions about our developer tools.",
        }}
    />

    <div class="bg-white dark:bg-gray-900">
        <!-- Hero Section -->
        <section class="relative py-16 md:py-24 overflow-hidden">
            <!-- Gradient Backgrounds (only in light mode) -->
            <div
                class="absolute left-0 top-0 w-1/3 h-1/2 bg-gradient-to-br from-primary/20 to-transparent opacity-50 blur-3xl dark:opacity-0"
            >
            </div>
            <div
                class="absolute right-0 bottom-0 w-1/3 h-1/2 bg-gradient-to-tl from-secondary/20 to-transparent opacity-50 blur-3xl dark:opacity-0"
            >
            </div>

            <div class="relative z-10 container mx-auto px-4">
                <div class="max-w-3xl mx-auto text-center">
                    <h1
                        class="text-4xl md:text-5xl font-bold mb-6 text-gray-800 dark:text-white"
                    >
                        Contact Us
                    </h1>
                    <p class="text-xl text-gray-600 dark:text-gray-400 mb-8">
                        We'd love to hear from you! Whether you have feedback on
                        our tools, suggestions for new features, or just want to
                        say hello, please feel free to reach out.
                    </p>
                </div>
            </div>
        </section>

        <!-- Contact Form Section -->
        <section class="py-12 bg-gray-50 dark:bg-gray-800">
            <div class="container mx-auto px-4">
                <div class="max-w-3xl mx-auto">
                    <div
                        class="bg-white dark:bg-gray-900 p-8 rounded-xl shadow-sm mb-12 border border-gray-100 dark:border-gray-600"
                    >
                        <h2
                            class="text-2xl font-bold text-gray-800 dark:text-white mb-6"
                        >
                            Send Us a Message
                        </h2>
                        <form
                            class="space-y-6"
                            action="https://submit-form.com/IB6gwtCH5"
                        >
                            <input
                                type="hidden"
                                name="_redirect"
                                value="https://devbottle.com/thanks/"
                            />
                            <input
                                type="hidden"
                                name="_email.subject"
                                value="You have a new message from DevBottle!"
                            />

                            <div>
                                <label
                                    for="name"
                                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                                >
                                    Name
                                </label>
                                <input
                                    type="text"
                                    id="name"
                                    name="name"
                                    placeholder="Name"
                                    class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                    required
                                />
                            </div>

                            <div>
                                <label
                                    for="email"
                                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                                >
                                    Email
                                </label>
                                <input
                                    type="email"
                                    id="email"
                                    name="email"
                                    placeholder="Email"
                                    class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                    required
                                />
                            </div>

                            <div>
                                <label
                                    for="message"
                                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                                >
                                    Message
                                </label>
                                <textarea
                                    id="message"
                                    name="message"
                                    placeholder="Message"
                                    rows="6"
                                    class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark"
                                    required></textarea>
                            </div>

                            <div>
                                <button
                                    type="submit"
                                    class="px-6 py-3 bg-primary dark:bg-primary-dark text-white rounded-lg hover:bg-primary-dark dark:hover:bg-primary transition-colors font-medium"
                                >
                                    Send Message
                                </button>
                            </div>
                        </form>
                    </div>

                    <div class="grid md:grid-cols-2 gap-8">
                        <div
                            class="bg-white dark:bg-gray-900 p-6 rounded-xl shadow-sm border border-gray-100 dark:border-gray-600"
                        >
                            <h2
                                class="text-xl font-semibold mb-4 text-gray-800 dark:text-white"
                            >
                                Email Us
                            </h2>
                            <p class="text-gray-600 dark:text-gray-400 mb-2">
                                For general inquiries:
                            </p>
                            <a
                                href="mailto:<EMAIL>"
                                class="text-primary dark:text-primary-dark hover:underline font-medium"
                            >
                                <EMAIL>
                            </a>
                        </div>

                        <div
                            class="bg-white dark:bg-gray-900 p-6 rounded-xl shadow-sm border border-gray-100 dark:border-gray-600"
                        >
                            <h2
                                class="text-xl font-semibold mb-4 text-gray-800 dark:text-white"
                            >
                                About DevBottle
                            </h2>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">
                                DevBottle provides simple, focused tools for
                                developers, designers, and content creators. Our
                                mission is to create tools that make your
                                workflow more efficient and enjoyable.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</AppLayout>
