---
import AppLayout from "../../layouts/AppLayout.astro";
import { SEO } from "astro-seo";
import BlogPost from "../../components/BlogPost.jsx";
import StructuredData from "../../components/StructuredData.astro";

// Get all markdown blog posts
const posts = await Astro.glob("./*.md");

// Sort posts by date (newest first)
const sortedPosts = posts.sort((a, b) => {
    return (
        new Date(b.frontmatter.date).getTime() -
        new Date(a.frontmatter.date).getTime()
    );
});
---

<AppLayout title="Blog | DevBottle">
    <SEO
        slot="head"
        title="DevBottle Blog"
        description="Articles, tutorials and insights for developers."
        canonical="https://devbottle.com/blog"
        openGraph={{
            basic: {
                title: "DevBottle Blog",
                type: "website",
                image: `https://devbottle.com/images/${sortedPosts[0]?.frontmatter.image || "og-home.jpg"}`,
                url: "https://devbottle.com/blog",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description: "Articles, tutorials and insights for developers.",
            },
        }}
    />
    <StructuredData
        slot="head"
        type="website"
        data={{
            url: "https://devbottle.com/blog",
            title: "DevBottle Blog",
            description: "Articles, tutorials and insights for developers.",
            image: `https://devbottle.com/images/${sortedPosts[0]?.frontmatter.image || "og-home.jpg"}`,
        }}
    />

    <div class="bg-white dark:bg-gray-900">
        <!-- Hero Section -->
        <section class="relative py-16 md:py-24 overflow-hidden">
            <!-- Gradient Backgrounds (only in light mode) -->
            <div
                class="absolute left-0 top-0 w-1/3 h-1/2 bg-gradient-to-br from-primary/20 to-transparent opacity-50 blur-3xl dark:opacity-0"
            >
            </div>
            <div
                class="absolute right-0 bottom-0 w-1/3 h-1/2 bg-gradient-to-tl from-secondary/20 to-transparent opacity-50 blur-3xl dark:opacity-0"
            >
            </div>

            <div class="relative z-10 container mx-auto px-4">
                <div class="max-w-3xl mx-auto text-center">
                    <h1
                        class="text-4xl md:text-5xl font-bold mb-6 text-gray-800 dark:text-white"
                    >
                        DevBottle Blog
                    </h1>
                    <p class="text-xl text-gray-600 dark:text-gray-400 mb-8">
                        Explore articles, tutorials, and insights about web
                        development, tools, and best practices.
                    </p>
                </div>
            </div>
        </section>

        <!-- Blog Posts Section -->
        <section class="py-12 bg-gray-50 dark:bg-gray-800">
            <div class="container mx-auto px-4">
                <div class="max-w-6xl mx-auto">
                    <div class="grid md:grid-cols-2 gap-12">
                        {
                            sortedPosts.map((post) => (
                                <div class="border-b border-gray-200 dark:border-gray-700 pb-8">
                                    <BlogPost
                                        title={post.frontmatter.title}
                                        category={post.frontmatter.category}
                                        excerpt={post.frontmatter.excerpt}
                                        image={post.frontmatter.image}
                                        author={post.frontmatter.author}
                                        authorImage={
                                            post.frontmatter.authorImage
                                        }
                                        date={post.frontmatter.date}
                                        slug={post.url}
                                        client:load
                                    />
                                </div>
                            ))
                        }
                    </div>
                </div>
            </div>
        </section>
    </div>
</AppLayout>
