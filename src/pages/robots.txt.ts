import type { APIRoute } from 'astro';

const getRobotsTxt = (siteUrl: URL) => `User-agent: *
Allow: /

# Sitemaps
Sitemap: ${new URL('sitemap.xml', siteUrl)}
Sitemap: ${new URL('sitemap-index.xml', siteUrl)}

# Disallow specific paths
Disallow: /admin/
Disallow: /thanks
`;

export const GET: APIRoute = ({ site }) => {
  if (!site) {
    throw new Error('site is undefined. Make sure to set the site property in your astro.config.mjs file.');
  }
  
  return new Response(getRobotsTxt(site), {
    headers: {
      'Content-Type': 'text/plain',
    },
  });
};
