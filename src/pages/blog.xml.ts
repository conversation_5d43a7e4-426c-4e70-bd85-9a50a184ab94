import type { APIRoute } from 'astro';

export const GET: APIRoute = async () => {
  // Base URL from your site config
  const site = 'https://devbottle.com';

  // For blog posts, we'll use a static list that matches the actual blog posts
  // In a real-world scenario, you would use a CMS or database to manage this
  const blogPosts = [
    {
      title: 'Introducing DevBottle: Tools for Developers and Designers',
      excerpt: 'We\'re excited to launch DevBottle, a collection of simple yet powerful tools designed to make your development and design workflow smoother.',
      date: 'April 7, 2025',
      url: '/blog/introducing-devbottle/',
      category: 'Announcement'
    },
    {
      title: 'Expanding Our Toolset',
      excerpt: 'We\'ve added several new tools to help with your development workflow.',
      date: 'April 1, 2025',
      url: '/blog/expanding-our-toolset/',
      category: 'Updates'
    },
    {
      title: 'SVG Optimizer for Web Performance',
      excerpt: 'Learn how our new SVG Optimizer tool can help improve your website performance.',
      date: 'April 13, 2025',
      url: '/blog/svg-optimizer-for-web-performance/',
      category: 'Tools'
    },
    {
      title: 'DevText Generator: The Lorem Ipsum Alternative Built Specifically for Developers',
      excerpt: 'Introducing DevText Generator, a specialized Lorem Ipsum alternative that creates developer-focused placeholder text with programming terminology, code snippets, and technical jargon.',
      date: 'April 16, 2025',
      url: '/blog/devtext-generator-lorem-ipsum-alternative/',
      category: 'Tools'
    },
    {
      title: 'API Tester: A Developer\'s Swiss Army Knife for API Testing and Debugging',
      excerpt: 'Introducing our new API Tester tool - a powerful, intuitive interface for testing and debugging API endpoints. Send requests, inspect responses, and streamline your API development workflow.',
      date: 'April 17, 2025',
      url: '/blog/api-tester-tool-for-developers/',
      category: 'Tools'
    },
    {
      title: 'Master Regular Expressions with DevBottle\'s RegEx Tester',
      excerpt: 'Introducing our new RegEx Tester tool with an interactive interface, real-time matching, and a comprehensive cheat sheet to help you build, test, and debug regular expressions with confidence.',
      date: 'May 15, 2025',
      url: '/blog/master-regular-expressions-with-devbottles-regex-tester/',
      category: 'Tools'
    }
  ];

  // Note: When you add a new blog post, add it to this list as well

  // Generate blog RSS feed
  const rss = `<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>DevBottle Blog</title>
    <description>Articles, tutorials and insights for developers.</description>
    <link>${site}/blog/</link>
    <atom:link href="${site}/blog.xml" rel="self" type="application/rss+xml"/>
    <language>en-us</language>
    <lastBuildDate>${new Date().toUTCString()}</lastBuildDate>
    ${blogPosts.map(post => `
    <item>
      <title>${post.title}</title>
      <description><![CDATA[${post.excerpt}]]></description>
      <pubDate>${new Date(post.date).toUTCString()}</pubDate>
      <link>${site}${post.url}</link>
      <guid isPermaLink="true">${site}${post.url}</guid>
      <category>${post.category}</category>
    </item>`).join('')}
  </channel>
</rss>`;

  return new Response(rss, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600'
    }
  });
}
