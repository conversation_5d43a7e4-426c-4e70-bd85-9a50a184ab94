---
import Homepage from "../layouts/Homepage.astro";
import { SEO } from "astro-seo";
import Button from "../components/ui/Button.jsx";
import Card from "../components/ui/Card.jsx";
import BlogPost from "../components/BlogPost.jsx";
import { getAllTools } from "../data/tools.js";
import * as LucideIcons from "@lucide/astro";
import StructuredData from "../components/StructuredData.astro";
import OptimizedImage from "../components/OptimizedImage.astro";

// Get all tools
const allTools = getAllTools();

// Get all markdown blog posts
const posts = await Astro.glob("./blog/*.md");

// Sort posts by date (newest first)
const sortedPosts = posts.sort((a, b) => {
    return (
        new Date(b.frontmatter.date).getTime() -
        new Date(a.frontmatter.date).getTime()
    );
});

// Get the 4 most recent posts
const recentPosts = sortedPosts.slice(0, 4);
---

<Homepage title="DevBottle - Developer Tools">
    <SEO
        slot="head"
        title="DevBottle - Simple Tools for Developers"
        description="A collection of simple, useful tools for developers, designers, and content creators."
        canonical="https://devbottle.com"
        openGraph={{
            basic: {
                title: "DevBottle - Developer Tools",
                type: "website",
                image: "https://devbottle.com/images/og-home.jpg",
                url: "https://devbottle.com",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "A collection of simple, useful tools for developers.",
            },
        }}
    />
    <StructuredData
        slot="head"
        type="website"
        data={{
            url: "https://devbottle.com",
            title: "DevBottle - Simple Tools for Developers",
            description:
                "A collection of simple, useful tools for developers, designers, and content creators.",
        }}
    />

    <!-- Hero Section -->
    <section class="relative pt-16 pb-0 md:pt-24 overflow-hidden">
        <!-- Gradient Backgrounds -->
        <div
            class="absolute left-0 top-0 w-1/3 h-1/2 bg-gradient-to-br from-primary/20 to-transparent opacity-50 blur-3xl"
        >
        </div>
        <div
            class="absolute right-0 bottom-0 w-1/3 h-1/2 bg-gradient-to-tl from-secondary/20 to-transparent opacity-50 blur-3xl"
        >
        </div>

        <div class="relative z-10 container mx-auto px-4">
            <div class="container px-4 mx-auto">
                <div class="text-center max-w-4xl mx-auto">
                    <div
                        class="inline-block mb-7 px-2 py-1 text-md font-semibold bg-green-100 dark:bg-gray-800 rounded-full"
                    >
                        <div class="flex flex-wrap items-center -m-1">
                            <div class="w-auto p-1">
                                <p>🍺 Fresh-Brewed Solutions</p>
                            </div>
                        </div>
                    </div>
                    <!-- <p
                        class="mb-5 text-md text-primary dark:text-primary-dark font-semibold uppercase tracking-px flex items-center justify-center"
                    >
                        🍺 Fresh-Brewed Solutions
                    </p> -->
                    <h1
                        class="mb-9 text-3xl md:text-5xl xl:text-7xl font-bold font-heading tracking-px-n leading-tight"
                    >
                        Developer Tools On Tap: Simple Utilities for Modern Web
                        Builders
                    </h1>
                    <div class="mb-7 md:inline-block">
                        <a
                            href="/tools"
                            class="py-4 px-6 w-full text-white font-semibold border border-primary rounded-xl focus:ring focus:ring-primary/30 bg-primary hover:bg-primary-dark transition ease-in-out duration-200"
                            >Explore Tools</a
                        >
                    </div>
                    <div class="mb-16 block">
                        <a
                            class="inline-flex items-center text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary-dark transition-colors"
                            href="/blog"
                        >
                            <svg
                                class="w-5 h-5 mr-2"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"
                                ></path>
                            </svg>
                            <span class="font-medium">Latest from our blog</span
                            >
                        </a>
                    </div>
                </div>
                <div class="relative max-w-max mx-auto mb-8 lg:mb-0">
                    <OptimizedImage
                        src="dashboard.webp"
                        alt="DevBottle tools dashboard preview"
                        class="mx-auto transform hover:scale-105 transition ease-in-out duration-1000 relative z-0"
                        width={1200}
                        height={800}
                        loading="eager"
                        quality={90}
                    />
                    <!-- Benefits card - below dashboard on small/md screens, positioned inside on lg/xl -->
                    <div
                        class="benefits static mt-8 w-full max-w-none lg:absolute lg:left-[100px] lg:right-auto lg:top-24 xl:left-[-100px] lg:w-[400px] lg:mt-0 bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm p-4 md:p-6 rounded-xl shadow-xl transition-all duration-300 z-10 border border-gray-100 dark:border-gray-700"
                    >
                        <h3
                            class="text-lg md:text-xl font-bold mb-3 md:mb-4 text-gray-800 dark:text-gray-200"
                        >
                            Why DevBottle?
                        </h3>
                        <ul
                            class="grid grid-cols-2 gap-4 text-sm md:text-base lg:grid-cols-1 lg:space-y-2"
                        >
                            <li class="flex items-start">
                                <span
                                    class="flex-shrink-0 flex items-center justify-center w-5 h-5 rounded-full bg-primary/10 dark:bg-primary-dark/20 text-primary dark:text-primary-dark mr-2"
                                >
                                    <svg
                                        class="w-3 h-3"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="3"
                                            d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </span>
                                <span
                                    >Time-saving tools for busy developers</span
                                >
                            </li>
                            <li class="flex items-start">
                                <span
                                    class="flex-shrink-0 flex items-center justify-center w-5 h-5 rounded-full bg-primary/10 dark:bg-primary-dark/20 text-primary dark:text-primary-dark mr-2"
                                >
                                    <svg
                                        class="w-3 h-3"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="3"
                                            d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </span>
                                <span
                                    >Instant browser access, no installation</span
                                >
                            </li>
                            <li class="flex items-start">
                                <span
                                    class="flex-shrink-0 flex items-center justify-center w-5 h-5 rounded-full bg-primary/10 dark:bg-primary-dark/20 text-primary dark:text-primary-dark mr-2"
                                >
                                    <svg
                                        class="w-3 h-3"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="3"
                                            d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </span>
                                <span
                                    >Privacy-focused: no signup, no tracking</span
                                >
                            </li>
                            <li class="flex items-start">
                                <span
                                    class="flex-shrink-0 flex items-center justify-center w-5 h-5 rounded-full bg-primary/10 dark:bg-primary-dark/20 text-primary dark:text-primary-dark mr-2"
                                >
                                    <svg
                                        class="w-3 h-3"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="3"
                                            d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </span>
                                <span
                                    >Lightning-fast with dark mode support</span
                                >
                            </li>
                            <li class="flex items-start">
                                <span
                                    class="flex-shrink-0 flex items-center justify-center w-5 h-5 rounded-full bg-primary/10 dark:bg-primary-dark/20 text-primary dark:text-primary-dark mr-2"
                                >
                                    <svg
                                        class="w-3 h-3"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="3"
                                            d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </span>
                                <span>Streamlines everyday developer tasks</span
                                >
                            </li>
                            <li class="flex items-start">
                                <span
                                    class="flex-shrink-0 flex items-center justify-center w-5 h-5 rounded-full bg-primary/10 dark:bg-primary-dark/20 text-primary dark:text-primary-dark mr-2"
                                >
                                    <svg
                                        class="w-3 h-3"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="3"
                                            d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </span>
                                <span
                                    >Accessible and responsive on all devices</span
                                >
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!-- <div class="mt-16 md:mt-24 relative max-w-4xl mx-auto">
                <div
                    class="relative z-10 bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden"
                >
                    <Image
                        src={logoImg}
                        alt="DevBottle Logo"
                        width={600}
                        height={400}
                        class="w-full h-auto object-cover"
                    />
                </div>
                <div
                    class="absolute -bottom-6 -right-6 w-24 h-24 bg-primary/20 dark:bg-primary-dark/20 rounded-full blur-xl"
                >
                </div>
                <div
                    class="absolute -top-6 -left-6 w-32 h-32 bg-secondary/20 dark:bg-secondary-dark/20 rounded-full blur-xl"
                >
                </div>
            </div> -->
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-gray-50 dark:bg-gray-900">
        <div class="container mx-auto px-4">
            <div class="max-w-3xl mx-auto text-center mb-16">
                <h2
                    class="mb-4 text-4xl md:text-4xl text-center font-bold font-heading tracking-tight leading-tight"
                >
                    Design and Development Utility Tools
                </h2>
                <p class="text-lg text-gray-600 dark:text-gray-400">
                    Discover our freshly-brewed collection of simple yet
                    powerful tools bottled to perfection and ready to pour into
                    your development workflow for smoother, more efficient
                    coding.
                </p>
            </div>

            {/* For XL screens, center the tools if there are only 2 */}
            <div class="max-w-6xl mx-auto">
                {/* Display only up to 6 tools */}
                <div
                    class={`grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 justify-items-center ${allTools.length <= 2 ? "xl:grid-cols-2 xl:max-w-4xl xl:mx-auto" : ""}`}
                >
                    {
                        allTools.slice(0, 6).map((tool) => (
                            <Card
                                href={tool.path}
                                className="flex flex-col h-full w-full max-w-md hover:scale-105 transition-transform duration-300"
                                client:load
                            >
                                <div class="flex items-center mb-4">
                                    <div class="text-primary dark:text-primary-dark mr-3">
                                        {(() => {
                                            const IconComponent =
                                                LucideIcons[tool.icon];
                                            return IconComponent ? (
                                                <IconComponent size={28} />
                                            ) : null;
                                        })()}
                                    </div>
                                    <h3 class="text-xl font-bold">
                                        {tool.name}
                                    </h3>
                                </div>
                                <p class="text-gray-600 dark:text-gray-400 mb-4">
                                    {(() => {
                                        // Get the appropriate description
                                        let desc =
                                            tool.shortDescription ||
                                            tool.description;

                                        // Limit to 120 characters
                                        if (desc.length > 120) {
                                            // Find the last space before the 120 character limit
                                            const lastSpace = desc
                                                .substring(0, 120)
                                                .lastIndexOf(" ");
                                            // If there's a space, cut at that point, otherwise cut at 120
                                            const cutPoint =
                                                lastSpace > 0 ? lastSpace : 120;
                                            desc =
                                                desc.substring(0, cutPoint) +
                                                "...";
                                        }

                                        return desc;
                                    })()}
                                </p>
                                <div class="mt-auto pt-4 flex justify-end">
                                    <span class="text-primary dark:text-primary-dark font-medium inline-flex items-center">
                                        Launch this tool
                                        <svg
                                            class="w-4 h-4 ml-1"
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                stroke-width="2"
                                                d="M14 5l7 7m0 0l-7 7m7-7H3"
                                            />
                                        </svg>
                                    </span>
                                </div>
                            </Card>
                        ))
                    }
                </div>

                {/* Show "More Tools" button if there are more than 6 tools */}
                {
                    allTools.length > 6 && (
                        <div class="mt-12 text-center">
                            <Button
                                href="/tools"
                                variant="outline"
                                size="lg"
                                className="mx-auto"
                                client:load
                            >
                                More Tools
                            </Button>
                        </div>
                    )
                }
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-primary dark:bg-primary-dark text-white">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto text-center">
                <h2 class="text-3xl md:text-4xl font-bold mb-6">
                    Ready to tap into better development?
                </h2>
                <p class="text-xl text-white mb-10 max-w-2xl mx-auto">
                    Our tools are brewed to save you time and make your
                    development process more refreshing. Uncap them today!
                </p>
                <Button
                    href="/tools"
                    variant="secondary"
                    size="lg"
                    className="bg-white text-primary dark:bg-gray-900 dark:text-primary-dark"
                    client:load
                >
                    Explore All Tools
                </Button>
            </div>
        </div>
    </section>

    <!-- Blog Section -->
    <section class="pt-24 pb-36 bg-white dark:bg-gray-900 overflow-hidden">
        <div class="container px-4 mx-auto">
            <h2
                class="mb-4 text-4xl md:text-5xl text-center font-bold font-heading tracking-tight leading-tight"
            >
                Fresh Brews from Our Blog
            </h2>
            <p
                class="mb-16 font-medium text-gray-600 dark:text-gray-400 text-center leading-relaxed md:max-w-lg mx-auto"
            >
                Sip on our latest insights, tutorials, and best practices
                crafted by our team of expert developers and designers.
            </p>

            <div class="flex flex-wrap -m-8">
                {
                    recentPosts.map((post) => (
                        <div class="w-full md:w-1/2 p-8">
                            <BlogPost
                                title={post.frontmatter.title}
                                category={post.frontmatter.category}
                                excerpt={post.frontmatter.excerpt}
                                image={post.frontmatter.image}
                                author={post.frontmatter.author}
                                authorImage={post.frontmatter.authorImage}
                                date={post.frontmatter.date}
                                slug={post.url}
                                client:load
                            />
                        </div>
                    ))
                }
            </div>

            <div class="mt-12 text-center">
                <Button
                    href="/blog"
                    variant="outline"
                    size="lg"
                    className="mx-auto"
                    client:load
                >
                    More Articles
                </Button>
            </div>
        </div>
    </section>
</Homepage>
