---
import AppLayout from "../layouts/AppLayout.astro";
import { SEO } from "astro-seo";
import * as LucideIcons from "@lucide/astro";
import StructuredData from "../components/StructuredData.astro";
---

<AppLayout title="About DevBottle | Simple Tools for Developers">
    <SEO
        slot="head"
        title="About DevBottle - Simple Tools for Developers"
        description="Learn about DevBottle's mission to create simple, focused tools that make developers' and designers' workflows more efficient and enjoyable."
        canonical="https://devbottle.com/about"
        openGraph={{
            basic: {
                title: "About DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-about.jpg",
                url: "https://devbottle.com/about",
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description: "Learn about DevBottle's mission and story.",
            },
        }}
    />
    <StructuredData
        slot="head"
        type="organization"
        data={{
            name: "DevBottle",
            description: "Simple, focused tools for developers and designers.",
            url: "https://devbottle.com",
        }}
    />

    <div class="bg-white dark:bg-gray-900">
        <!-- Hero Section -->
        <section class="relative py-16 md:py-24 overflow-hidden">
            <!-- Gradient Backgrounds (only in light mode) -->
            <div
                class="absolute left-0 top-0 w-1/3 h-1/2 bg-gradient-to-br from-primary/20 to-transparent opacity-50 blur-3xl dark:opacity-0"
            >
            </div>
            <div
                class="absolute right-0 bottom-0 w-1/3 h-1/2 bg-gradient-to-tl from-secondary/20 to-transparent opacity-50 blur-3xl dark:opacity-0"
            >
            </div>

            <div class="relative z-10 container mx-auto px-4">
                <div class="max-w-3xl mx-auto text-center">
                    <h1
                        class="text-4xl md:text-5xl font-bold mb-6 text-gray-800 dark:text-white"
                    >
                        About DevBottle
                    </h1>
                    <p class="text-xl text-gray-600 dark:text-gray-400 mb-8">
                        Simple, focused tools for developers, designers, and
                        content creators.
                    </p>
                </div>
            </div>
        </section>

        <!-- Mission Section -->
        <section class="py-12 bg-gray-50 dark:bg-gray-800">
            <div class="container mx-auto px-4">
                <div class="max-w-3xl mx-auto">
                    <div class="flex items-center justify-center mb-8">
                        <div class="text-primary dark:text-primary-dark mr-3">
                            <LucideIcons.Target size={32} />
                        </div>
                        <h2
                            class="text-3xl font-bold text-gray-800 dark:text-white"
                        >
                            Our Mission
                        </h2>
                    </div>
                    <p class="text-lg text-gray-600 dark:text-gray-400 mb-6">
                        DevBottle was created with a simple mission: to build
                        focused, efficient tools that solve specific problems in
                        the development and design workflow.
                    </p>
                    <p class="text-lg text-gray-600 dark:text-gray-400 mb-6">
                        We believe that the best tools are those that do one
                        thing and do it well. Rather than creating complex,
                        feature-heavy applications, we focus on building
                        streamlined tools that address particular pain points in
                        the creative process.
                    </p>
                    <p class="text-lg text-gray-600 dark:text-gray-400">
                        Our goal is to help developers, designers, and content
                        creators work more efficiently and enjoyably by
                        providing tools that simplify common tasks and eliminate
                        friction from their workflow.
                    </p>
                </div>
            </div>
        </section>

        <!-- Values Section -->
        <section class="py-12 bg-white dark:bg-gray-900">
            <div class="container mx-auto px-4">
                <div class="max-w-3xl mx-auto">
                    <div class="flex items-center justify-center mb-8">
                        <div class="text-primary dark:text-primary-dark mr-3">
                            <LucideIcons.Heart size={32} />
                        </div>
                        <h2
                            class="text-3xl font-bold text-gray-800 dark:text-white"
                        >
                            Our Values
                        </h2>
                    </div>

                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-xl">
                            <div class="flex items-center mb-4">
                                <div
                                    class="text-primary dark:text-primary-dark mr-3"
                                >
                                    <LucideIcons.Focus size={24} />
                                </div>
                                <h3
                                    class="text-xl font-semibold text-gray-800 dark:text-white"
                                >
                                    Simplicity
                                </h3>
                            </div>
                            <p class="text-gray-600 dark:text-gray-400">
                                We believe in the power of simplicity. Our tools
                                are designed to be intuitive and
                                straightforward, without unnecessary complexity
                                or bloat.
                            </p>
                        </div>

                        <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-xl">
                            <div class="flex items-center mb-4">
                                <div
                                    class="text-primary dark:text-primary-dark mr-3"
                                >
                                    <LucideIcons.Zap size={24} />
                                </div>
                                <h3
                                    class="text-xl font-semibold text-gray-800 dark:text-white"
                                >
                                    Efficiency
                                </h3>
                            </div>
                            <p class="text-gray-600 dark:text-gray-400">
                                We respect your time. Our tools are built to be
                                fast, responsive, and to help you accomplish
                                tasks with minimal effort.
                            </p>
                        </div>

                        <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-xl">
                            <div class="flex items-center mb-4">
                                <div
                                    class="text-primary dark:text-primary-dark mr-3"
                                >
                                    <LucideIcons.Shield size={24} />
                                </div>
                                <h3
                                    class="text-xl font-semibold text-gray-800 dark:text-white"
                                >
                                    Privacy
                                </h3>
                            </div>
                            <p class="text-gray-600 dark:text-gray-400">
                                Your data stays with you. All our tools process
                                your information in the browser, ensuring that
                                your content never leaves your device.
                            </p>
                        </div>

                        <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-xl">
                            <div class="flex items-center mb-4">
                                <div
                                    class="text-primary dark:text-primary-dark mr-3"
                                >
                                    <LucideIcons.Palette size={24} />
                                </div>
                                <h3
                                    class="text-xl font-semibold text-gray-800 dark:text-white"
                                >
                                    Craftsmanship
                                </h3>
                            </div>
                            <p class="text-gray-600 dark:text-gray-400">
                                We take pride in our work. Each tool is
                                carefully crafted with attention to detail, from
                                functionality to user experience.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Story Section -->
        <section class="py-12 bg-gray-50 dark:bg-gray-800">
            <div class="container mx-auto px-4">
                <div class="max-w-3xl mx-auto">
                    <div class="flex items-center justify-center mb-8">
                        <div class="text-primary dark:text-primary-dark mr-3">
                            <LucideIcons.BookOpen size={32} />
                        </div>
                        <h2
                            class="text-3xl font-bold text-gray-800 dark:text-white"
                        >
                            Our Story
                        </h2>
                    </div>
                    <p class="text-lg text-gray-600 dark:text-gray-400 mb-6">
                        DevBottle began as a personal project to solve everyday
                        challenges in web development. As developers, we often
                        found ourselves creating small utilities to handle
                        repetitive tasks or solve specific problems in our
                        workflow.
                    </p>
                    <p class="text-lg text-gray-600 dark:text-gray-400 mb-6">
                        We realized that these tools could be valuable to others
                        facing the same challenges. What started as a collection
                        of personal utilities evolved into DevBottle – a
                        platform dedicated to sharing these tools with the wider
                        development community.
                    </p>
                    <p class="text-lg text-gray-600 dark:text-gray-400 mb-6">
                        Today, DevBottle continues to grow with new tools that
                        address common pain points in development and design.
                        Each tool is born from real-world needs and refined
                        through practical use.
                    </p>
                    <p class="text-lg text-gray-600 dark:text-gray-400">
                        We're committed to expanding our collection with tools
                        that maintain our core values of simplicity, efficiency,
                        privacy, and craftsmanship.
                    </p>
                </div>
            </div>
        </section>

        <!-- Tools Section -->
        <section class="py-12 bg-white dark:bg-gray-900">
            <div class="container mx-auto px-4">
                <div class="max-w-3xl mx-auto">
                    <div class="flex items-center justify-center mb-8">
                        <div class="text-primary dark:text-primary-dark mr-3">
                            <LucideIcons.Wrench size={32} />
                        </div>
                        <h2
                            class="text-3xl font-bold text-gray-800 dark:text-white"
                        >
                            Our Tools
                        </h2>
                    </div>
                    <p
                        class="text-lg text-gray-600 dark:text-gray-400 mb-8 text-center"
                    >
                        We've created a diverse collection of tools to help with
                        various aspects of development and design.
                    </p>

                    <div class="flex justify-center">
                        <a
                            href="/tools"
                            class="px-6 py-3 bg-primary dark:bg-primary-dark text-white rounded-lg hover:bg-primary-dark dark:hover:bg-primary transition-colors"
                        >
                            Explore Our Tools
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section class="py-12 bg-gray-50 dark:bg-gray-800">
            <div class="container mx-auto px-4">
                <div class="max-w-3xl mx-auto text-center">
                    <div class="flex items-center justify-center mb-8">
                        <div class="text-primary dark:text-primary-dark mr-3">
                            <LucideIcons.MessageSquare size={32} />
                        </div>
                        <h2
                            class="text-3xl font-bold text-gray-800 dark:text-white"
                        >
                            Get in Touch
                        </h2>
                    </div>
                    <p class="text-lg text-gray-600 dark:text-gray-400 mb-8">
                        We'd love to hear your feedback, suggestions, or just
                        say hello. Your input helps us improve our tools and
                        create new ones that address real needs.
                    </p>

                    <div class="flex justify-center">
                        <a
                            href="/contact"
                            class="px-6 py-3 bg-primary dark:bg-primary-dark text-white rounded-lg hover:bg-primary-dark dark:hover:bg-primary transition-colors"
                        >
                            Contact Us
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </div>
</AppLayout>
