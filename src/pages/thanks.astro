---
import AppLayout from "../layouts/AppLayout.astro";
import { SEO } from "astro-seo";
import StructuredData from "../components/StructuredData.astro";

// This page should be excluded from the sitemap
export const prerender = true;
---

<AppLayout title="Thank You | DevBottle">
    <SEO
        slot="head"
        title="Thank You - DevBottle"
        description="Thank you for contacting DevBottle. We appreciate your message and will get back to you soon."
        noindex={true}
        nofollow={true}
    />
    <StructuredData
        slot="head"
        type="website"
        data={{
            url: "https://devbottle.com/thanks",
            title: "Thank You - DevBottle",
            description:
                "Thank you for contacting DevBottle. We appreciate your message and will get back to you soon.",
        }}
    />

    <div class="bg-white dark:bg-gray-900">
        <!-- Hero Section -->
        <section class="relative py-16 md:py-24 overflow-hidden">
            <!-- Gradient Backgrounds (only in light mode) -->
            <div
                class="absolute left-0 top-0 w-1/3 h-1/2 bg-gradient-to-br from-primary/20 to-transparent opacity-50 blur-3xl dark:opacity-0"
            >
            </div>
            <div
                class="absolute right-0 bottom-0 w-1/3 h-1/2 bg-gradient-to-tl from-secondary/20 to-transparent opacity-50 blur-3xl dark:opacity-0"
            >
            </div>

            <div class="relative z-10 container mx-auto px-4">
                <div class="max-w-3xl mx-auto text-center">
                    <h1
                        class="text-4xl md:text-5xl font-bold mb-6 text-gray-800 dark:text-white"
                    >
                        Thank You!
                    </h1>
                    <p class="text-xl text-gray-600 dark:text-gray-400 mb-8">
                        We've received your message and will get back to you as soon as possible.
                        Thank you for reaching out to us!
                    </p>
                </div>
            </div>
        </section>

        <!-- Suggestions Section -->
        <section class="py-12 bg-gray-50 dark:bg-gray-800">
            <div class="container mx-auto px-4">
                <div class="max-w-3xl mx-auto">
                    <div
                        class="bg-white dark:bg-gray-900 p-8 rounded-xl shadow-sm mb-12 border border-gray-100 dark:border-gray-600"
                    >
                        <h2
                            class="text-2xl font-bold text-gray-800 dark:text-white mb-6 text-center"
                        >
                            In the Meantime...
                        </h2>
                        <p class="text-gray-600 dark:text-gray-400 mb-8 text-center">
                            While you wait for our response, feel free to explore more of what DevBottle has to offer:
                        </p>
                        
                        <div class="grid md:grid-cols-2 gap-8">
                            <div
                                class="bg-white dark:bg-gray-900 p-6 rounded-xl shadow-sm border border-gray-100 dark:border-gray-600"
                            >
                                <h3
                                    class="text-xl font-semibold mb-4 text-gray-800 dark:text-white"
                                >
                                    Check Out Our Tools
                                </h3>
                                <p class="text-gray-600 dark:text-gray-400 mb-4">
                                    Explore our collection of simple, focused tools designed to make your development workflow more efficient.
                                </p>
                                <a
                                    href="/tools"
                                    class="text-primary dark:text-primary-dark hover:underline font-medium inline-flex items-center"
                                >
                                    Browse Tools
                                    <svg
                                        class="w-4 h-4 ml-1"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M14 5l7 7m0 0l-7 7m7-7H3"
                                        />
                                    </svg>
                                </a>
                            </div>

                            <div
                                class="bg-white dark:bg-gray-900 p-6 rounded-xl shadow-sm border border-gray-100 dark:border-gray-600"
                            >
                                <h3
                                    class="text-xl font-semibold mb-4 text-gray-800 dark:text-white"
                                >
                                    Read Our Blog
                                </h3>
                                <p class="text-gray-600 dark:text-gray-400 mb-4">
                                    Discover articles, tutorials, and insights about web development, tools, and best practices.
                                </p>
                                <a
                                    href="/blog"
                                    class="text-primary dark:text-primary-dark hover:underline font-medium inline-flex items-center"
                                >
                                    Visit Blog
                                    <svg
                                        class="w-4 h-4 ml-1"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M14 5l7 7m0 0l-7 7m7-7H3"
                                        />
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</AppLayout>
