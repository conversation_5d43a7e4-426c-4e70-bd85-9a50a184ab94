# DevBottle Tool Templates

This directory contains templates for creating new tools for the DevBottle platform.

## Available Templates

1. `tool-template.jsx` - A React component template for creating a new tool
2. `tool-page-template.astro` - An Astro page template for creating a new tool page

## Creating a New Tool

To create a new tool, follow these steps:

1. Create a new React component in `src/components/tools/` based on `tool-template.jsx`
2. Create a new Astro page in `src/pages/tools/` based on `tool-page-template.astro`
3. Add your tool to the `tools` array in `src/data/tools.js`

## Tool Container Width

You can control the width of your tool's container by adding the `containerWidth` property to your tool's data in `src/data/tools.js`. The following options are available:

- `"default"` - 8/12 width on large screens (default if not specified)
- `"full"` - Full width on all screen sizes
- `"narrow"` - 6/12 width on large screens
- `"wide"` - 10/12 width on large screens

Example:

```javascript
{
  id: 'mytool',
  name: '<PERSON> Tool',
  // ... other properties
  containerWidth: 'full' // Use full width for this tool
}
```

## Using the containerWidth Property in Your Tool Component

The `containerWidth` property is automatically passed to the `ToolContainer` component when you use the following pattern:

```jsx
<ToolContainer 
  className="py-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-colors duration-200"
  containerWidth={toolData?.containerWidth || "default"}
>
  {/* Tool content */}
</ToolContainer>
```

This ensures that your tool respects the width setting specified in the tools data.

## Best Practices

1. Always provide a default value for `containerWidth` in case it's not specified in the tool data
2. Use the `"full"` width for tools that benefit from more horizontal space, such as:
   - Color palette generators
   - Code editors
   - Data visualization tools
3. Use the `"narrow"` width for tools with simple inputs and outputs, such as:
   - Text transformers
   - Simple calculators
   - Form-based tools
4. Use the `"default"` width for most other tools
