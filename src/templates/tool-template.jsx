import { useState, useEffect } from "react";
import { getToolById } from "../data/tools.js";
import ToolHeader from "../components/ui/ToolHeader.jsx";
import ToolContainer from "../components/ui/ToolContainer.jsx";
import LucideIcon from "../components/ui/LucideIcon.jsx";

// Check if we're in the browser environment
const isBrowser = typeof window !== 'undefined';

export default function ToolTemplate() {
  const [toolData, setToolData] = useState(null);
  const [input, setInput] = useState("");
  const [output, setOutput] = useState("");
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    // Get the tool data
    const data = getToolById('toolid'); // Replace 'toolid' with your tool's ID
    setToolData(data);
  }, []);

  // Process the input
  const processInput = () => {
    // Replace this with your tool's logic
    setOutput(input);
  };

  // Copy output to clipboard
  const copyToClipboard = () => {
    if (!isBrowser || !output) return;

    navigator.clipboard.writeText(output).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };

  // Load a sample
  const loadSample = () => {
    setInput("Sample input for this tool");
    // Optionally process the sample immediately
    // processInput();
  };

  return (
    <ToolContainer 
      className="py-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-colors duration-200"
      containerWidth={toolData?.containerWidth || "default"}
    >
      <ToolHeader
        toolData={toolData}
        defaultTitle="Tool Name"
        defaultIcon="Tool"
        defaultDescription="A brief description of what this tool does."
      />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Input Section */}
        <div>
          <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
            Input
          </h2>
          
          <div className="mb-4">
            <textarea
              className="w-full h-64 p-4 border border-gray-300 dark:border-gray-600 rounded-xl text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark transition-all"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="Enter your input here..."
            />
          </div>

          <div className="flex space-x-4">
            <button
              onClick={processInput}
              className="px-4 py-2 bg-primary dark:bg-primary-dark text-white rounded-lg hover:bg-primary/90 dark:hover:bg-primary-dark/90 focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-primary-dark/50 transition-all flex items-center"
            >
              <LucideIcon name="Play" size={16} className="mr-2" />
              Process
            </button>
            
            <button
              onClick={loadSample}
              className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300 dark:focus:ring-gray-600 transition-all flex items-center"
            >
              <LucideIcon name="FileText" size={16} className="mr-2" />
              Load Sample
            </button>
          </div>
        </div>

        {/* Output Section */}
        <div>
          <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
            Output
          </h2>
          
          <div className="relative">
            <textarea
              className="w-full h-64 p-4 border border-gray-300 dark:border-gray-600 rounded-xl text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark transition-all"
              value={output}
              readOnly
              placeholder="Output will appear here..."
            />

            {output && (
              <button
                type="button"
                className="absolute bottom-4 right-4 px-3 py-1 bg-primary dark:bg-primary-dark text-white rounded-md text-sm hover:bg-primary/90 dark:hover:bg-primary-dark/90 transition-colors"
                onClick={copyToClipboard}
              >
                {copied ? "Copied!" : "Copy"}
              </button>
            )}
          </div>
        </div>
      </div>
    </ToolContainer>
  );
}
