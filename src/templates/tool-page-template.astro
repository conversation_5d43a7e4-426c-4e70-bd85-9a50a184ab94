---
import ToolLayout from "../layouts/ToolLayout.astro";
import { SEO } from "astro-seo";
import ToolComponent from "../components/tools/ToolComponent.jsx"; // Replace with your actual component
import StructuredData from "../components/StructuredData.astro";
import { getToolById } from "../data/tools.js";

// Get tool data from the tools.js file
const tool = getToolById("toolid"); // Replace 'toolid' with your tool's ID
const pageTitle = `${tool?.name || "Tool Name"} | DevBottle`;
---

<ToolLayout title={pageTitle}>
    <SEO
        slot="head"
        title="Tool Name - Brief description"
        description="A more detailed description of what this tool does and its benefits."
        openGraph={{
            basic: {
                title: "Tool Name - DevBottle",
                type: "website",
                image: "https://devbottle.com/images/og-toolname.jpg", // Replace with your OG image
                url: "https://devbottle.com/tools/toolname", // Replace with your tool's URL
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description:
                    "A more detailed description of what this tool does and its benefits.",
            },
        }}
    />
    <StructuredData
        slot="head"
        type="website"
        data={{
            url: "https://devbottle.com/tools/toolname", // Replace with your tool's URL
            title: "Tool Name - DevBottle",
            description:
                "A more detailed description of what this tool does and its benefits.",
        }}
    />

    <ToolComponent client:idle />
</ToolLayout>
