/* Blog Typography and Content Styles */
@layer components {
  /* Custom styles for elements not covered by @tailwindcss/typography */
  .blog-content {
    @apply max-w-none text-gray-800 dark:text-gray-200 text-lg;
    @apply leading-relaxed tracking-normal;
  }

  /* Headings */
  .blog-content h2 {
    @apply text-3xl font-bold mt-16 mb-6 text-gray-900 dark:text-white;
    @apply border-b border-gray-200 dark:border-gray-700 pb-3;
    @apply leading-tight tracking-tight;
    @apply relative;
  }

  .blog-content h2::before {
    content: '';
    @apply absolute -left-4 top-0 bottom-3 w-1 bg-primary dark:bg-primary-dark rounded-full opacity-70;
  }

  .blog-content h3 {
    @apply text-2xl font-bold mt-12 mb-4 text-gray-900 dark:text-white;
    @apply leading-tight tracking-tight;
  }

  .blog-content h4 {
    @apply text-xl font-semibold mt-10 mb-4 text-gray-900 dark:text-white;
    @apply leading-tight;
  }

  /* Paragraphs */
  .blog-content p {
    @apply text-lg leading-relaxed mb-8 text-gray-700 dark:text-gray-300;
    @apply max-w-prose;
  }

  .blog-content p:first-of-type {
    @apply text-xl font-medium text-gray-800 dark:text-gray-200;
  }

  .blog-content strong {
    @apply font-semibold text-gray-900 dark:text-white;
  }

  .blog-content em {
    @apply italic text-gray-800 dark:text-gray-200;
  }

  /* Lists */
  .blog-content ul,
  .blog-content ol {
    @apply mb-8 ml-6 text-lg text-gray-700 dark:text-gray-300;
    @apply max-w-prose;
    @apply space-y-3;
  }

  .blog-content ul {
    @apply list-none;
  }

  .blog-content ul li {
    @apply relative pl-6;
  }

  .blog-content ul li::before {
    content: '';
    @apply absolute left-0 top-[0.6em] w-2 h-2 bg-primary dark:bg-primary-dark rounded-full;
  }

  .blog-content ol {
    @apply list-none;
    counter-reset: item;
  }

  .blog-content ol li {
    @apply relative pl-8;
    counter-increment: item;
  }

  .blog-content ol li::before {
    content: counter(item) ".";
    @apply absolute left-0 top-0 font-semibold text-primary dark:text-primary-dark;
  }

  .blog-content li {
    @apply mb-3 leading-relaxed;
  }

  .blog-content li > ul,
  .blog-content li > ol {
    @apply mt-3 mb-0 ml-4;
  }

  /* Links */
  .blog-content a {
    @apply text-primary dark:text-primary-dark font-medium;
    @apply no-underline border-b-2 border-primary/30 dark:border-primary-dark/30;
    @apply hover:border-primary dark:hover:border-primary-dark transition-all duration-200;
  }

  /* Blockquotes */
  .blog-content blockquote {
    @apply pl-6 py-6 my-10 mx-4 md:mx-8;
    @apply border-l-4 border-primary dark:border-primary-dark;
    @apply bg-gray-50 dark:bg-gray-800 rounded-r-lg;
    @apply shadow-md;
    @apply relative;
  }

  .blog-content blockquote::before {
    content: '\201C';
    @apply absolute -top-6 -left-2 text-6xl text-primary/30 dark:text-primary-dark/30 font-serif;
  }

  .blog-content blockquote p {
    @apply text-xl italic text-gray-700 dark:text-gray-300;
    @apply leading-relaxed;
  }

  .blog-content blockquote cite {
    @apply block mt-4 text-sm font-medium text-gray-600 dark:text-gray-400 not-italic;
    @apply border-t border-gray-200 dark:border-gray-700 pt-2;
  }

  /* Code */
  .blog-content code {
    @apply font-mono text-sm px-1.5 py-0.5 rounded bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200;
  }

  .blog-content pre {
    @apply my-6 p-4 rounded-lg bg-gray-900 dark:bg-black overflow-x-auto;
  }

  .blog-content pre code {
    @apply block text-gray-200 dark:text-gray-300 bg-transparent p-0 text-sm leading-relaxed;
  }

  /* Tables */
  .blog-content table {
    @apply w-full my-10 border-collapse;
    @apply rounded-lg overflow-hidden;
    @apply shadow-md;
  }

  .blog-content table thead {
    @apply bg-gray-100 dark:bg-gray-800;
  }

  .blog-content th {
    @apply py-4 px-6 text-left font-semibold;
    @apply border-b-2 border-gray-200 dark:border-gray-700;
    @apply text-gray-900 dark:text-white;
  }

  .blog-content td {
    @apply py-4 px-6 border-b border-gray-200 dark:border-gray-700;
    @apply text-gray-700 dark:text-gray-300;
  }

  /* Fix table padding in Tailwind Typography */
  .prose-lg table thead th:first-child,
  .prose-lg table tbody td:first-child {
    padding-left: 1.5rem !important;
  }

  .blog-content tbody tr:hover {
    @apply bg-gray-50 dark:bg-gray-800/50 transition-colors duration-150;
  }

  /* Images */
  .blog-content img {
    @apply my-8 rounded-lg shadow-md max-w-full h-auto mx-auto;
  }

  .blog-content figure {
    @apply my-8;
  }

  .blog-content figcaption {
    @apply mt-2 text-center text-sm text-gray-600 dark:text-gray-400 italic;
  }

  /* Horizontal Rule */
  .blog-content hr {
    @apply my-12 border-t border-gray-200 dark:border-gray-700;
  }

  /* Custom Components */
  .prose .note,
  .prose .warning,
  .prose .tip {
    @apply p-6 my-8 rounded-lg;
    @apply border-l-4;
    @apply shadow-md;
    @apply relative;
    @apply pl-12;
  }

  .prose .note::before,
  .prose .warning::before,
  .prose .tip::before {
    @apply absolute left-4 top-6;
    @apply text-xl;
  }

  .prose .note {
    @apply bg-blue-50 dark:bg-blue-900/20 border-blue-500 text-blue-800 dark:text-blue-200;
  }

  .prose .note::before {
    content: '💡';
  }

  .prose .warning {
    @apply bg-amber-50 dark:bg-amber-900/20 border-amber-500 text-amber-800 dark:text-amber-200;
  }

  .prose .warning::before {
    content: '⚠️';
  }

  .prose .tip {
    @apply bg-green-50 dark:bg-green-900/20 border-green-500 text-green-800 dark:text-green-200;
  }

  .prose .tip::before {
    content: '✅';
  }

  .prose .note p:last-child,
  .prose .warning p:last-child,
  .prose .tip p:last-child {
    @apply mb-0;
  }
}
