/* Font imports */
@import url('https://fonts.cdnfonts.com/css/general-sans?styles=135312,135310,135313,135303');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

/* Base styles */
@layer base {
  html {
    @apply scroll-smooth;
  }
  
  body {
    @apply antialiased bg-body text-text-primary font-body transition-colors duration-200;
  }
  
  .dark body {
    @apply bg-body-dark text-text-primary-dark;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-heading font-bold;
  }
  
  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl;
  }
  
  h2 {
    @apply text-3xl md:text-4xl lg:text-5xl;
  }
  
  h3 {
    @apply text-2xl md:text-3xl;
  }
  
  p {
    @apply text-text-secondary dark:text-text-secondary-dark;
  }
  
  a {
    @apply transition-colors duration-200;
  }
}

/* Common components */
@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-5 py-3 font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply btn bg-primary hover:bg-primary/90 text-white focus:ring-primary/50;
  }
  
  .btn-secondary {
    @apply btn bg-white border border-gray-300 hover:bg-gray-50 text-text-primary focus:ring-gray-300;
  }
  
  .btn-dark {
    @apply dark:bg-gray-800 dark:border-gray-700 dark:hover:bg-gray-700 dark:text-white dark:focus:ring-gray-600;
  }
  
  .card {
    @apply bg-white dark:bg-gray-800 rounded-xl border border-gray-100 dark:border-gray-700 transition-all duration-200 hover:shadow-md;
  }
  
  .container-custom {
    @apply container mx-auto px-4 md:px-8;
  }
  
  .section {
    @apply py-12 md:py-16 lg:py-24;
  }
  
  .gradient-bg {
    @apply relative overflow-hidden;
  }
  
  .gradient-bg::before {
    @apply content-[''] absolute top-0 left-0 w-full h-full opacity-50 pointer-events-none;
  }
}

/* Utilities */
@layer utilities {
  .text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-500;
  }
}
