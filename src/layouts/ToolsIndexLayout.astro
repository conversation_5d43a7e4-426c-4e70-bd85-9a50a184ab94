---
import Header from "../components/Header.astro";
import Footer from "../components/Footer.astro";
import ThemeScript from "../components/ThemeScript.astro";
import "../styles/global.css";
const { title } = Astro.props;
---

<html lang="en" class="scroll-smooth">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
        <title>{title}</title>
        <slot name="head" />
        <ThemeScript />
    </head>
    <body
        class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 min-h-screen transition-colors duration-200"
    >
        <Header />

        <main>
            <slot />
        </main>

        <Footer />
    </body>
</html>
