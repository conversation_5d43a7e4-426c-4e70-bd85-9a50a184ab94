---
// type utility for markdown layout props
import type { MarkdownLayoutProps } from "astro";
import AppLayout from "./AppLayout.astro";
import Container from "../components/ui/Container.jsx";
import Section from "../components/ui/Section.jsx";
import { formatDate } from "../utils/dateUtils";
import { SEO } from "astro-seo";
import StructuredData from "../components/StructuredData.astro";
import BlogImage from "../components/BlogImage.astro";

type Props = MarkdownLayoutProps<{
    // Define frontmatter props here
    title: string;
    image: string;
    category: string;
    author: string;
    authorImage: string;
    date: string;
    excerpt: string;
}>;

// get properties from the markdown frontmatter
const { title, image, category, author, authorImage, date, excerpt } =
    Astro.props.frontmatter;

// Get the slug from the file path
const slug = Astro.url.pathname.split("/").filter(Boolean).pop();

// Get all blog posts for related articles
const allPosts = await Astro.glob("../pages/blog/*.md");
const relatedPosts = allPosts
    .filter((post) => post.url !== Astro.url.pathname)
    .slice(0, 2);
---

<AppLayout title={`${title} | DevBottle Blog`}>
    <SEO
        slot="head"
        title={`${title} | DevBottle Blog`}
        description={excerpt}
        canonical={Astro.url.href}
        openGraph={{
            basic: {
                title: title,
                type: "article",
                image: image.startsWith("http")
                    ? image
                    : `https://devbottle.com/images/${image}`,
                url: Astro.url.href,
            },
            optional: {
                locale: "en-US",
                siteName: "DevBottle",
                description: excerpt,
            },
            article: {
                publishedTime: date,
                authors: [author],
                section: category,
            },
            image: {
                alt: title,
            },
        }}
    />
    <StructuredData
        slot="head"
        type="article"
        data={{
            title: title,
            description: excerpt,
            image: image.startsWith("http")
                ? image
                : `https://devbottle.com/images/${image}`,
            datePublished: date,
            author: author,
            url: Astro.url.href,
        }}
    />
    <Section className="bg-white dark:bg-gray-900">
        <Container>
            <div class="max-w-3xl mx-auto">
                <div class="mb-8">
                    <a
                        href="/blog"
                        class="text-primary dark:text-primary-dark hover:underline inline-flex items-center"
                    >
                        <svg
                            class="w-4 h-4 mr-1"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Blog
                    </a>
                </div>

                <div class="mb-8">
                    <p
                        class="text-sm text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2"
                    >
                        {category}
                    </p>
                    <h1
                        class="text-4xl md:text-5xl font-bold mb-4 text-gray-800 dark:text-white"
                    >
                        {title}
                    </h1>

                    <div class="flex items-center mb-8">
                        <BlogImage
                            src={authorImage}
                            alt={author}
                            class="w-10 h-10 rounded-full mr-3"
                        />
                        <div>
                            <p class="font-medium">{author}</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                {date}
                            </p>
                        </div>
                    </div>
                </div>

                <div class="mb-10">
                    <div class="mb-8">
                        <BlogImage src={image} alt={title} />
                    </div>

                    <div
                        class="prose prose-lg dark:prose-invert dark:prose-dark max-w-none blog-table-fix"
                    >
                        <style>
                            .blog-table-fix
                                :where(table)
                                :where(thead th:first-child),
                            .blog-table-fix
                                :where(table)
                                :where(tbody td:first-child) {
                                padding-left: 1.5rem !important;
                                padding-inline-start: 1.5rem !important;
                            }
                        </style>
                        <slot />
                    </div>
                </div>

                <div class="border-t border-gray-200 dark:border-gray-700 pt-8">
                    <h3
                        class="text-xl font-bold mb-4 text-gray-800 dark:text-white"
                    >
                        Related Articles
                    </h3>
                    <div class="grid md:grid-cols-2 gap-6">
                        {
                            relatedPosts.map((post) => (
                                <a href={post.url} class="block group">
                                    <h4 class="font-semibold group-hover:text-primary dark:group-hover:text-primary-dark transition-colors">
                                        {post.frontmatter.title}
                                    </h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                        {post.frontmatter.excerpt}
                                    </p>
                                </a>
                            ))
                        }
                    </div>
                </div>
            </div>
        </Container>
    </Section>
</AppLayout>
