/**
 * Tools data for the DevBottle application
 * Add new tools to this array to have them automatically appear on the tools page
 *
 * Available properties:
 * - id: Unique identifier for the tool
 * - name: Display name of the tool
 * - shortDescription: Brief description for cards and listings
 * - description: Medium-length description for meta tags
 * - fullDescription: Complete description for the tool page
 * - icon: Lucide icon name to use for the tool
 * - path: URL path to the tool
 * - component: Component name for the tool
 * - containerWidth: Control the width of the tool container ('default', 'full', 'narrow', 'wide')
 */
export const tools = [
    {
        id: "slugtool",
        name: "Slug Tool",
        shortDescription:
            "Convert any text into a clean, URL-friendly slug. Perfect for SEO, filenames, or creating readable identifiers.",
        description:
            "Convert any text into a clean, URL-friendly slug. Perfect for SEO, filenames, or creating readable identifiers for your content.",
        fullDescription:
            "Pour any text into our bottle and get a clean, URL-friendly slug. Perfect for SEO, filenames, or creating readable identifiers for your content. Options include lowercase conversion, stop word removal, and custom separators.",
        icon: "Link2",
        path: "/tools/slugtool",
        component: "SlugTool",
        containerWidth: "default",
    },
    {
        id: "markupcleaner",
        name: "Markup Cleaner",
        shortDescription:
            "Clean up messy HTML for your CMS or blog editor. Removes unwanted tags, inline styles, and formatting.",
        description:
            "Clean up messy HTML for your CMS or blog editor. Removes unwanted tags, inline styles, and formatting while preserving the essential structure.",
        fullDescription:
            "Paste any messy HTML below. We'll automatically strip out unnecessary tags, inline styles, classes, IDs, and any embedded stylesheets — leaving you with clean, readable markup that's safe to paste directly into your CMS, blog editor, or project.",
        icon: "FileCode",
        path: "/tools/markupcleaner",
        component: "MarkupCleanerTool",
        containerWidth: "narrow",
    },
    {
        id: "shadowgenerator",
        name: "CSS Shadow Generator",
        shortDescription:
            "Create beautiful CSS shadows with a visual editor. Includes box-shadow and filter shadow options.",
        description:
            "Visual box-shadow and CSS filter shadow builder with real-time preview and code generation for your projects.",
        fullDescription:
            "Create beautiful CSS shadows with our visual editor. Adjust X/Y offset, blur, spread, and color with intuitive controls. Choose from preset styles like Material Design, iOS, and Neumorphism, or create your own custom shadows. Preview your shadows in real-time and copy the generated code as CSS, JavaScript, or Tailwind classes.",
        icon: "Square",
        path: "/tools/shadowgenerator",
        component: "ShadowGeneratorTool",
        containerWidth: "default",
    },
    {
        id: "fontscale",
        name: "Font Scale Calculator",
        shortDescription:
            "Create harmonious type scales for your designs with popular ratios like the golden ratio.",
        description:
            "Typography ratio calculator with visual hierarchy preview. Generate a consistent type scale for your web projects.",
        fullDescription:
            "Create harmonious typography with our font scale calculator. Choose from popular ratio presets like the golden ratio, perfect fourth, or create your own custom scale. Adjust the base size, preview the visual hierarchy, and generate ready-to-use CSS custom properties. Includes WCAG contrast checking to ensure your typography is accessible.",
        icon: "Type",
        path: "/tools/fontscale",
        component: "FontScaleCalculator",
        containerWidth: "default",
    },
    {
        id: "contrastchecker",
        name: "Color Contrast Checker",
        shortDescription:
            "Check color contrast ratios for WCAG compliance and get accessible color suggestions.",
        description:
            "Ensure your color combinations meet accessibility standards with our contrast checker tool.",
        fullDescription:
            "Check color contrast ratios for WCAG compliance and get accessible color suggestions for your web designs. Ensure your website meets accessibility standards with real-time contrast ratio calculation and visual preview.",
        icon: "Contrast",
        path: "/tools/contrastchecker",
        component: "ContrastChecker",
        containerWidth: "default",
    },
    {
        id: "criticalcss",
        name: "Critical CSS Extractor",
        shortDescription:
            "Extract critical CSS from your HTML and CSS to improve page load times.",
        description:
            "Identify and separate the CSS needed for above-the-fold content to optimize page loading.",
        fullDescription:
            "Extract critical CSS from your HTML and CSS to improve page load times and performance. This tool helps you identify and separate the CSS needed for above-the-fold content, reducing render-blocking resources and improving Core Web Vitals.",
        icon: "Scissors",
        path: "/tools/criticalcss",
        component: "CriticalCssExtractor",
        containerWidth: "full",
    },
    {
        id: "svgoptimizer",
        name: "SVG Optimizer",
        shortDescription:
            "Clean and optimize SVG files for better web performance.",
        description:
            "Optimize SVG files by cleaning viewbox, stripping metadata, and reducing file size.",
        fullDescription:
            "Optimize SVG files by cleaning viewbox, stripping metadata, and reducing file size for web use. This tool helps you prepare SVG graphics for production websites with improved performance.",
        icon: "FileImage",
        path: "/tools/svgoptimizer",
        component: "SvgOptimizer",
        containerWidth: "default",
    },
    {
        id: "devtextgenerator",
        name: "DevText Generator",
        shortDescription:
            "Generate developer-focused placeholder text for mockups, documentation, and testing.",
        description:
            "Create technical Lorem Ipsum text with programming terms, code snippets, and developer jargon.",
        fullDescription:
            "Generate developer-focused placeholder text for your mockups, documentation, and testing. Unlike traditional Lorem Ipsum, DevText Generator creates content with programming terminology, variable names, and technical jargon that looks like real developer documentation or comments.",
        icon: "FileText",
        path: "/tools/devtextgenerator",
        component: "DevTextGenerator",
        containerWidth: "default",
    },
    {
        id: "apitester",
        name: "API Tester",
        shortDescription:
            "Test and debug API endpoints with a simple, intuitive interface.",
        description:
            "Make HTTP requests to any API endpoint and inspect responses with formatted output.",
        fullDescription:
            "Test and debug API endpoints with our intuitive interface. Send GET, POST, PUT, DELETE and other HTTP requests with custom headers and request bodies. View formatted JSON or XML responses, check status codes, and measure response times. Perfect for developers testing RESTful APIs or webhooks during development.",
        icon: "ServerCog",
        path: "/tools/apitester",
        component: "ApiTester",
        containerWidth: "full",
    },
    {
        id: "colorpalette",
        name: "Color Palette Generator",
        shortDescription:
            "Create harmonious color palettes for your web projects with various color schemes.",
        description:
            "Generate beautiful color palettes using different color harmony rules like monochromatic, analogous, and complementary.",
        fullDescription:
            "Create harmonious color palettes for your web projects with our intuitive color palette generator. Choose from different color schemes including monochromatic, analogous, complementary, triadic, and tetradic. Start with a base color and let our tool generate a balanced palette that works perfectly together. Export your palette as CSS variables, Tailwind config, or copy individual color codes.",
        icon: "Palette",
        path: "/tools/colorpalette",
        component: "ColorPaletteGenerator",
        containerWidth: "full",
    },
    {
        id: "metatagpreview",
        name: "Meta Tag Preview",
        shortDescription:
            "Preview how your website appears when shared on social media platforms.",
        description:
            "Visualize and analyze meta tags, Open Graph tags, and Twitter Card tags from any URL.",
        fullDescription:
            "See how your website appears when shared on social media platforms like Facebook, Twitter, and LinkedIn. This tool fetches and displays meta tags, Open Graph tags, Twitter Card tags, and other relevant metadata from any URL. Perfect for content creators, marketers, and developers who want to optimize their social media presence.",
        icon: "Share2",
        path: "/tools/metatagpreview",
        component: "MetaTagPreview",
        containerWidth: "full",
    },
    {
        id: "schemagenerator",
        name: "Schema Generator",
        shortDescription:
            "Create structured data markup for better SEO and rich results in search engines.",
        description:
            "Generate JSON-LD schema markup for your website to improve search engine visibility and enable rich results.",
        fullDescription:
            "Create structured data markup that helps search engines understand your content better. This tool generates JSON-LD code for common schema types like Organization, Person, Local Business, Product, Article, Event, Recipe, and FAQ. Properly implemented schema markup can enhance your search engine listings with rich results like star ratings, images, and additional information, potentially improving click-through rates and visibility.",
        icon: "Code2",
        path: "/tools/schemagenerator",
        component: "SchemaGenerator",
        containerWidth: "default",
    },
    {
        id: "regextester",
        name: "RegEx Tester",
        shortDescription:
            "Test and debug regular expressions with real-time matching and highlighting.",
        description:
            "Interactive regular expression tester with match highlighting, group extraction, and common pattern examples.",
        fullDescription:
            "Test and debug regular expressions with our interactive tool. See matches highlighted in real-time, extract capture groups, and understand exactly how your patterns work. Includes support for all regex flags (global, case-insensitive, multiline, etc.) and a library of common patterns for emails, URLs, phone numbers, and more. Perfect for developers working with form validation, text parsing, or data extraction.",
        icon: "FileSearch",
        path: "/tools/regextester",
        component: "RegExTester",
        containerWidth: "default",
    },
    {
        id: "gradientgenerator",
        name: "Gradient Generator",
        shortDescription:
            "Create beautiful CSS gradients with animation capabilities for your web projects.",
        description:
            "Design linear and radial gradients with a visual editor, add animations, and export ready-to-use CSS code.",
        fullDescription:
            "Create stunning CSS gradients for your web projects with our visual gradient generator. Design linear, radial, and conic gradients with multiple color stops, customize angles and positions, and add smooth animations. Preview your gradients in real-time and export the code as CSS, Tailwind, or React styles. Perfect for designers and developers looking to add visual interest to their websites and applications.",
        icon: "Paintbrush",
        path: "/tools/gradientgenerator",
        component: "GradientGenerator",
        containerWidth: "full",
    },
    {
        id: "flexboxgrid",
        name: "Flexbox & Grid Generator",
        shortDescription:
            "Create and customize flexbox and CSS grid layouts with an interactive visual editor.",
        description:
            "Design responsive layouts using flexbox and CSS grid with a visual editor that generates clean CSS code.",
        fullDescription:
            "Create and customize flexbox and CSS grid layouts with an interactive visual editor. Adjust container properties like flex-direction, justify-content, and align-items for flexbox, or grid-template-columns and grid-template-rows for grid layouts. Customize individual item properties, add or remove items, and see real-time previews of your layout. Choose from preset layouts or create your own from scratch. Perfect for learning CSS layout techniques or quickly generating code for your projects.",
        icon: "Layout",
        path: "/tools/flexboxgrid",
        component: "FlexboxGridGenerator",
        containerWidth: "full",
    },
    {
        id: "bordergenerator",
        name: "Border Generator",
        shortDescription:
            "Create custom CSS borders with different shapes like zig-zag, scooped, scalloped, or wavy.",
        description:
            "Design unique border styles using CSS masks with an interactive visual editor that generates clean CSS code.",
        fullDescription:
            "Create custom CSS borders with different shapes like zig-zag, scooped, scalloped, or wavy using CSS masks. Customize which sides of your element have borders, adjust the size, angle, and other properties to create unique designs. Preview your borders in real-time and get the CSS code ready to use in your projects. Perfect for adding visual interest to your web designs without using images.",
        icon: "Frame",
        path: "/tools/bordergenerator",
        component: "ExactBorderGenerator",
        containerWidth: "default",
    },
    // Add new tools here
];

/**
 * Get a tool by its ID
 * @param {string} id - The tool ID to find
 * @returns {object|undefined} The tool object or undefined if not found
 */
export function getToolById(id) {
    return tools.find((tool) => tool.id === id);
}

/**
 * Get all available tools
 * @returns {array} Array of all tool objects
 */
export function getAllTools() {
    return tools;
}
