// @ts-check
import { defineConfig } from 'astro/config';
import react from '@astrojs/react';
import tailwind from '@astrojs/tailwind';
import sitemap from '@astrojs/sitemap';
import { defineConfig as defineViteConfig } from 'vite';

// https://astro.build/config
export default defineConfig({
  output: 'static',
  integrations: [
    react(),
    tailwind(),
    sitemap({
      filter: (page) => !page.includes('/thanks/'),
      lastmod: new Date(),
      entryLimit: 10000 // Set a high limit to ensure all pages are in one file
    }),
  ],
  site: 'https://devbottle.com',
  image: {
    // Enable image optimization
    service: { entrypoint: 'astro/assets/services/sharp' },
    // Set reasonable defaults for image optimization
    remotePatterns: [{ protocol: 'https' }],
  },
  vite: defineViteConfig({
    build: {
      // Increase the size limit for optimized images
      assetsInlineLimit: 0,
    },
  }),
});