#!/bin/bash

# This script updates the SEO content containers in all tool pages to match the tool container width

# Find all tool pages (excluding index.astro)
TOOL_PAGES=$(find src/pages/tools -name "*.astro" -not -name "index.astro")

# Loop through each tool page
for page in $TOOL_PAGES; do
  echo "Updating $page..."
  
  # Replace the container div with one that uses the toolData containerWidth
  sed -i '' 's/<div class="container mx-auto px-4 py-12 max-w-4xl">/<div class="container mx-auto px-4 py-12 w-full lg:w-8\/12 lg:mx-auto" style="width: var(--tool-container-width, auto);">/g' "$page"
  
  # Add script to set the container width based on the tool container
  sed -i '' '/<\/div>\n<\/ToolLayout>/i\
    <script>\
        document.addEventListener("DOMContentLoaded", function() {\
            const toolContainer = document.querySelector(".tool-container");\
            if (toolContainer) {\
                const computedStyle = window.getComputedStyle(toolContainer);\
                const width = computedStyle.width;\
                document.documentElement.style.setProperty("--tool-container-width", width);\
            }\
        });\
    </script>' "$page"
done

echo "All tool pages updated successfully!"
